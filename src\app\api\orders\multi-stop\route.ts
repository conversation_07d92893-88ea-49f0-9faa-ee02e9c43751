import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import {
  secureAPI,
  sanitizeInput,
  isValidUUID,
} from '@/lib/security/api-security';
import { auditLog } from '@/lib/security/audit-logger';

// Define interfaces for the request body that match the database schema
interface Address {
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
}

interface PackageDetails {
  weight: number;
  dimensions: string;
  description: string;
  fragile: boolean;
}

interface FiscalData extends Record<string, unknown> {
  rfc?: string;
  business_name?: string;
  tax_regime?: string;
}

interface PaymentMethodDetails extends Record<string, unknown> {
  card_last_four?: string;
  digital_wallet_provider?: string;
  bank_account_last_four?: string;
}

interface Stop extends Record<string, unknown> {
  id: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  scheduled_time?: string;
  instructions?: string;
}

interface RouteOptimization extends Record<string, unknown> {
  mode: string;
  preferences: Record<string, unknown>;
}

// POST /api/orders/multi-stop - Create a multi-stop order
export const POST = secureAPI(
  {
    POST: async (request, context) => {
      try {
        // Parse and sanitize request body
        let requestBody;
        try {
          requestBody = sanitizeInput(await request.json());
        } catch {
          return NextResponse.json(
            { error: 'Formato de cuerpo de solicitud inválido' },
            { status: 400 }
          );
        }

        const {
          customer_id,
          pickup_address,
          delivery_addresses,
          package_details,
          vehicle_type_id,
          cargo_type_id,
          delivery_mode,
          scheduled_pickup_time,
          scheduled_delivery_time,
          delivery_instructions,
          special_handling_notes,
          fiscal_data,
          payment_method_details,
          stops,
          route_optimization,
        } = requestBody as {
          customer_id?: string;
          pickup_address?: Address;
          delivery_addresses?: Address[];
          package_details?: PackageDetails;
          vehicle_type_id?: string;
          cargo_type_id?: string;
          delivery_mode?: string;
          scheduled_pickup_time?: string;
          scheduled_delivery_time?: string;
          delivery_instructions?: string;
          special_handling_notes?: string;
          fiscal_data?: FiscalData;
          payment_method_details?: PaymentMethodDetails;
          stops?: Stop[];
          route_optimization?: RouteOptimization;
        };

        // Validate required fields
        if (
          !customer_id ||
          !pickup_address ||
          !delivery_addresses ||
          !Array.isArray(delivery_addresses)
        ) {
          return NextResponse.json(
            {
              error:
                'Los campos customer_id, pickup_address y delivery_addresses son requeridos',
            },
            { status: 400 }
          );
        }

        // Validate UUID format for customer_id
        if (!isValidUUID(customer_id)) {
          return NextResponse.json(
            { error: 'ID de cliente inválido' },
            { status: 400 }
          );
        }

        // Validate UUID fields if provided
        if (vehicle_type_id && !isValidUUID(vehicle_type_id)) {
          return NextResponse.json(
            { error: 'ID de tipo de vehículo inválido' },
            { status: 400 }
          );
        }

        if (cargo_type_id && !isValidUUID(cargo_type_id)) {
          return NextResponse.json(
            { error: 'ID de tipo de carga inválido' },
            { status: 400 }
          );
        }

        // Validate that we have at least one delivery address
        if (delivery_addresses.length === 0) {
          return NextResponse.json(
            { error: 'Debe proporcionar al menos una dirección de entrega' },
            { status: 400 }
          );
        }

        // Validate stops if provided
        if (stops && !Array.isArray(stops)) {
          return NextResponse.json(
            { error: 'El campo stops debe ser un arreglo' },
            { status: 400 }
          );
        }

        const supabase = await createClient();

        // Check if customer exists
        const { data: customer, error: customerError } = await supabase
          .from('profiles')
          .select('id')
          .eq('id', customer_id)
          .single();

        if (customerError) {
          console.error('Database error:', customerError);
          await auditLog({
            event: 'MULTI_STOP_ORDER_CUSTOMER_CHECK_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { error: customerError.message, customerId: customer_id },
            severity: 'MEDIUM',
            category: 'DATA',
          });

          return NextResponse.json(
            { error: 'Error al verificar el cliente' },
            { status: 500 }
          );
        }

        if (!customer) {
          return NextResponse.json(
            { error: 'Cliente no encontrado' },
            { status: 404 }
          );
        }

        // Create the multi-stop order
        const orderData = {
          customer_id,
          pickup_address,
          delivery_addresses,
          package_details: package_details || null,
          vehicle_type_id: vehicle_type_id || null,
          cargo_type_id: cargo_type_id || null,
          delivery_mode: delivery_mode || null,
          scheduled_pickup_time: scheduled_pickup_time || null,
          scheduled_delivery_time: scheduled_delivery_time || null,
          delivery_instructions: delivery_instructions || null,
          special_handling_notes: special_handling_notes || null,
          fiscal_data: fiscal_data || null,
          payment_method_details: payment_method_details || null,
          stops: stops || null,
          route_optimization: route_optimization || null,
          status: 'pending',
          payment_status: 'pending',
          payment_method: 'stripe',
        };

        const { data, error } = await supabase
          .from('orders')
          .insert([orderData])
          .select()
          .single();

        if (error) {
          console.error('Database error:', error);
          await auditLog({
            event: 'MULTI_STOP_ORDER_CREATE_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { error: error.message, requestData: requestBody },
            severity: 'MEDIUM',
            category: 'DATA',
          });
          return NextResponse.json(
            { error: 'Error al crear la orden multi-entrega' },
            { status: 500 }
          );
        }

        await auditLog({
          event: 'MULTI_STOP_ORDER_CREATED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            orderId: data.id,
            customerId: customer_id,
            stopCount: delivery_addresses.length,
          },
          severity: 'MEDIUM',
          category: 'DATA',
        });

        return NextResponse.json(
          {
            success: true,
            message: 'Orden multi-entrega creada exitosamente',
            data,
          },
          { status: 201 }
        );
      } catch (error) {
        await auditLog({
          event: 'MULTI_STOP_ORDER_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: true,
    allowedRoles: ['admin', 'customer'],
    requireMFA: false,
    validateCSRF: true,
    rateLimitRpm: 30,
  }
);

// PUT /api/orders/multi-stop/[id] - Update stops for a multi-stop order
export const PUT = secureAPI(
  {
    PUT: async (request, context) => {
      try {
        // Extract order ID from URL parameters
        const url = new URL(request.url);
        const orderId = url.pathname.split('/').slice(-2)[0];

        // Validate UUID format
        if (!orderId || !isValidUUID(orderId)) {
          return NextResponse.json(
            { error: 'ID de orden inválido' },
            { status: 400 }
          );
        }

        // Parse and sanitize request body
        let requestBody;
        try {
          requestBody = sanitizeInput(await request.json());
        } catch {
          return NextResponse.json(
            { error: 'Formato de cuerpo de solicitud inválido' },
            { status: 400 }
          );
        }

        const { stops, route_optimization } = requestBody as {
          stops?: Stop[];
          route_optimization?: RouteOptimization;
        };

        // Validate stops if provided
        if (stops && !Array.isArray(stops)) {
          return NextResponse.json(
            { error: 'El campo stops debe ser un arreglo' },
            { status: 400 }
          );
        }

        const supabase = await createClient();

        // Check if order exists and user is authorized
        const { data: order, error: orderError } = await supabase
          .from('orders')
          .select('customer_id, delivery_id')
          .eq('id', orderId)
          .single();

        if (orderError) {
          console.error('Database error:', orderError);
          await auditLog({
            event: 'MULTI_STOP_ORDER_UPDATE_CHECK_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { error: orderError.message, orderId },
            severity: 'MEDIUM',
            category: 'DATA',
          });

          if (orderError.code === 'PGRST116') {
            return NextResponse.json(
              { error: 'Orden no encontrada' },
              { status: 404 }
            );
          }

          return NextResponse.json(
            { error: 'Error al verificar la orden' },
            { status: 500 }
          );
        }

        if (!order) {
          return NextResponse.json(
            { error: 'Orden no encontrada' },
            { status: 404 }
          );
        }

        // Authorization check
        const userRole = context.user?.role;
        const userId = context.user?.id;

        if (userRole === 'customer' && order.customer_id !== userId) {
          await auditLog({
            event: 'UNAUTHORIZED_MULTI_STOP_ORDER_UPDATE_ATTEMPT',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { orderId, userRole, userId },
            severity: 'HIGH',
            category: 'SECURITY',
          });
          return NextResponse.json(
            { error: 'No tienes permiso para actualizar esta orden' },
            { status: 403 }
          );
        }

        if (userRole === 'delivery' && order.delivery_id !== userId) {
          await auditLog({
            event: 'UNAUTHORIZED_MULTI_STOP_ORDER_UPDATE_ATTEMPT',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { orderId, userRole, userId },
            severity: 'HIGH',
            category: 'SECURITY',
          });
          return NextResponse.json(
            { error: 'Solo puedes actualizar órdenes asignadas a ti' },
            { status: 403 }
          );
        }

        // Prepare update data
        const updateData: Record<string, unknown> = {
          updated_at: new Date().toISOString(),
        };

        if (stops !== undefined) updateData.stops = stops;
        if (route_optimization !== undefined)
          updateData.route_optimization = route_optimization;

        // Update order
        const { data, error } = await supabase
          .from('orders')
          .update(updateData)
          .eq('id', orderId)
          .select()
          .single();

        if (error) {
          console.error('Database error:', error);
          await auditLog({
            event: 'MULTI_STOP_ORDER_UPDATE_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { error: error.message, orderId, updateData },
            severity: 'MEDIUM',
            category: 'DATA',
          });
          return NextResponse.json(
            { error: 'Error al actualizar la orden multi-entrega' },
            { status: 500 }
          );
        }

        if (!data) {
          return NextResponse.json(
            { error: 'Orden no encontrada' },
            { status: 404 }
          );
        }

        await auditLog({
          event: 'MULTI_STOP_ORDER_UPDATED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { orderId, updatedFields: Object.keys(updateData) },
          severity: 'MEDIUM',
          category: 'DATA',
        });

        return NextResponse.json({
          success: true,
          message: 'Orden multi-entrega actualizada exitosamente',
          data,
        });
      } catch (error) {
        await auditLog({
          event: 'MULTI_STOP_ORDER_UPDATE_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: true,
    allowedRoles: ['admin', 'customer', 'delivery'],
    requireMFA: false,
    validateCSRF: true,
    rateLimitRpm: 30,
  }
);
