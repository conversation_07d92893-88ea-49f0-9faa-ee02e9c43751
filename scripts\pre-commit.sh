#!/bin/bash

# Pre-commit hook script
# This script runs code quality checks before committing

echo "🔍 Running pre-commit checks..."

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm is not installed. Please install pnpm first."
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    pnpm install
fi

# Run Prettier check
echo "🎨 Checking code formatting with Prettier..."
if ! pnpm prettier:check; then
    echo "❌ Code formatting issues found. Run 'pnpm prettier:write' to fix them."
    exit 1
fi

# Run ESLint
echo "🔧 Running ESLint..."
if ! pnpm lint; then
    echo "❌ ESLint issues found. Run 'pnpm lint:fix' to fix them."
    exit 1
fi

# Run TypeScript type check
echo "📝 Running TypeScript type check..."
if ! pnpm type-check; then
    echo "❌ TypeScript type errors found. Please fix them before committing."
    exit 1
fi

echo "✅ All pre-commit checks passed!"
exit 0
