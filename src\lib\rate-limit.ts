/**
 * Rate limiting utility for API endpoints
 * In production, consider using Redis or a database for distributed rate limiting
 */

export interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  keyGenerator?: (identifier: string) => string;
}

export interface RateLimitResult {
  allowed: boolean;
  resetTime?: number;
  remaining?: number;
}

class RateLimiter {
  private store = new Map<string, { count: number; resetTime: number }>();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(
      () => {
        this.cleanup();
      },
      5 * 60 * 1000
    );
  }

  check(identifier: string, config: RateLimitConfig): RateLimitResult {
    const now = Date.now();
    const key = config.keyGenerator
      ? config.keyGenerator(identifier)
      : identifier;
    const record = this.store.get(key);

    if (!record || now > record.resetTime) {
      // First request or window expired
      this.store.set(key, { count: 1, resetTime: now + config.windowMs });
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: now + config.windowMs,
      };
    }

    if (record.count >= config.maxRequests) {
      // Rate limit exceeded
      return {
        allowed: false,
        resetTime: record.resetTime,
        remaining: 0,
      };
    }

    // Increment count
    record.count++;
    this.store.set(key, record);

    return {
      allowed: true,
      remaining: config.maxRequests - record.count,
      resetTime: record.resetTime,
    };
  }

  private cleanup() {
    const now = Date.now();
    for (const [key, record] of this.store.entries()) {
      if (now > record.resetTime) {
        this.store.delete(key);
      }
    }
  }

  // Method to manually clear rate limit for testing or admin purposes
  clear(identifier?: string) {
    if (identifier) {
      this.store.delete(identifier);
    } else {
      this.store.clear();
    }
  }

  // Get current status without incrementing
  status(identifier: string, config: RateLimitConfig): RateLimitResult {
    const now = Date.now();
    const key = config.keyGenerator
      ? config.keyGenerator(identifier)
      : identifier;
    const record = this.store.get(key);

    if (!record || now > record.resetTime) {
      return {
        allowed: true,
        remaining: config.maxRequests,
        resetTime: now + config.windowMs,
      };
    }

    return {
      allowed: record.count < config.maxRequests,
      remaining: Math.max(0, config.maxRequests - record.count),
      resetTime: record.resetTime,
    };
  }

  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.store.clear();
  }
}

// Global rate limiter instance
export const ratelimit = new RateLimiter();

// Predefined rate limit configurations
export const rateLimitConfigs = {
  // Password reset: 3 requests per 15 minutes
  passwordReset: {
    maxRequests: 3,
    windowMs: 15 * 60 * 1000, // 15 minutes
  },

  // Login attempts: 5 requests per 5 minutes
  login: {
    maxRequests: 5,
    windowMs: 5 * 60 * 1000, // 5 minutes
  },

  // Registration: 2 requests per 10 minutes
  registration: {
    maxRequests: 2,
    windowMs: 10 * 60 * 1000, // 10 minutes
  },

  // Global API: 100 requests per minute
  globalApi: {
    maxRequests: 100,
    windowMs: 60 * 1000, // 1 minute
  },

  // Sensitive operations: 1 request per 5 minutes
  sensitive: {
    maxRequests: 1,
    windowMs: 5 * 60 * 1000, // 5 minutes
  },
};

// Helper function to get client IP from request headers
export function getClientIP(request: Request): string {
  // Try to get real IP from various headers (works with most reverse proxies)
  const headers = request.headers;

  const forwarded = headers.get('x-forwarded-for');
  const realIP = headers.get('x-real-ip');
  const cfConnectingIP = headers.get('cf-connecting-ip');

  if (forwarded) {
    // x-forwarded-for can contain multiple IPs, take the first one
    return forwarded.split(',')[0].trim();
  }
  if (realIP) {
    return realIP;
  }
  if (cfConnectingIP) {
    return cfConnectingIP;
  }

  // Fallback - this might not work in all deployment environments
  return 'unknown';
}

// Helper function to create rate limit key generators
export const keyGenerators = {
  ip: (ip: string) => `ip:${ip}`,
  email: (email: string) => `email:${email.toLowerCase()}`,
  user: (userId: string) => `user:${userId}`,
  global: () => 'global',
  combined: (ip: string, email: string) =>
    `combined:${ip}:${email.toLowerCase()}`,
};

// Utility function to format rate limit error messages
export function formatRateLimitError(
  resetTime: number,
  action: string = 'this action'
): string {
  const now = Date.now();
  const remainingMs = resetTime - now;

  if (remainingMs <= 0) {
    return `Rate limit exceeded for ${action}. Please try again.`;
  }

  const remainingMinutes = Math.ceil(remainingMs / 60000);
  const remainingSeconds = Math.ceil(remainingMs / 1000);

  if (remainingMinutes > 1) {
    return `Too many attempts for ${action}. Please try again in ${remainingMinutes} minutes.`;
  } else if (remainingSeconds > 60) {
    return `Too many attempts for ${action}. Please try again in 1 minute.`;
  } else {
    return `Too many attempts for ${action}. Please try again in ${remainingSeconds} seconds.`;
  }
}
