/**
 * Order Status Configuration
 *
 * This file centralizes all order status definitions to ensure consistency
 * across the database, API, and frontend components.
 */

// Valid order status values - MUST match database constraint
export const ORDER_STATUSES = [
  'pending',
  'confirmed',
  'in-transit',
  'pending-admin-confirmation',
  'delivered',
  'closed',
  'cancelled',
] as const;

export type OrderStatus = (typeof ORDER_STATUSES)[number];

// Status display configuration for UI
export const ORDER_STATUS_CONFIG = {
  pending: {
    label: 'Pendiente',
    color: 'bg-yellow-100 text-yellow-800',
    icon: '⏳',
    description: 'Orden creada, esperando confirmación',
  },
  confirmed: {
    label: 'Confirmado',
    color: 'bg-blue-100 text-blue-800',
    icon: '✅',
    description: 'Orden confirmada por administración',
  },
  'in-transit': {
    label: 'En Tránsito',
    color: 'bg-purple-100 text-purple-800',
    icon: '🚚',
    description: 'Orden en camino al destino',
  },
  'pending-admin-confirmation': {
    label: 'Pendiente Confirmación Admin',
    color: 'bg-orange-100 text-orange-800',
    icon: '👨‍💼',
    description: 'Esperando confirmación de administrador',
  },
  delivered: {
    label: 'Entregado',
    color: 'bg-green-100 text-green-800',
    icon: '📦',
    description: 'Orden entregada al cliente',
  },
  closed: {
    label: 'Cerrado',
    color: 'bg-gray-100 text-gray-800',
    icon: '🔒',
    description: 'Orden completada y cerrada',
  },
  cancelled: {
    label: 'Cancelado',
    color: 'bg-red-100 text-red-800',
    icon: '❌',
    description: 'Orden cancelada',
  },
} as const;

// Status transitions - defines which status changes are allowed
export const STATUS_TRANSITIONS: Record<OrderStatus, OrderStatus[]> = {
  pending: ['confirmed', 'cancelled'],
  confirmed: ['in-transit', 'cancelled'],
  'in-transit': ['pending-admin-confirmation', 'delivered', 'cancelled'],
  'pending-admin-confirmation': ['delivered', 'closed', 'cancelled'],
  delivered: ['closed'],
  closed: [], // Final state
  cancelled: [], // Final state
};

// Role-based status permissions
export const STATUS_PERMISSIONS = {
  admin: {
    canUpdate: ORDER_STATUSES,
    canClose: true,
    canCancel: true,
  },
  delivery: {
    canUpdate: ['in-transit', 'pending-admin-confirmation'] as OrderStatus[],
    canClose: false,
    canCancel: false,
  },
  customer: {
    canUpdate: [] as OrderStatus[],
    canClose: false,
    canCancel: true, // Only their own orders and only if pending
  },
} as const;

// Utility functions
export function isValidStatus(status: string): status is OrderStatus {
  return ORDER_STATUSES.includes(status as OrderStatus);
}

export function getStatusConfig(status: OrderStatus) {
  return ORDER_STATUS_CONFIG[status];
}

export function canTransitionTo(
  fromStatus: OrderStatus,
  toStatus: OrderStatus
): boolean {
  return STATUS_TRANSITIONS[fromStatus].includes(toStatus);
}

export function getValidTransitions(currentStatus: OrderStatus): OrderStatus[] {
  return STATUS_TRANSITIONS[currentStatus];
}

export function canUserUpdateStatus(
  userRole: 'admin' | 'delivery' | 'customer',
  status: OrderStatus
): boolean {
  return STATUS_PERMISSIONS[userRole].canUpdate.includes(status);
}

export function getStatusOptions(
  userRole: 'admin' | 'delivery' | 'customer'
): OrderStatus[] {
  return [...STATUS_PERMISSIONS[userRole].canUpdate];
}

// Database constraint SQL for reference
export const DATABASE_CONSTRAINT_SQL = `
ALTER TABLE public.orders ADD CONSTRAINT orders_status_check 
CHECK (status IN (${ORDER_STATUSES.map(s => `'${s}'`).join(', ')}));
`;
