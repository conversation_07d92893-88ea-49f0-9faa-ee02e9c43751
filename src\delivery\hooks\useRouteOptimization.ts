import { useCallback } from 'react';

// 'use client' directive for Next.js Client Components
// This hook uses client-side React features and must run on the client

export interface Location {
  id: string;
  name: string;
  lat: number;
  lng: number;
  deliveryWindowStart?: string; // Format: "HH:MM"
  deliveryWindowEnd?: string; // Format: "HH:MM"
  packageWeight?: number; // in kg
  packageVolume?: number; // in cubic meters
}

export interface RouteSegment {
  from: Location;
  to: Location;
  distance: number; // in km
  duration: number; // in minutes
  trafficFactor: number;
}

export interface OptimizedRoute {
  locations: Location[];
  totalDistance: number; // in km
  totalDuration: number; // in minutes
  segments: RouteSegment[];
}

export interface TrafficInsights {
  peakHours: string[];
  cityFactors: Record<string, number>;
  roadTypeFactors: Record<string, number>;
  timeFactors: Record<string, number>;
}

export interface Stop {
  id?: string;
  address?: string;
  lat?: number;
  lng?: number;
  scheduled_time?: string;
  name?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  location?: string;
  estimatedServiceTime?: number;
}

// Mexican traffic patterns data
const TRAFFIC_PATTERNS: TrafficInsights = {
  peakHours: ['07:00-09:00', '17:00-19:00'],
  cityFactors: {
    'Ciudad de México': 1.8,
    Guadalajara: 1.6,
    Monterrey: 1.5,
    Puebla: 1.4,
    Tijuana: 1.7,
    default: 1.3,
  },
  roadTypeFactors: {
    highway: 1.0,
    primary: 1.3,
    secondary: 1.6,
    urban: 2.0,
  },
  timeFactors: {
    '06:00-08:00': 1.8,
    '08:00-10:00': 2.0,
    '10:00-12:00': 1.3,
    '12:00-14:00': 1.4,
    '14:00-16:00': 1.2,
    '16:00-18:00': 1.9,
    '18:00-20:00': 2.2,
    '20:00-22:00': 1.5,
    default: 1.0,
  },
};

export function useRouteOptimization() {
  // Calculate distance between two points using Haversine formula
  const calculateDistance = useCallback(
    (lat1: number, lng1: number, lat2: number, lng2: number): number => {
      const R = 6371; // Earth radius in km
      const dLat = ((lat2 - lat1) * Math.PI) / 180;
      const dLng = ((lng2 - lng1) * Math.PI) / 180;
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos((lat1 * Math.PI) / 180) *
          Math.cos((lat2 * Math.PI) / 180) *
          Math.sin(dLng / 2) *
          Math.sin(dLng / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c;
    },
    []
  );

  // Estimate duration based on distance and traffic factors
  const estimateDuration = useCallback(
    (
      distance: number,
      trafficFactor: number,
      roadType: keyof typeof TRAFFIC_PATTERNS.roadTypeFactors = 'urban'
    ): number => {
      // Base speed: 30 km/h in urban areas, 80 km/h on highways
      const baseSpeed = roadType === 'highway' ? 80 : 30;
      const baseDuration = (distance / baseSpeed) * 60; // Convert to minutes
      return baseDuration * trafficFactor;
    },
    []
  );

  // Get traffic factor based on city and time
  const getTrafficFactor = useCallback(
    (cityName: string, timeOfDay: string): number => {
      // Get city factor
      const cityFactor =
        TRAFFIC_PATTERNS.cityFactors[cityName] ||
        TRAFFIC_PATTERNS.cityFactors['default'] ||
        1.0;

      // Get time factor
      let timeFactor = 1.0;
      for (const [timeRange, factor] of Object.entries(
        TRAFFIC_PATTERNS.timeFactors
      )) {
        if (timeRange === 'default') continue;

        const [start, end] = timeRange.split('-');
        if (timeOfDay >= start && timeOfDay <= end) {
          timeFactor = factor;
          break;
        }
      }

      if (timeFactor === 1.0) {
        timeFactor = TRAFFIC_PATTERNS.timeFactors['default'] || 1.0;
      }

      return cityFactor * timeFactor;
    },
    []
  );

  // Convert stops to locations (helper function)
  const convertStopsToLocations = useCallback((stops: Stop[]): Location[] => {
    return stops.map((stop, index) => ({
      id: stop.id || `stop-${index}`,
      name: stop.address || `Stop ${index + 1}`,
      lat: stop.lat || 0,
      lng: stop.lng || 0,
      deliveryWindowStart: stop.scheduled_time
        ? stop.scheduled_time.split('T')[1]?.substring(0, 5)
        : undefined,
    }));
  }, []);

  // Enhanced TSP algorithm with traffic considerations
  const optimizeRoute = useCallback(
    (locations: Location[]): Location[] => {
      if (locations.length <= 2) return locations;

      // For demonstration, we'll implement a simple nearest neighbor algorithm
      // In a production environment, this would use more sophisticated algorithms
      const optimized: Location[] = [...locations];

      // Simple approach: start from the first location (usually warehouse/depot)
      const route: Location[] = [optimized.shift() as Location];
      const remaining = [...optimized];

      // Nearest neighbor algorithm
      while (remaining.length > 0) {
        const current = route[route.length - 1];
        let nearestIndex = 0;
        let minDistance = calculateDistance(
          current.lat,
          current.lng,
          remaining[0].lat,
          remaining[0].lng
        );

        // Find the nearest location
        for (let i = 1; i < remaining.length; i++) {
          const distance = calculateDistance(
            current.lat,
            current.lng,
            remaining[i].lat,
            remaining[i].lng
          );
          if (distance < minDistance) {
            minDistance = distance;
            nearestIndex = i;
          }
        }

        // Add nearest location to route
        route.push(remaining.splice(nearestIndex, 1)[0]);
      }

      return route;
    },
    [calculateDistance]
  );

  // Advanced route optimization considering traffic, time windows, and capacity
  const optimizeRouteAdvanced = useCallback(
    (
      locations: Location[],
      vehicleCapacity?: { maxWeight: number; maxVolume: number },
      preferredStartTime: string = '08:00'
    ): OptimizedRoute => {
      // First, optimize the basic route
      const orderedLocations = optimizeRoute(locations);

      // Calculate segments with traffic factors
      const segments: RouteSegment[] = [];
      let totalDistance = 0;
      let totalDuration = 0;
      let currentTime = preferredStartTime;

      for (let i = 0; i < orderedLocations.length - 1; i++) {
        const from = orderedLocations[i];
        const to = orderedLocations[i + 1];

        const distance = calculateDistance(from.lat, from.lng, to.lat, to.lng);
        const trafficFactor = getTrafficFactor(from.name, currentTime);
        const duration = estimateDuration(distance, trafficFactor);

        segments.push({
          from,
          to,
          distance,
          duration,
          trafficFactor,
        });

        totalDistance += distance;
        totalDuration += duration;

        // Update current time (simplified)
        const [hours, minutes] = currentTime.split(':').map(Number);
        const newTotalMinutes = hours * 60 + minutes + duration;
        const newHours = Math.floor(newTotalMinutes / 60) % 24;
        const newMinutes = newTotalMinutes % 60;
        currentTime = `${newHours.toString().padStart(2, '0')}:${newMinutes.toString().padStart(2, '0')}`;
      }

      return {
        locations: orderedLocations,
        totalDistance,
        totalDuration,
        segments,
      };
    },
    [calculateDistance, estimateDuration, getTrafficFactor, optimizeRoute]
  );

  // Get traffic insights for a set of locations
  const getTrafficInsights = useCallback((): TrafficInsights => {
    return TRAFFIC_PATTERNS;
  }, []);

  // Check if route respects delivery time windows
  const validateTimeWindows = useCallback((route: OptimizedRoute): boolean => {
    let currentTime = '08:00'; // Default start time

    for (const segment of route.segments) {
      // Add travel time
      const [hours, minutes] = currentTime.split(':').map(Number);
      const newTotalMinutes = hours * 60 + minutes + segment.duration;
      const newHours = Math.floor(newTotalMinutes / 60) % 24;
      const newMinutes = newTotalMinutes % 60;
      currentTime = `${newHours.toString().padStart(2, '0')}:${newMinutes.toString().padStart(2, '0')}`;

      // Check if we're at a delivery location
      const toLocation = segment.to;
      if (toLocation.deliveryWindowStart && toLocation.deliveryWindowEnd) {
        // Simple validation - in a real implementation, this would be more complex
        if (
          currentTime < toLocation.deliveryWindowStart ||
          currentTime > toLocation.deliveryWindowEnd
        ) {
          return false;
        }
      }
    }

    return true;
  }, []);

  return {
    optimizeRoute,
    optimizeRouteAdvanced,
    getTrafficInsights,
    validateTimeWindows,
    convertStopsToLocations,
  };
}
