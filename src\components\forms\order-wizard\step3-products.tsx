'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { OrderFormData } from '@/types/order-form';
import {
  ProductItem,
  SpecialHandling,
  Dimensions,
} from '@/lib/validation/order-schemas';

interface Step3Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
}

export function OrderWizardStep3({ formData, updateFormData }: Step3Props) {
  const addProduct = () => {
    const newProduct: ProductItem = {
      id: Date.now().toString(),
      name: '',
      quantity: 1,
      unit_measure: 'kg',
      unit_price: 0,
      subtotal: 0,
      weight: 1,
      weight_unit: 'kg',
      dimensions: {
        length: 10,
        width: 10,
        height: 10,
        unit: 'cm',
      },
      special_handling: {
        fragile: false,
        perishable: false,
        valuable: false,
        hazardous: false,
        refrigerated: false,
        oversized: false,
      },
      notes: '',
    };
    updateFormData({
      products: [...formData.products, newProduct],
    });
  };

  const removeProduct = (id: string) => {
    updateFormData({
      products: formData.products.filter(product => product.id !== id),
    });
  };

  const updateProduct = (
    id: string,
    field: keyof ProductItem,
    value: string | number
  ) => {
    const updatedProducts = formData.products.map(product => {
      if (product.id === id) {
        const updatedProduct = { ...product, [field]: value };
        // Recalculate subtotal
        if (field === 'quantity' || field === 'unit_price') {
          updatedProduct.subtotal =
            updatedProduct.quantity * updatedProduct.unit_price;
        }
        return updatedProduct;
      }
      return product;
    });

    updateFormData({
      products: updatedProducts,
    });
  };

  // Calculate total cost
  const totalCost = formData.products.reduce(
    (sum, product) => sum + product.subtotal,
    0
  );

  return (
    <Card>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <div>
            <CardTitle className='flex items-center gap-2'>
              🛒 Carrito de Compras
            </CardTitle>
            <CardDescription>Lista de productos para tu pedido</CardDescription>
          </div>
          <Button
            type='button'
            variant='outline'
            onClick={addProduct}
            className='text-sm'
          >
            + Agregar Producto
          </Button>
        </div>
      </CardHeader>
      <CardContent className='space-y-6'>
        {formData.products.map((product, index) => (
          <div
            key={product.id}
            className='border border-gray-200 rounded-lg p-4 space-y-4'
          >
            <div className='flex items-center justify-between'>
              <h4 className='font-medium text-gray-900'>
                Producto #{index + 1}
              </h4>
              {formData.products.length > 1 && (
                <Button
                  type='button'
                  variant='ghost'
                  onClick={() => removeProduct(product.id)}
                  className='text-red-600 text-sm'
                >
                  Eliminar
                </Button>
              )}
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <Label htmlFor={`product_name_${product.id}`}>
                  Nombre del Producto
                </Label>
                <Input
                  id={`product_name_${product.id}`}
                  value={product.name}
                  onChange={e =>
                    updateProduct(product.id, 'name', e.target.value)
                  }
                  placeholder='ej., Manzana roja, Leche deslactosada'
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor={`product_quantity_${product.id}`}>
                  Cantidad
                </Label>
                <Input
                  id={`product_quantity_${product.id}`}
                  type='number'
                  min='0.1'
                  step='0.1'
                  value={product.quantity}
                  onChange={e =>
                    updateProduct(
                      product.id,
                      'quantity',
                      parseFloat(e.target.value) || 0
                    )
                  }
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor={`product_unit_${product.id}`}>
                  Unidad de Medida
                </Label>
                <select
                  id={`product_unit_${product.id}`}
                  value={product.unit_measure}
                  onChange={e =>
                    updateProduct(product.id, 'unit_measure', e.target.value)
                  }
                  className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                  required
                >
                  <option value='kg'>kg</option>
                  <option value='g'>Gramos</option>
                  <option value='pieza'>Pieza</option>
                  <option value='paquete'>Paquete</option>
                  <option value='litro'>Litro</option>
                  <option value='ml'>Mililitros</option>
                  <option value='unidad'>Unidad</option>
                  <option value='caja'>Caja</option>
                  <option value='bulto'>Bulto</option>
                </select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor={`product_price_${product.id}`}>
                  Precio Unitario (MXN)
                </Label>
                <Input
                  id={`product_price_${product.id}`}
                  type='number'
                  min='0'
                  step='0.01'
                  value={product.unit_price}
                  onChange={e =>
                    updateProduct(
                      product.id,
                      'unit_price',
                      parseFloat(e.target.value) || 0
                    )
                  }
                  placeholder='0.00'
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor={`product_subtotal_${product.id}`}>
                  Subtotal
                </Label>
                <Input
                  id={`product_subtotal_${product.id}`}
                  type='text'
                  value={`$${product.subtotal.toFixed(2)}`}
                  disabled
                  className='bg-gray-50'
                />
              </div>

              {/* Weight Section */}
              <div className='space-y-2'>
                <Label htmlFor={`product_weight_${product.id}`}>
                  Peso Total
                </Label>
                <div className='flex gap-2'>
                  <Input
                    id={`product_weight_${product.id}`}
                    type='number'
                    min='0.001'
                    step='0.001'
                    value={product.weight || 1}
                    onChange={e =>
                      updateProduct(
                        product.id,
                        'weight',
                        parseFloat(e.target.value) || 1
                      )
                    }
                    placeholder='1.0'
                    required
                    className='flex-1'
                  />
                  <select
                    value={product.weight_unit || 'kg'}
                    onChange={e =>
                      updateProduct(product.id, 'weight_unit', e.target.value)
                    }
                    className='px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                  >
                    <option value='kg'>kg</option>
                    <option value='g'>g</option>
                  </select>
                </div>
              </div>

              <div className='space-y-2'>
                <Label>Volumen Estimado</Label>
                <div className='text-sm text-gray-600'>
                  {product.dimensions
                    ? `${((product.dimensions.length * product.dimensions.width * product.dimensions.height) / 1000000).toFixed(3)} m³`
                    : 'No especificado'}
                </div>
              </div>

              {/* Dimensions Section */}
              <div className='space-y-2 md:col-span-2'>
                <Label>Dimensiones (Largo × Ancho × Alto)</Label>
                <div className='grid grid-cols-4 gap-2'>
                  <Input
                    type='number'
                    min='0.1'
                    step='0.1'
                    value={product.dimensions?.length || 10}
                    onChange={e =>
                      updateProduct(product.id, 'dimensions', {
                        ...product.dimensions,
                        length: parseFloat(e.target.value) || 10,
                      })
                    }
                    placeholder='Largo'
                  />
                  <Input
                    type='number'
                    min='0.1'
                    step='0.1'
                    value={product.dimensions?.width || 10}
                    onChange={e =>
                      updateProduct(product.id, 'dimensions', {
                        ...product.dimensions,
                        width: parseFloat(e.target.value) || 10,
                      })
                    }
                    placeholder='Ancho'
                  />
                  <Input
                    type='number'
                    min='0.1'
                    step='0.1'
                    value={product.dimensions?.height || 10}
                    onChange={e =>
                      updateProduct(product.id, 'dimensions', {
                        ...product.dimensions,
                        height: parseFloat(e.target.value) || 10,
                      })
                    }
                    placeholder='Alto'
                  />
                  <select
                    value={product.dimensions?.unit || 'cm'}
                    onChange={e =>
                      updateProduct(product.id, 'dimensions', {
                        ...product.dimensions,
                        unit: e.target.value as 'cm' | 'm',
                      })
                    }
                    className='px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                  >
                    <option value='cm'>cm</option>
                    <option value='m'>m</option>
                  </select>
                </div>
              </div>

              {/* Special Handling Section */}
              <div className='space-y-2 md:col-span-2'>
                <Label>Manejo Especial</Label>
                <div className='grid grid-cols-2 md:grid-cols-3 gap-2'>
                  <label className='flex items-center space-x-2'>
                    <input
                      type='checkbox'
                      checked={product.special_handling?.fragile || false}
                      onChange={e =>
                        updateProduct(product.id, 'special_handling', {
                          ...product.special_handling,
                          fragile: e.target.checked,
                        })
                      }
                      className='text-blue-600'
                    />
                    <span className='text-sm'>🔸 Frágil</span>
                  </label>
                  <label className='flex items-center space-x-2'>
                    <input
                      type='checkbox'
                      checked={product.special_handling?.perishable || false}
                      onChange={e =>
                        updateProduct(product.id, 'special_handling', {
                          ...product.special_handling,
                          perishable: e.target.checked,
                        })
                      }
                      className='text-blue-600'
                    />
                    <span className='text-sm'>❄️ Perecedero</span>
                  </label>
                  <label className='flex items-center space-x-2'>
                    <input
                      type='checkbox'
                      checked={product.special_handling?.valuable || false}
                      onChange={e =>
                        updateProduct(product.id, 'special_handling', {
                          ...product.special_handling,
                          valuable: e.target.checked,
                        })
                      }
                      className='text-blue-600'
                    />
                    <span className='text-sm'>💎 Valioso</span>
                  </label>
                  <label className='flex items-center space-x-2'>
                    <input
                      type='checkbox'
                      checked={product.special_handling?.hazardous || false}
                      onChange={e =>
                        updateProduct(product.id, 'special_handling', {
                          ...product.special_handling,
                          hazardous: e.target.checked,
                        })
                      }
                      className='text-blue-600'
                    />
                    <span className='text-sm'>⚠️ Peligroso</span>
                  </label>
                  <label className='flex items-center space-x-2'>
                    <input
                      type='checkbox'
                      checked={product.special_handling?.refrigerated || false}
                      onChange={e =>
                        updateProduct(product.id, 'special_handling', {
                          ...product.special_handling,
                          refrigerated: e.target.checked,
                        })
                      }
                      className='text-blue-600'
                    />
                    <span className='text-sm'>🧊 Refrigerado</span>
                  </label>
                  <label className='flex items-center space-x-2'>
                    <input
                      type='checkbox'
                      checked={product.special_handling?.oversized || false}
                      onChange={e =>
                        updateProduct(product.id, 'special_handling', {
                          ...product.special_handling,
                          oversized: e.target.checked,
                        })
                      }
                      className='text-blue-600'
                    />
                    <span className='text-sm'>📏 Sobredimensionado</span>
                  </label>
                </div>
              </div>

              <div className='space-y-2 md:col-span-2'>
                <Label htmlFor={`product_notes_${product.id}`}>
                  Observaciones del Producto
                </Label>
                <Textarea
                  id={`product_notes_${product.id}`}
                  value={product.notes || ''}
                  onChange={e =>
                    updateProduct(product.id, 'notes', e.target.value)
                  }
                  placeholder='ej., maduro, sin azúcar, marca específica, instrucciones especiales'
                  rows={2}
                />
              </div>
            </div>
          </div>
        ))}

        {/* Enhanced Total Summary */}
        <div className='bg-blue-50 border border-blue-200 rounded-lg p-4 space-y-3'>
          <h4 className='text-lg font-medium text-blue-800'>
            Resumen de Carga
          </h4>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-blue-900'>
                ${totalCost.toFixed(2)}
              </div>
              <div className='text-sm text-blue-700'>Total MXN</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-blue-900'>
                {formData.products
                  .reduce((sum, product) => {
                    const weight = product.weight || 1;
                    const weightInKg =
                      product.weight_unit === 'g' ? weight / 1000 : weight;
                    return sum + weightInKg * product.quantity;
                  }, 0)
                  .toFixed(2)}
              </div>
              <div className='text-sm text-blue-700'>kg Total</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-blue-900'>
                {formData.products
                  .reduce((sum, product) => {
                    if (!product.dimensions) return sum;
                    const volume =
                      (product.dimensions.length *
                        product.dimensions.width *
                        product.dimensions.height) /
                      1000000;
                    return sum + volume * product.quantity;
                  }, 0)
                  .toFixed(3)}
              </div>
              <div className='text-sm text-blue-700'>m³ Total</div>
            </div>
          </div>

          {/* Special Handling Summary */}
          {formData.products.some(
            p =>
              p.special_handling?.fragile ||
              p.special_handling?.perishable ||
              p.special_handling?.valuable ||
              p.special_handling?.hazardous ||
              p.special_handling?.refrigerated ||
              p.special_handling?.oversized
          ) && (
            <div className='border-t border-blue-200 pt-3'>
              <div className='text-sm font-medium text-blue-800 mb-2'>
                Requerimientos Especiales:
              </div>
              <div className='flex flex-wrap gap-2'>
                {formData.products.some(p => p.special_handling?.fragile) && (
                  <span className='px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs'>
                    🔸 Frágil
                  </span>
                )}
                {formData.products.some(
                  p => p.special_handling?.perishable
                ) && (
                  <span className='px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs'>
                    ❄️ Perecedero
                  </span>
                )}
                {formData.products.some(p => p.special_handling?.valuable) && (
                  <span className='px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs'>
                    💎 Valioso
                  </span>
                )}
                {formData.products.some(p => p.special_handling?.hazardous) && (
                  <span className='px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs'>
                    ⚠️ Peligroso
                  </span>
                )}
                {formData.products.some(
                  p => p.special_handling?.refrigerated
                ) && (
                  <span className='px-2 py-1 bg-cyan-100 text-cyan-800 rounded-full text-xs'>
                    🧊 Refrigerado
                  </span>
                )}
                {formData.products.some(p => p.special_handling?.oversized) && (
                  <span className='px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs'>
                    📏 Sobredimensionado
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
