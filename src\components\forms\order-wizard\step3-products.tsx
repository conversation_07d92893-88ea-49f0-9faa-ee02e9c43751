'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { OrderFormData, ProductItem } from '@/types/order-form';

interface Step3Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
}

export function OrderWizardStep3({ formData, updateFormData }: Step3Props) {
  const addProduct = () => {
    const newProduct: ProductItem = {
      id: Date.now().toString(),
      name: '',
      quantity: 1,
      unit_measure: 'kg',
      unit_price: 0,
      subtotal: 0,
      notes: '',
    };
    updateFormData({
      products: [...formData.products, newProduct],
    });
  };

  const removeProduct = (id: string) => {
    updateFormData({
      products: formData.products.filter(product => product.id !== id),
    });
  };

  const updateProduct = (
    id: string,
    field: keyof ProductItem,
    value: string | number
  ) => {
    const updatedProducts = formData.products.map(product => {
      if (product.id === id) {
        const updatedProduct = { ...product, [field]: value };
        // Recalculate subtotal
        if (field === 'quantity' || field === 'unit_price') {
          updatedProduct.subtotal =
            updatedProduct.quantity * updatedProduct.unit_price;
        }
        return updatedProduct;
      }
      return product;
    });

    updateFormData({
      products: updatedProducts,
    });
  };

  // Calculate total cost
  const totalCost = formData.products.reduce(
    (sum, product) => sum + product.subtotal,
    0
  );

  return (
    <Card>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <div>
            <CardTitle className='flex items-center gap-2'>
              🛒 Carrito de Compras
            </CardTitle>
            <CardDescription>Lista de productos para tu pedido</CardDescription>
          </div>
          <Button
            type='button'
            variant='outline'
            onClick={addProduct}
            className='text-sm'
          >
            + Agregar Producto
          </Button>
        </div>
      </CardHeader>
      <CardContent className='space-y-6'>
        {formData.products.map((product, index) => (
          <div
            key={product.id}
            className='border border-gray-200 rounded-lg p-4 space-y-4'
          >
            <div className='flex items-center justify-between'>
              <h4 className='font-medium text-gray-900'>
                Producto #{index + 1}
              </h4>
              {formData.products.length > 1 && (
                <Button
                  type='button'
                  variant='ghost'
                  onClick={() => removeProduct(product.id)}
                  className='text-red-600 text-sm'
                >
                  Eliminar
                </Button>
              )}
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <Label htmlFor={`product_name_${product.id}`}>
                  Nombre del Producto
                </Label>
                <Input
                  id={`product_name_${product.id}`}
                  value={product.name}
                  onChange={e =>
                    updateProduct(product.id, 'name', e.target.value)
                  }
                  placeholder='ej., Manzana roja, Leche deslactosada'
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor={`product_quantity_${product.id}`}>
                  Cantidad
                </Label>
                <Input
                  id={`product_quantity_${product.id}`}
                  type='number'
                  min='0.1'
                  step='0.1'
                  value={product.quantity}
                  onChange={e =>
                    updateProduct(
                      product.id,
                      'quantity',
                      parseFloat(e.target.value) || 0
                    )
                  }
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor={`product_unit_${product.id}`}>
                  Unidad de Medida
                </Label>
                <select
                  id={`product_unit_${product.id}`}
                  value={product.unit_measure}
                  onChange={e =>
                    updateProduct(product.id, 'unit_measure', e.target.value)
                  }
                  className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                  required
                >
                  <option value='kg'>kg</option>
                  <option value='pieza'>Pieza</option>
                  <option value='paquete'>Paquete</option>
                  <option value='litro'>Litro</option>
                  <option value='gramo'>Gramo</option>
                  <option value='unidad'>Unidad</option>
                </select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor={`product_price_${product.id}`}>
                  Precio Unitario (MXN)
                </Label>
                <Input
                  id={`product_price_${product.id}`}
                  type='number'
                  min='0'
                  step='0.01'
                  value={product.unit_price}
                  onChange={e =>
                    updateProduct(
                      product.id,
                      'unit_price',
                      parseFloat(e.target.value) || 0
                    )
                  }
                  placeholder='0.00'
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor={`product_subtotal_${product.id}`}>
                  Subtotal
                </Label>
                <Input
                  id={`product_subtotal_${product.id}`}
                  type='text'
                  value={`$${product.subtotal.toFixed(2)}`}
                  disabled
                  className='bg-gray-50'
                />
              </div>
              <div className='space-y-2 md:col-span-2'>
                <Label htmlFor={`product_notes_${product.id}`}>
                  Observaciones del Producto
                </Label>
                <Input
                  id={`product_notes_${product.id}`}
                  value={product.notes || ''}
                  onChange={e =>
                    updateProduct(product.id, 'notes', e.target.value)
                  }
                  placeholder='ej., maduro, sin azúcar, marca específica'
                />
              </div>
            </div>
          </div>
        ))}

        {/* Total Summary */}
        <div className='border-t pt-4'>
          <div className='flex justify-between items-center text-lg font-semibold'>
            <span>Total del Pedido:</span>
            <span className='text-green-600'>${totalCost.toFixed(2)} MXN</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
