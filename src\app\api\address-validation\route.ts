import { NextResponse } from 'next/server';
import { secureAPI, sanitizeInput } from '@/lib/security/api-security';
import { auditLog } from '@/lib/security/audit-logger';

// Mexican states (estados) list
const MEXICAN_STATES = [
  'Aguascalientes',
  'Baja California',
  'Baja California Sur',
  'Campeche',
  'Chiapas',
  'Chihuahua',
  'Ciudad de México',
  'Coahuila',
  'Colima',
  'Durango',
  'Guanajuato',
  'Guerrero',
  'Hidalgo',
  'Jalisco',
  'México',
  'Michoacán',
  'Morelos',
  'Nayarit',
  'Nuevo León',
  'Oaxaca',
  'Puebla',
  'Querétaro',
  'Quintana Roo',
  'San Luis Potosí',
  'Sinaloa',
  'Sonora',
  'Tabasco',
  'Tamaulipas',
  'Tlaxcala',
  'Veracruz',
  'Yucatán',
  'Zacatecas',
];

// Common Mexican address formats
const ADDRESS_FORMATS = {
  standard: /^[a-zA-Z0-9\s#áéíó<PERSON>ÁÉÍÓÚüÜñÑ\.,\-]+$/,
  zipCode: /^\d{5}$/,
  phone: /^(\+52\s?)?(\d{10}|\d{3}\s\d{3}\s\d{4}|\(\d{3}\)\s\d{3}\s\d{4})$/,
};

// POST /api/address-validation - Validate Mexican address
export const POST = secureAPI(
  {
    POST: async (request, context) => {
      try {
        // Parse and sanitize request body
        let requestBody;
        try {
          requestBody = sanitizeInput(await request.json());
        } catch {
          return NextResponse.json(
            { error: 'Formato de cuerpo de solicitud inválido' },
            { status: 400 }
          );
        }

        const { address, city, state, zip_code, country, phone } =
          requestBody as {
            address?: string;
            city?: string;
            state?: string;
            zip_code?: string;
            country?: string;
            phone?: string;
          };

        // Validate required fields
        if (!address || !city || !state || !zip_code || !country) {
          return NextResponse.json(
            { error: 'Todos los campos de dirección son requeridos' },
            { status: 400 }
          );
        }

        // Validate country is Mexico
        if (
          country.toLowerCase() !== 'mexico' &&
          country.toLowerCase() !== 'méxico'
        ) {
          return NextResponse.json(
            { error: 'Este servicio solo valida direcciones en México' },
            { status: 400 }
          );
        }

        // Validate address format
        if (!ADDRESS_FORMATS.standard.test(address)) {
          return NextResponse.json(
            { error: 'Formato de dirección inválido' },
            { status: 400 }
          );
        }

        // Validate city format
        if (!ADDRESS_FORMATS.standard.test(city)) {
          return NextResponse.json(
            { error: 'Formato de ciudad inválido' },
            { status: 400 }
          );
        }

        // Validate state
        const normalizedState = state.trim();
        const validState = MEXICAN_STATES.some(
          s => s.toLowerCase() === normalizedState.toLowerCase()
        );

        if (!validState) {
          return NextResponse.json(
            {
              error:
                'Estado inválido. Debe ser un estado de la República Mexicana',
              validStates: MEXICAN_STATES,
            },
            { status: 400 }
          );
        }

        // Validate zip code format
        if (!ADDRESS_FORMATS.zipCode.test(zip_code)) {
          return NextResponse.json(
            { error: 'Código postal inválido. Debe tener 5 dígitos' },
            { status: 400 }
          );
        }

        // Validate phone format if provided
        if (phone && !ADDRESS_FORMATS.phone.test(phone)) {
          return NextResponse.json(
            {
              error: 'Formato de teléfono inválido. Ejemplo: +52 ************',
            },
            { status: 400 }
          );
        }

        // If all validations pass, return success
        const validationResult = {
          isValid: true,
          normalizedAddress: {
            address: address.trim(),
            city: city.trim(),
            state: MEXICAN_STATES.find(
              s => s.toLowerCase() === normalizedState.toLowerCase()
            ),
            zip_code: zip_code.trim(),
            country: 'México',
            phone: phone ? phone.trim() : undefined,
          },
          validationDetails: {
            address: 'valid',
            city: 'valid',
            state: 'valid',
            zipCode: 'valid',
            phone: phone ? 'valid' : 'not_provided',
          },
        };

        await auditLog({
          event: 'ADDRESS_VALIDATED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            validationResult,
            originalRequest: requestBody,
          },
          severity: 'LOW',
          category: 'DATA',
        });

        return NextResponse.json({
          success: true,
          message: 'Dirección validada exitosamente',
          data: validationResult,
        });
      } catch (error) {
        await auditLog({
          event: 'ADDRESS_VALIDATION_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: false,
    rateLimitRpm: 60,
  }
);

// GET /api/address-validation/states - Get list of Mexican states
export const GET = secureAPI(
  {
    GET: async (request, context) => {
      try {
        await auditLog({
          event: 'MEXICAN_STATES_FETCHED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { count: MEXICAN_STATES.length },
          severity: 'LOW',
          category: 'DATA',
        });

        return NextResponse.json({
          success: true,
          data: {
            states: MEXICAN_STATES,
            count: MEXICAN_STATES.length,
          },
        });
      } catch (error) {
        await auditLog({
          event: 'MEXICAN_STATES_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: false,
    rateLimitRpm: 120,
  }
);
