import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // Fetch user wallet balance
    const { data: wallet, error: walletError } = await supabase
      .from('user_wallets')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (walletError && walletError.code !== 'PGRST116') {
      console.error('Error fetching wallet:', walletError);
      return NextResponse.json(
        { error: 'Failed to fetch balance' },
        { status: 500 }
      );
    }

    const balance = wallet ? wallet.balance : 0;

    // Fetch recent transaction history with Stripe correlation
    const { data: transactions, error: transactionsError } = await supabase
      .from('wallet_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10);

    if (transactionsError) {
      console.error('Error fetching transactions:', transactionsError);
      // Don't fail the request, just log the error
    }

    // Format transactions for better readability
    const formattedTransactions =
      transactions?.map(tx => ({
        id: tx.id,
        type: tx.type,
        amount: tx.amount,
        description: tx.description,
        status: tx.status,
        stripe_payment_intent_id: tx.stripe_payment_intent_id,
        created_at: tx.created_at,
        // Extract transaction reference from description if available
        transaction_ref: tx.description?.match(/mouvers_\w+_\d+/)?.[0] || null,
      })) || [];

    return NextResponse.json({
      balance,
      wallet_id: wallet?.id || null,
      recent_transactions: formattedTransactions,
      success: true,
    });
  } catch (error) {
    console.error('Error in balance API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
