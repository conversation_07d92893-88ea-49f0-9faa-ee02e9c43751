import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { stripe } from '@/lib/stripe';
import <PERSON><PERSON> from 'stripe';

// In production, you'd want to use Redis or a database for this
// For now, using in-memory Set with cleanup
const processedEvents = new Map<string, number>();
const CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour
const EVENT_RETENTION = 24 * 60 * 60 * 1000; // 24 hours

// Cleanup old processed events periodically
setInterval(() => {
  const now = Date.now();
  for (const [eventId, timestamp] of processedEvents.entries()) {
    if (now - timestamp > EVENT_RETENTION) {
      processedEvents.delete(eventId);
    }
  }
}, CLEANUP_INTERVAL);

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  console.log(
    `[Webhook] ${new Date().toISOString()} - Received webhook request`
  );

  const body = await request.text();
  const signature = request.headers.get('stripe-signature');

  let event: Stripe.Event;

  // Runtime check for webhook secret (not at build time)
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

  if (!webhookSecret) {
    console.error(
      '[Webhook] ❌ STRIPE_WEBHOOK_SECRET not configured. Please add this to your environment variables.'
    );
    console.error(
      '[Webhook] 📝 You can find the webhook secret in your Stripe Dashboard:'
    );
    console.error('[Webhook] 🔗 https://dashboard.stripe.com/webhooks');
    console.error(
      '[Webhook] ▶️ Select your webhook endpoint > "Signing secret" > "Click to reveal"'
    );
    return NextResponse.json(
      {
        error: 'Webhook endpoint secret not configured',
        help: 'Add STRIPE_WEBHOOK_SECRET to your environment variables. Find it in Stripe Dashboard > Webhooks > [Your Endpoint] > Signing secret',
      },
      { status: 500 }
    );
  }

  if (!signature) {
    console.error(
      '[Webhook] ❌ No Stripe signature provided in request headers'
    );
    console.error(
      '[Webhook] 💡 This suggests the request is not coming from Stripe'
    );
    return NextResponse.json(
      { error: 'No signature provided' },
      { status: 400 }
    );
  }

  // Verify webhook signature
  try {
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    console.log(
      `[Webhook] ✅ Signature verified successfully for event: ${event.type}`
    );
  } catch (err) {
    console.error('[Webhook] ❌ Signature verification failed:', err);
    console.error(
      '[Webhook] 🔧 Make sure you are using the correct webhook secret from your Stripe Dashboard'
    );
    console.error(
      '[Webhook] 🔗 Dashboard: https://dashboard.stripe.com/webhooks'
    );
    return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
  }

  // Handle duplicate events (Stripe best practice)
  if (processedEvents.has(event.id)) {
    console.log(`[Webhook] 🔄 Duplicate event ${event.id} ignored`);
    return NextResponse.json({ received: true, duplicate: true });
  }

  console.log(`[Webhook] 🔄 Processing event: ${event.type} - ${event.id}`);

  // Mark event as processed immediately
  processedEvents.set(event.id, Date.now());

  // Log webhook receipt to database for monitoring
  try {
    const supabase = await createClient();
    await supabase.from('webhook_logs').insert({
      event_type: event.type,
      stripe_event_id: event.id,
      stripe_object_id:
        ((event.data.object as Record<string, unknown>).id as string) ||
        'unknown',
      status: 'processing',
      metadata: {
        received_at: new Date().toISOString(),
        event_created: new Date(event.created * 1000).toISOString(),
        api_version: event.api_version,
        request_id: event.request?.id || null,
      },
    });
  } catch (logError) {
    console.error('[Webhook] ⚠️ Failed to log webhook receipt:', logError);
    // Don't fail the webhook for logging issues
  }

  // Return 200 immediately (Stripe best practice) and process asynchronously
  const responsePromise = NextResponse.json({
    received: true,
    event_id: event.id,
    event_type: event.type,
    timestamp: new Date().toISOString(),
    processing_time_ms: Date.now() - startTime,
  });

  // Process event asynchronously (don't await - respond immediately)
  processEventAsync(event).catch(error => {
    console.error(
      `[Webhook] ❌ Async processing failed for event ${event.id}:`,
      error
    );
  });

  return responsePromise;
}

// Async event processing function
async function processEventAsync(event: Stripe.Event) {
  const processingStart = Date.now();

  try {
    console.log(
      `[Webhook] 🚀 Started async processing for ${event.type} - ${event.id}`
    );

    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(
          event.data.object as Stripe.Checkout.Session,
          event.id
        );
        break;
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(
          event.data.object as Stripe.PaymentIntent
        );
        break;
      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(
          event.data.object as Stripe.PaymentIntent
        );
        break;
      case 'invoice.payment_succeeded':
        console.log(
          '[Webhook] 💰 Invoice payment succeeded:',
          event.data.object.id
        );
        break;
      default:
        console.log(`[Webhook] ⚪ Unhandled event type: ${event.type}`);
    }

    const processingTime = Date.now() - processingStart;
    console.log(
      `[Webhook] ✅ Successfully processed event: ${event.type} - ${event.id} (${processingTime}ms)`
    );

    // Update webhook log with success
    await updateWebhookLog(event.id, 'processed', {
      processing_time_ms: processingTime,
    });
  } catch (error) {
    const processingTime = Date.now() - processingStart;
    console.error(
      `[Webhook] ❌ Error processing event ${event.id} (${processingTime}ms):`,
      error
    );

    // Update webhook log with error
    await updateWebhookLog(event.id, 'failed', {
      error_message: error instanceof Error ? error.message : 'Unknown error',
      processing_time_ms: processingTime,
    });
  }
}

async function updateWebhookLog(
  eventId: string,
  status: string,
  additionalData: Record<string, unknown> = {}
) {
  try {
    const supabase = await createClient();
    await supabase
      .from('webhook_logs')
      .update({
        status,
        updated_at: new Date().toISOString(),
        metadata: additionalData,
      })
      .eq('stripe_event_id', eventId);
  } catch (error) {
    console.error('[Webhook] ⚠️ Failed to update webhook log:', error);
  }
}

async function handleCheckoutSessionCompleted(
  session: Stripe.Checkout.Session,
  eventId: string
) {
  try {
    const supabase = await createClient();

    console.log(`[Webhook] 💳 Processing checkout session: ${session.id}`);

    // Extract metadata from session
    const metadata = session.metadata || {};
    const { user_id, transaction_ref, amount_mxn, user_email, platform } =
      metadata;

    if (!user_id || !amount_mxn) {
      console.error('[Webhook] ❌ Missing required metadata:', {
        user_id,
        amount_mxn,
        session_id: session.id,
      });
      throw new Error(
        `Missing required metadata: user_id=${user_id}, amount_mxn=${amount_mxn}`
      );
    }

    const amountNum = parseFloat(amount_mxn);

    console.log(
      `[Webhook] 💰 Processing payment for user ${user_id}, amount: ${amountNum} MXN`
    );

    // Check for duplicate transaction by payment intent (Stripe best practice)
    const { data: existingTransaction, error: checkError } = await supabase
      .from('wallet_transactions')
      .select('id, status')
      .eq('stripe_payment_intent_id', session.payment_intent as string)
      .single();

    if (existingTransaction) {
      console.log(
        `[Webhook] 🔄 Transaction already processed for payment_intent: ${session.payment_intent}`
      );
      return;
    }

    if (checkError && checkError.code !== 'PGRST116') {
      console.error(
        '[Webhook] ❌ Error checking for existing transaction:',
        checkError
      );
      throw checkError;
    }

    // Step 1: Ensure user wallet exists (create if doesn't exist)
    const { data: existingWallet, error: walletError } = await supabase
      .from('user_wallets')
      .select('*')
      .eq('user_id', user_id)
      .single();

    let wallet = existingWallet;

    if (walletError && walletError.code === 'PGRST116') {
      // Wallet doesn't exist, create it
      console.log(`[Webhook] 👛 Creating new wallet for user ${user_id}`);
      const { data: newWallet, error: createError } = await supabase
        .from('user_wallets')
        .insert({
          user_id: user_id,
          balance: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (createError) {
        console.error('[Webhook] ❌ Error creating wallet:', createError);
        throw createError;
      }
      wallet = newWallet;
      console.log(
        `[Webhook] ✅ Created wallet ${wallet.id} for user ${user_id}`
      );
    } else if (walletError) {
      console.error('[Webhook] ❌ Error fetching wallet:', walletError);
      throw walletError;
    }

    if (!wallet) {
      console.error('[Webhook] ❌ No wallet found or created');
      throw new Error('Failed to get or create wallet');
    }

    console.log(
      `[Webhook] 👛 Using wallet ${wallet.id} with current balance: ${wallet.balance}`
    );

    // Step 2: Create the transaction
    console.log(`[Webhook] 📝 Creating new transaction`);
    const { data: transaction, error: transactionError } = await supabase
      .from('wallet_transactions')
      .insert({
        user_id: user_id,
        wallet_id: wallet.id,
        type: 'deposit',
        amount: amountNum,
        description: `Stripe payment via checkout session ${session.id}`,
        status: 'completed',
        stripe_payment_intent_id: session.payment_intent as string,
        transaction_ref: transaction_ref,
        metadata: {
          stripe_session_id: session.id,
          webhook_event_id: eventId,
          platform: platform,
          user_email: user_email,
          processed_at: new Date().toISOString(),
          payment_status: session.payment_status,
          customer_email: session.customer_details?.email,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (transactionError) {
      console.error(
        '[Webhook] ❌ Error creating transaction:',
        transactionError
      );
      throw transactionError;
    }

    console.log(
      `[Webhook] ✅ Created transaction ${transaction.id} for ${amountNum} MXN`
    );

    // Step 3: Update wallet balance
    const currentBalance = parseFloat(wallet.balance.toString());
    const newBalance = currentBalance + amountNum;
    const { error: updateError } = await supabase
      .from('user_wallets')
      .update({
        balance: newBalance,
        updated_at: new Date().toISOString(),
      })
      .eq('id', wallet.id);

    if (updateError) {
      console.error('[Webhook] ❌ Error updating wallet balance:', updateError);
      throw updateError;
    }

    console.log(
      `[Webhook] ✅ Updated wallet ${wallet.id} balance from ${currentBalance.toFixed(2)} to ${newBalance.toFixed(2)} MXN`
    );
    console.log(
      `[Webhook] 🎉 Successfully processed checkout session ${session.id}`
    );
  } catch (error) {
    console.error(
      '[Webhook] ❌ Error in handleCheckoutSessionCompleted:',
      error
    );
    throw error;
  }
}

async function handlePaymentIntentSucceeded(
  paymentIntent: Stripe.PaymentIntent
) {
  console.log(`[Webhook] 💳 Payment intent succeeded: ${paymentIntent.id}`);
  // Add specific payment intent handling if needed
}

async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  console.log(`[Webhook] ❌ Payment intent failed: ${paymentIntent.id}`);
  // Add specific payment intent failure handling if needed
}
