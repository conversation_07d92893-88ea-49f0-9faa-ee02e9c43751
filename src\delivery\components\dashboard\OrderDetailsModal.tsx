'use client';

import { useState, useEffect } from 'react';
import MapWrapper from '../maps/MapWrapper';
import dynamic from 'next/dynamic';
import { Order } from '@/delivery/types/order';

// Import BasicMap as fallback
const BasicMap = dynamic(() => import('../maps/BasicMap'), {
  ssr: false,
  loading: () => (
    <div className='w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center'>
      <div className='text-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2'></div>
        <p className='text-sm text-gray-600'>Cargando mapa...</p>
      </div>
    </div>
  ),
});

interface OrderDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  order: Order;
  onStatusUpdate?: (
    orderId: string,
    newStatus:
      | 'pending'
      | 'confirmed'
      | 'in-transit'
      | 'pending-admin-confirmation'
      | 'delivered'
      | 'closed'
      | 'cancelled'
  ) => Promise<void>;
}

export default function OrderDetailsModal({
  isOpen,
  onClose,
  order,
  onStatusUpdate,
}: OrderDetailsModalProps) {
  const [modalKey, setModalKey] = useState(0);
  const [useBasicMap, setUseBasicMap] = useState(false);
  const [showStatusUpdate, setShowStatusUpdate] = useState(false);
  const [newStatus, setNewStatus] = useState<
    | 'pending'
    | 'confirmed'
    | 'in-transit'
    | 'pending-admin-confirmation'
    | 'delivered'
    | 'closed'
    | 'cancelled'
  >(order?.status || 'pending');
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showClientMessage, setShowClientMessage] = useState(false);
  const [messageType, setMessageType] = useState('shipped');
  const [customMessage, setCustomMessage] = useState('');
  const [contactMethods, setContactMethods] = useState<string[]>([]);
  const [sendingMessage, setSendingMessage] = useState(false);

  useEffect(() => {
    if (isOpen && order) {
      // Force reinitialization when modal opens with a new order
      setModalKey(prev => prev + 1);
      setUseBasicMap(false);

      // Set timeout to use basic map if complex map doesn't load
      const timeoutId = setTimeout(() => {
        console.log('⏰ OrderDetailsModal timeout - switching to BasicMap');
        setUseBasicMap(true);
      }, 5000);

      return () => clearTimeout(timeoutId);
    }
  }, [isOpen, order]);

  if (!isOpen || !order) return null;

  const formatAddress = (
    address:
      | {
          street?: string;
          number?: string;
          city?: string;
          state?: string;
          zip?: string;
        }
      | string
      | null
  ) => {
    if (!address) return 'No especificada';
    if (typeof address === 'string') return address;
    return `${address.street || ''} ${address.number || ''}, ${address.city || ''}, ${address.state || ''} ${address.zip || ''}`;
  };

  const formatPackageDetails = (
    details: {
      weight?: string | number;
      dimensions?: string | number;
      description?: string;
    } | null
  ) => {
    if (!details) return 'No especificados';
    return `${details.weight || 'N/A'} kg, ${details.dimensions || 'N/A'} cm, ${details.description || 'Sin descripción'}`;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      confirmed: 'bg-blue-100 text-blue-800',
      'in-transit': 'bg-purple-100 text-purple-800',
      'pending-admin-confirmation': 'bg-orange-100 text-orange-800',
      delivered: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getPaymentStatusColor = (status: string) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      paid: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getStatusDisplayText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pendiente';
      case 'confirmed':
        return 'Confirmado';
      case 'in-transit':
        return 'En Tránsito';
      case 'pending-admin-confirmation':
        return 'Entregado (Pendiente Confirmación)';
      case 'delivered':
        return 'Entregado';
      case 'cancelled':
        return 'Cancelado';
      default:
        return status;
    }
  };

  const getStatusDescription = (status: string) => {
    switch (status) {
      case 'pending':
        return 'La orden ha sido creada pero aún no ha sido confirmada por el cliente.';
      case 'confirmed':
        return 'El cliente ha confirmado la orden y el pago ha sido procesado.';
      case 'in-transit':
        return 'El paquete está en tránsito hacia el destino.';
      case 'pending-admin-confirmation':
        return 'El paquete ha sido marcado como entregado. Un administrador debe confirmar la entrega para completar el proceso.';
      case 'delivered':
        return 'El paquete ha sido entregado al cliente.';
      case 'cancelled':
        return 'La orden ha sido cancelada por el cliente o el administrador.';
      default:
        return 'Estado no especificado.';
    }
  };

  const getPaymentStatusDisplayText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pendiente';
      case 'paid':
        return 'Pagado';
      case 'failed':
        return 'Fallido';
      default:
        return status;
    }
  };

  const handleStatusUpdate = async () => {
    if (!onStatusUpdate || !order) return;

    // Show confirmation dialog
    setShowConfirmation(true);
  };

  const confirmStatusUpdate = async () => {
    if (!onStatusUpdate || !order) return;

    setUpdatingStatus(true);
    try {
      await onStatusUpdate(order.id, newStatus);
      setShowStatusUpdate(false);
      setShowConfirmation(false);
      // Close modal after successful update
      onClose();
    } catch {
      // Silent error handling for security
      setUpdatingStatus(false);
    }
  };

  const handleContactMethodChange = (method: string, checked: boolean) => {
    setContactMethods(prev => {
      if (checked) {
        return [...prev, method];
      } else {
        return prev.filter(m => m !== method);
      }
    });
  };

  const handleSendClientMessage = async () => {
    if (!order.customer) return;

    setSendingMessage(true);
    try {
      const message =
        messageType === 'custom'
          ? customMessage
          : getMessageContent(messageType);
      const subject = `[Orden #${order.id}] ${messageType}`;
      const body = `Hola ${order.customer.full_name || 'Cliente'},

${message}

Gracias por tu preferencia.

Atentamente,
Tu Equipo de Delivery`;

      let sent = false;
      if (contactMethods.includes('email')) {
        const mailtoLink = `mailto:${order.customer.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
        window.open(mailtoLink, '_blank');
        sent = true;
      }
      if (contactMethods.includes('phone')) {
        const telLink = `tel:${order.customer.phone}`;
        window.open(telLink, '_blank');
        sent = true;
      }

      if (!sent) {
        alert(
          'No se pudo enviar el mensaje. No se encontraron métodos de contacto.'
        );
      } else {
        alert('Mensaje enviado con éxito!');
      }
    } catch (error) {
      console.error('Error sending client message:', error);
      alert('Error al enviar el mensaje al cliente.');
    } finally {
      setShowClientMessage(false);
      setMessageType('shipped');
      setCustomMessage('');
      setContactMethods([]);
    }
    setSendingMessage(false);
  };

  const getMessageContent = (type: string) => {
    switch (type) {
      case 'shipped':
        return 'Tu orden ha sido enviada. El paquete está en camino.';
      case 'in-transit':
        return 'El paquete está en tránsito hacia el destino. ¡Estarás recibiendo tu pedido pronto!';
      case 'delivered':
        return '¡Felicitaciones! Tu pedido ha sido entregado. ¡Gracias por tu preferencia!';
      default:
        return 'Mensaje personalizado';
    }
  };

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
      <div
        key={`modal-${modalKey}-${order.id}`}
        className='bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto'
      >
        {/* Header */}
        <div className='sticky top-0 bg-white border-b border-gray-200 px-6 py-4'>
          <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-bold text-gray-900'>
              Detalles de la Orden #{order.id.slice(0, 8)}
            </h2>
            <button
              onClick={onClose}
              className='text-gray-400 hover:text-gray-600 text-2xl font-bold'
            >
              ×
            </button>
          </div>
        </div>

        {/* Content */}
        <div className='p-6 pb-8 space-y-6'>
          {/* Información General */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div className='bg-gray-50 p-4 rounded-lg'>
              <h3 className='text-lg font-semibold text-gray-900 mb-3'>
                Información General
              </h3>
              <div className='space-y-2 text-sm'>
                <div className='flex justify-between'>
                  <span className='font-medium text-gray-700'>ID:</span>
                  <span className='text-gray-600'>{order.id}</span>
                </div>
                <div className='flex justify-between'>
                  <span className='font-medium text-gray-700'>Estado:</span>
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}
                  >
                    {getStatusDisplayText(order.status)}
                  </span>
                </div>
                <div className='mt-2 text-xs text-gray-600 bg-white p-2 rounded border'>
                  {getStatusDescription(order.status)}
                </div>
                <div className='flex justify-between'>
                  <span className='font-medium text-gray-700'>Pago:</span>
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${getPaymentStatusColor(order.payment_status)}`}
                  >
                    {getPaymentStatusDisplayText(order.payment_status)}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span className='font-medium text-gray-700'>
                    Método de Pago:
                  </span>
                  <span className='text-gray-600'>
                    {order.payment_method || 'No especificado'}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span className='font-medium text-gray-700'>
                    Costo Total:
                  </span>
                  <span className='text-gray-600'>
                    ${order.total_cost || '0.00'}
                  </span>
                </div>
              </div>
            </div>

            <div className='bg-gray-50 p-4 rounded-lg'>
              <h3 className='text-lg font-semibold text-gray-900 mb-3'>
                Fechas
              </h3>
              <div className='space-y-2 text-sm'>
                <div className='flex justify-between'>
                  <span className='font-medium text-gray-700'>Creada:</span>
                  <span className='text-gray-600'>
                    {new Date(order.created_at).toLocaleString('es-ES')}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span className='font-medium text-gray-700'>
                    Actualizada:
                  </span>
                  <span className='text-gray-600'>
                    {new Date(order.updated_at).toLocaleString('es-ES')}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Información del Cliente */}
          <div className='bg-blue-50 p-4 rounded-lg'>
            <h3 className='text-lg font-semibold text-gray-900 mb-3'>
              Información del Cliente
            </h3>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4 text-sm'>
              <div>
                <span className='font-medium text-gray-700'>Nombre:</span>
                <p className='text-gray-600'>
                  {order.customer?.full_name || 'No especificado'}
                </p>
              </div>
              <div>
                <span className='font-medium text-gray-700'>Email:</span>
                <p className='text-gray-600'>
                  {order.customer?.email || 'No especificado'}
                </p>
              </div>
              <div>
                <span className='font-medium text-gray-700'>Teléfono:</span>
                <p className='text-gray-600'>
                  {order.customer?.phone || 'No especificado'}
                </p>
              </div>
            </div>
          </div>

          {/* Direcciones */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div className='bg-green-50 p-4 rounded-lg'>
              <h3 className='text-lg font-semibold text-gray-900 mb-3'>
                Dirección de Recogida
              </h3>
              <p className='text-sm text-gray-600'>
                {formatAddress(order.pickup_address)}
              </p>
            </div>

            <div className='bg-orange-50 p-4 rounded-lg'>
              <h3 className='text-lg font-semibold text-gray-900 mb-3'>
                Dirección de Entrega
              </h3>
              <p className='text-sm text-gray-600'>
                {formatAddress(order.delivery_addresses)}
              </p>
            </div>
          </div>

          {/* Mapa de Ruta */}
          <div className='bg-blue-50 p-4 rounded-lg mb-4'>
            <h3 className='text-lg font-semibold text-gray-900 mb-3'>
              Ruta de Entrega
            </h3>
            {/* Use BasicMap as fallback if complex map doesn't load */}
            {useBasicMap ? (
              <BasicMap
                key={`basic-map-${order.id}-${modalKey}`}
                className='h-64'
              />
            ) : (
              <MapWrapper
                key={`map-${order.id}-${modalKey}`}
                pickupAddress={order.pickup_address}
                deliveryAddress={order.delivery_addresses}
                className='h-64'
              />
            )}
          </div>

          {/* Detalles del Paquete */}
          <div className='bg-purple-50 p-4 rounded-lg'>
            <h3 className='text-lg font-semibold text-gray-900 mb-3'>
              Detalles del Paquete
            </h3>
            <p className='text-sm text-gray-600'>
              {formatPackageDetails(order.package_details)}
            </p>
          </div>

          {/* Espacio adicional para evitar superposición con el mapa */}
          <div className='h-8'></div>

          {/* Status Change History */}
          <div className='bg-blue-50 p-4 rounded-lg border border-blue-200'>
            <h3 className='text-lg font-semibold text-gray-900 mb-3'>
              Historial de Cambios de Estado
            </h3>
            <div className='space-y-2 text-sm'>
              <div className='flex justify-between items-center'>
                <span className='font-medium text-gray-700'>
                  Estado Actual:
                </span>
                <span
                  className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}
                >
                  {order.status === 'pending-admin-confirmation'
                    ? 'Pendiente Confirmación'
                    : order.status}
                </span>
              </div>
              <div className='flex justify-between items-center'>
                <span className='font-medium text-gray-700'>
                  Último Cambio:
                </span>
                <span className='text-gray-600'>
                  {new Date(order.updated_at).toLocaleString('es-ES')}
                </span>
              </div>
              <div className='text-xs text-gray-500 italic'>
                Los cambios de estado se registran automáticamente cuando se
                actualiza la orden
              </div>
              {order.status === 'pending-admin-confirmation' && (
                <div className='mt-3 p-3 bg-orange-100 border border-orange-300 rounded-md'>
                  <p className='text-sm text-orange-800 font-medium'>
                    📋 Estado Pendiente de Confirmación
                  </p>
                  <p className='text-xs text-orange-700 mt-1'>
                    El paquete ha sido marcado como entregado. Un administrador
                    debe confirmar la entrega para completar el proceso.
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Status Update Form */}
          {onStatusUpdate && (
            <div className='bg-yellow-50 p-4 rounded-lg border border-yellow-200'>
              <h3 className='text-lg font-semibold text-gray-900 mb-3'>
                Actualizar Estado de la Orden
              </h3>
              {!showStatusUpdate ? (
                <button
                  onClick={() => setShowStatusUpdate(true)}
                  className='px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors'
                >
                  Actualizar Estado
                </button>
              ) : (
                <div className='space-y-4'>
                  {/* Security Warning for Admin-Confirmed Orders */}
                  {(order.status === 'confirmed' ||
                    order.status === 'in-transit') && (
                    <div className='bg-yellow-50 border border-yellow-200 p-3 rounded-md'>
                      <div className='flex items-center space-x-2'>
                        <svg
                          className='h-5 w-5 text-yellow-600'
                          fill='none'
                          viewBox='0 0 24 24'
                          stroke='currentColor'
                        >
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'
                          />
                        </svg>
                        <p className='text-sm text-yellow-800 font-medium'>
                          ⚠️ Orden Confirmada por Administración
                        </p>
                      </div>
                      <p className='text-xs text-yellow-700 mt-1'>
                        Por seguridad, solo puedes marcar esta orden como
                        entregada una vez que completes la entrega.
                      </p>
                    </div>
                  )}

                  <div>
                    <label className='block text-sm font-medium text-gray-700 mb-2'>
                      Nuevo Estado
                    </label>
                    <select
                      value={newStatus}
                      onChange={e =>
                        setNewStatus(
                          e.target.value as
                            | 'pending'
                            | 'confirmed'
                            | 'in-transit'
                            | 'pending-admin-confirmation'
                            | 'delivered'
                            | 'cancelled'
                        )
                      }
                      className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                      disabled={updatingStatus}
                    >
                      {/* Only show relevant options based on current status */}
                      {order.status === 'pending' && (
                        <>
                          <option value='pending'>Pendiente</option>
                          <option value='cancelled'>Cancelado</option>
                        </>
                      )}
                      {(order.status === 'confirmed' ||
                        order.status === 'in-transit') && (
                        <>
                          <option value='confirmed' disabled>
                            Confirmado (Bloqueado por Admin)
                          </option>
                          <option value='in-transit' disabled>
                            En Tránsito (Bloqueado por Admin)
                          </option>
                          <option value='pending-admin-confirmation'>
                            Entregado (Pendiente Confirmación)
                          </option>
                        </>
                      )}
                      {order.status === 'pending-admin-confirmation' && (
                        <>
                          <option value='pending-admin-confirmation'>
                            Pendiente Confirmación Admin
                          </option>
                          <option value='cancelled'>Cancelado</option>
                        </>
                      )}
                      {order.status === 'delivered' && (
                        <option value='delivered'>Entregado</option>
                      )}
                      {order.status === 'cancelled' && (
                        <option value='cancelled'>Cancelado</option>
                      )}
                    </select>
                  </div>
                  <div className='flex space-x-3'>
                    <button
                      onClick={handleStatusUpdate}
                      disabled={updatingStatus}
                      className='px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 transition-colors'
                    >
                      {updatingStatus ? 'Actualizando...' : 'Confirmar Cambio'}
                    </button>
                    <button
                      onClick={() => setShowStatusUpdate(false)}
                      disabled={updatingStatus}
                      className='px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 transition-colors'
                    >
                      Cancelar
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Client Communication Section */}
          {order.customer && (
            <div className='bg-blue-50 border border-blue-200 p-4 rounded-md mb-4'>
              <h3 className='text-lg font-semibold text-blue-900 mb-3 flex items-center'>
                <svg
                  className='h-5 w-5 mr-2'
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z'
                  />
                </svg>
                Comunicación con el Cliente
              </h3>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
                <div>
                  <p className='text-sm font-medium text-blue-800'>Cliente:</p>
                  <p className='text-blue-900'>{order.customer.full_name}</p>
                </div>
                <div>
                  <p className='text-sm font-medium text-blue-800'>Teléfono:</p>
                  <p className='text-blue-900'>{order.customer.phone}</p>
                </div>
                <div className='md:col-span-2'>
                  <p className='text-sm font-medium text-blue-800'>Email:</p>
                  <p className='text-blue-900'>{order.customer.email}</p>
                </div>
              </div>

              <div className='space-y-3'>
                <button
                  onClick={() => setShowClientMessage(true)}
                  className='w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center'
                >
                  <svg
                    className='h-4 w-4 mr-2'
                    fill='none'
                    viewBox='0 0 24 24'
                    stroke='currentColor'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z'
                    />
                  </svg>
                  Enviar Mensaje al Cliente
                </button>

                <button
                  onClick={() => setShowStatusUpdate(true)}
                  className='w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors flex items-center justify-center'
                >
                  <svg
                    className='h-4 w-4 mr-2'
                    fill='none'
                    viewBox='0 0 24 24'
                    stroke='currentColor'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
                    />
                  </svg>
                  Actualizar Estado de la Orden
                </button>
              </div>
            </div>
          )}

          {/* Confirmation Dialog */}
          {showConfirmation && (
            <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
              <div className='bg-white rounded-lg p-6 max-w-md w-full mx-4'>
                <h3 className='text-lg font-semibold text-gray-900 mb-4'>
                  Confirmar Cambio de Estado
                </h3>
                <p className='text-gray-600 mb-6'>
                  ¿Estás seguro de que quieres cambiar el estado de la orden de{' '}
                  <span
                    className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}
                  >
                    {order.status}
                  </span>{' '}
                  a{' '}
                  <span
                    className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(newStatus)}`}
                  >
                    {newStatus}
                  </span>
                  ?
                </p>
                <div className='flex space-x-3'>
                  <button
                    onClick={confirmStatusUpdate}
                    disabled={updatingStatus}
                    className='px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 transition-colors'
                  >
                    {updatingStatus ? 'Actualizando...' : 'Confirmar'}
                  </button>
                  <button
                    onClick={() => setShowConfirmation(false)}
                    disabled={updatingStatus}
                    className='px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 transition-colors'
                  >
                    Cancelar
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Client Message Modal */}
          {showClientMessage && (
            <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
              <div className='bg-white rounded-lg p-6 max-w-md w-full mx-4'>
                <h3 className='text-lg font-semibold text-gray-900 mb-4'>
                  Enviar Mensaje al Cliente
                </h3>

                <div className='mb-4'>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Tipo de Mensaje
                  </label>
                  <select
                    value={messageType}
                    onChange={e => setMessageType(e.target.value)}
                    className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                  >
                    <option value='shipped'>🚚 Orden Enviada</option>
                    <option value='in-transit'>🚛 En Tránsito</option>
                    <option value='delivered'>✅ Entregada</option>
                    <option value='custom'>✉️ Mensaje Personalizado</option>
                  </select>
                </div>

                {messageType === 'custom' && (
                  <div className='mb-4'>
                    <label className='block text-sm font-medium text-gray-700 mb-2'>
                      Mensaje Personalizado
                    </label>
                    <textarea
                      value={customMessage}
                      onChange={e => setCustomMessage(e.target.value)}
                      placeholder='Escribe tu mensaje personalizado aquí...'
                      className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                      rows={3}
                    />
                  </div>
                )}

                <div className='mb-4'>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Método de Contacto
                  </label>
                  <div className='space-y-2'>
                    <label className='flex items-center'>
                      <input
                        type='checkbox'
                        checked={contactMethods.includes('email')}
                        onChange={e =>
                          handleContactMethodChange('email', e.target.checked)
                        }
                        className='mr-2'
                      />
                      <span className='text-sm'>📧 Email</span>
                    </label>
                    <label className='flex items-center'>
                      <input
                        type='checkbox'
                        checked={contactMethods.includes('phone')}
                        onChange={e =>
                          handleContactMethodChange('phone', e.target.checked)
                        }
                        className='mr-2'
                      />
                      <span className='text-sm'>📱 SMS/WhatsApp</span>
                    </label>
                  </div>
                </div>

                <div className='flex space-x-3'>
                  <button
                    onClick={handleSendClientMessage}
                    disabled={sendingMessage || contactMethods.length === 0}
                    className='px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex-1'
                  >
                    {sendingMessage ? 'Enviando...' : 'Enviar Mensaje'}
                  </button>
                  <button
                    onClick={() => setShowClientMessage(false)}
                    className='px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors'
                  >
                    Cancelar
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Acciones */}
          <div className='flex justify-end space-x-3 pt-6 border-t border-gray-200'>
            <button
              onClick={onClose}
              className='px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50'
            >
              Cerrar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
