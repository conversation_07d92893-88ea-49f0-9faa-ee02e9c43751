/**
 * Comprehensive Order Form Validation Schemas using Zod
 *
 * Provides type-safe validation for the complete Mexican logistics order flow
 */

import { z } from 'zod';

// Mexican postal code validation (5 digits)
const mexicanPostalCodeSchema = z
  .string()
  .regex(/^\d{5}$/, 'Código postal debe tener 5 dígitos')
  .refine(code => parseInt(code) >= 1000 && parseInt(code) <= 99999, {
    message: 'Código postal inválido para México',
  });

// Mexican phone number validation
const mexicanPhoneSchema = z
  .string()
  .regex(
    /^(\+52\s?)?(\d{2}\s?\d{4}\s?\d{4}|\d{3}\s?\d{3}\s?\d{4})$/,
    'Formato de teléfono mexicano inválido'
  );

// RFC validation for Mexican tax ID
const rfcSchema = z
  .string()
  .regex(/^[A-ZÑ&]{3,4}\d{6}[A-Z0-9]{3}$/, 'Formato de RFC inválido')
  .optional();

// Special handling flags
export const SpecialHandlingSchema = z.object({
  fragile: z.boolean().default(false),
  perishable: z.boolean().default(false),
  valuable: z.boolean().default(false),
  hazardous: z.boolean().default(false),
  refrigerated: z.boolean().default(false),
  oversized: z.boolean().default(false),
});

// Product dimensions schema
export const DimensionsSchema = z.object({
  length: z
    .number()
    .min(0.1, 'Largo debe ser mayor a 0')
    .max(1000, 'Largo máximo 1000 cm'),
  width: z
    .number()
    .min(0.1, 'Ancho debe ser mayor a 0')
    .max(1000, 'Ancho máximo 1000 cm'),
  height: z
    .number()
    .min(0.1, 'Alto debe ser mayor a 0')
    .max(1000, 'Alto máximo 1000 cm'),
  unit: z.enum(['cm', 'm']).default('cm'),
});

// Enhanced product item schema
export const ProductItemSchema = z.object({
  id: z.string().min(1, 'ID del producto requerido'),
  name: z
    .string()
    .min(2, 'Nombre del producto debe tener al menos 2 caracteres')
    .max(100, 'Nombre del producto muy largo'),
  quantity: z
    .number()
    .min(0.1, 'Cantidad debe ser mayor a 0')
    .max(10000, 'Cantidad máxima 10,000'),
  unit_measure: z.enum([
    'kg',
    'g',
    'pieza',
    'paquete',
    'litro',
    'ml',
    'unidad',
    'caja',
    'bulto',
  ]),
  unit_price: z
    .number()
    .min(0, 'Precio debe ser mayor o igual a 0')
    .max(1000000, 'Precio máximo $1,000,000'),
  subtotal: z.number().min(0, 'Subtotal debe ser mayor o igual a 0'),
  weight: z
    .number()
    .min(0.001, 'Peso debe ser mayor a 0')
    .max(50000, 'Peso máximo 50,000 kg'),
  weight_unit: z.enum(['kg', 'g']).default('kg'),
  dimensions: DimensionsSchema.optional(),
  special_handling: SpecialHandlingSchema.default({
    fragile: false,
    perishable: false,
    valuable: false,
    hazardous: false,
    refrigerated: false,
    oversized: false,
  }),
  notes: z.string().max(500, 'Notas muy largas').optional(),
});

// Address schema with Mexican specifics
export const AddressSchema = z.object({
  id: z.string().min(1, 'ID de dirección requerido'),
  street: z
    .string()
    .min(3, 'Calle debe tener al menos 3 caracteres')
    .max(100, 'Calle muy larga'),
  number: z.string().max(20, 'Número muy largo').optional(),
  colony: z
    .string()
    .min(2, 'Colonia debe tener al menos 2 caracteres')
    .max(100, 'Colonia muy larga'),
  city: z
    .string()
    .min(2, 'Ciudad debe tener al menos 2 caracteres')
    .max(100, 'Ciudad muy larga'),
  state: z.enum([
    'Aguascalientes',
    'Baja California',
    'Baja California Sur',
    'Campeche',
    'Chiapas',
    'Chihuahua',
    'Ciudad de México',
    'Coahuila',
    'Colima',
    'Durango',
    'Estado de México',
    'Guanajuato',
    'Guerrero',
    'Hidalgo',
    'Jalisco',
    'Michoacán',
    'Morelos',
    'Nayarit',
    'Nuevo León',
    'Oaxaca',
    'Puebla',
    'Querétaro',
    'Quintana Roo',
    'San Luis Potosí',
    'Sinaloa',
    'Sonora',
    'Tabasco',
    'Tamaulipas',
    'Tlaxcala',
    'Veracruz',
    'Yucatán',
    'Zacatecas',
  ]),
  zip: mexicanPostalCodeSchema,
  references: z.string().max(200, 'Referencias muy largas').optional(),
  coordinates: z
    .object({
      lat: z.number().min(-90).max(90),
      lng: z.number().min(-180).max(180),
    })
    .optional(),
});

// Stop schema for multi-stop deliveries
export const StopSchema = z.object({
  id: z.string().min(1, 'ID de parada requerido'),
  order: z.number().min(1, 'Orden de parada debe ser mayor a 0'),
  recipient_name: z
    .string()
    .min(2, 'Nombre del destinatario requerido')
    .max(100, 'Nombre muy largo'),
  recipient_phone: mexicanPhoneSchema,
  address: AddressSchema,
  scheduled_time: z.string().optional(),
  delivery_instructions: z
    .string()
    .max(500, 'Instrucciones muy largas')
    .optional(),
  products: z.array(z.string()).default([]), // Product IDs for this stop
});

// Fiscal data schema
export const FiscalDataSchema = z.object({
  rfc: rfcSchema,
  business_name: z
    .string()
    .min(3, 'Razón social debe tener al menos 3 caracteres')
    .max(200, 'Razón social muy larga')
    .optional(),
  tax_regime: z
    .enum([
      'Régimen General de Ley Personas Morales',
      'Personas Físicas con Actividades Empresariales',
      'Régimen de Incorporación Fiscal',
      'Régimen Simplificado de Confianza',
      'Personas Físicas con Actividades Profesionales',
      'Arrendamiento',
      'Enajenación de Bienes',
      'Intereses',
      'Obtención de Premios',
      'Sin Obligaciones Fiscales',
    ])
    .optional(),
  tax_address: AddressSchema.optional(),
});

// Payment method details schema
export const PaymentMethodDetailsSchema = z.object({
  card_last_four: z
    .string()
    .regex(/^\d{4}$/, 'Últimos 4 dígitos de tarjeta inválidos')
    .optional(),
  digital_wallet_provider: z
    .enum(['paypal', 'mercado_pago', 'oxxo_pay', 'spei'])
    .optional(),
  bank_account_last_four: z
    .string()
    .regex(/^\d{4}$/, 'Últimos 4 dígitos de cuenta inválidos')
    .optional(),
});

// Time slot schema
export const TimeSlotSchema = z.enum([
  '08:00-11:00',
  '11:00-14:00',
  '14:00-17:00',
  '17:00-20:00',
  '08:00-10:00',
  '10:00-12:00',
  '12:00-14:00',
  '14:00-16:00',
  '16:00-18:00',
  '18:00-20:00',
]);

// Main order form data schema
export const OrderFormDataSchema = z.object({
  // Step 1: Customer Information
  customer_name: z
    .string()
    .min(2, 'Nombre debe tener al menos 2 caracteres')
    .max(100, 'Nombre muy largo'),
  customer_phone: mexicanPhoneSchema,
  customer_email: z.string().email('Email inválido').optional(),

  // Step 2: Delivery Address
  delivery_address: AddressSchema,

  // Step 3: Products
  products: z
    .array(ProductItemSchema)
    .min(1, 'Debe agregar al menos un producto'),

  // Step 4: Delivery Options
  delivery_date: z.string().min(1, 'Fecha de entrega requerida'),
  delivery_time_slot: TimeSlotSchema,
  delivery_mode: z.enum(['home', 'pickup_point']),

  // Step 5: Payment
  payment_method: z.enum(['card', 'cash', 'digital_wallet', 'bank_transfer']),
  invoice_required: z.boolean().default(false),

  // Step 6: Additional Notes
  special_instructions: z
    .string()
    .max(1000, 'Instrucciones muy largas')
    .optional(),
  allow_substitutions: z.boolean().default(false),

  // Step 7: Mexican Logistics
  vehicle_type_id: z.string().optional(),
  cargo_type_id: z.string().optional(),
  scheduled_pickup_time: z.string().optional(),
  scheduled_delivery_time: z.string().optional(),
  delivery_instructions: z
    .string()
    .max(500, 'Instrucciones muy largas')
    .optional(),
  special_handling_notes: z.string().max(1000, 'Notas muy largas').optional(),
  fiscal_data: FiscalDataSchema.optional(),
  payment_method_details: PaymentMethodDetailsSchema.optional(),

  // Multi-stop functionality
  stops: z.array(StopSchema).default([]),
  route_optimization: z
    .enum(['balanced', 'fastest', 'shortest', 'eco'])
    .default('balanced'),

  // Additional fields
  tracking_number: z.string().optional(),
  delivery_zone: z.string().optional(),
  delivery_region: z
    .enum(['local', 'regional', 'national', 'international'])
    .default('local'),
    
  // Step 9: Order Confirmation
  terms_accepted: z.boolean().refine(val => val === true, {
    message: 'Debes aceptar los términos y condiciones para continuar',
  }),
});

export type OrderFormData = z.infer<typeof OrderFormDataSchema>;
export type ProductItem = z.infer<typeof ProductItemSchema>;
export type Address = z.infer<typeof AddressSchema>;
export type Stop = z.infer<typeof StopSchema>;
export type FiscalData = z.infer<typeof FiscalDataSchema>;
export type SpecialHandling = z.infer<typeof SpecialHandlingSchema>;
export type Dimensions = z.infer<typeof DimensionsSchema>;
