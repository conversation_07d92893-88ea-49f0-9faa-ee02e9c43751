'use client';

import { useState, useEffect } from 'react';

interface ClientOnlyMapProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export default function ClientOnlyMap({
  children,
  fallback,
}: ClientOnlyMapProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      fallback || (
        <div className='w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2'></div>
            <p className='text-sm text-gray-600'>Cargando mapa...</p>
          </div>
        </div>
      )
    );
  }

  return <>{children}</>;
}
