// Re-export the shared Supabase client creation function
export { createClient } from '@/utils/supabase/client';

// Create and export the client instance
import { createClient as createSupabaseClient } from '@/utils/supabase/client';

export const supabase = createSupabaseClient();

// Define the Profile type locally since it's not exported from the client
export type Profile = {
  id: string;
  email: string;
  full_name: string;
  phone: string;
  role: string;
  created_at: string;
  updated_at: string;
};
