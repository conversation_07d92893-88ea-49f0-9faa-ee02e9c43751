'use client';

import { ReactNode, useEffect } from 'react';
import { useAuthStore } from '@/stores/authStore';

// Simple wrapper component that initializes the Zustand store
export function AuthProvider({ children }: { children: ReactNode }) {
  const { initialize } = useAuthStore();

  useEffect(() => {
    initialize();
  }, [initialize]);

  return <>{children}</>;
}

// Hook that provides the same interface as the old useAuth
export function useAuth() {
  const authStore = useAuthStore();

  return {
    // State
    user: authStore.user,
    session: authStore.session,
    profile: authStore.profile,
    loading: authStore.loading,
    dbError: authStore.dbError,
    isInPasswordReset: authStore.isInPasswordReset,

    // Computed values
    isAdmin: authStore.isAdmin,
    isCustomer: authStore.isCustomer,
    isDelivery: authStore.isDelivery,
    hasValidMainAppRole: authStore.hasValidMainAppRole,
    shouldRedirectToDelivery: authStore.shouldRedirectToDelivery,
    hasValidRole: authStore.hasValidRole,
    isInInvalidState: authStore.isInInvalidState,

    // Actions
    setUser: authStore.setUser,
    setSession: authStore.setSession,
    setProfile: authStore.setProfile,
    setLoading: authStore.setLoading,
    setDbError: authStore.setDbError,
    setIsInPasswordReset: authStore.setIsInPasswordReset,

    // Auth methods
    signIn: authStore.signIn,
    signUp: authStore.signUp,
    signOut: authStore.signOut,
    resetPassword: authStore.resetPassword,
    updatePassword: authStore.updatePassword,
    completeProfile: authStore.completeProfile,

    // Profile methods
    fetchProfile: authStore.fetchProfile,
    refreshAuth: authStore.refreshAuth,

    // Initialization
    initialize: authStore.initialize,
  };
}
