import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { auditLog } from '@/lib/security/audit-logger';
import { zeroTrustAuth } from '@/lib/security/zero-trust-auth';
import { generateDeviceFingerprint } from '@/lib/security/utils';

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  const next = searchParams.get('next') ?? '/dashboard';
  const clientIP =
    request.headers.get('x-forwarded-for') ||
    request.headers.get('x-real-ip') ||
    'unknown';

  if (code) {
    const supabase = await createClient();

    try {
      // Enhanced PKCE flow with Zero Trust security
      const { data: sessionData, error } =
        await supabase.auth.exchangeCodeForSession(code);

      if (!error && sessionData.session) {
        const user = sessionData.session.user;

        // Generate device fingerprint from request headers
        const deviceFingerprint = generateDeviceFingerprint(request.headers);

        // Create secure session with Zero Trust
        try {
          const secureSession = await zeroTrustAuth.createSecureSession(user, {
            fingerprint: deviceFingerprint,
            userAgent: request.headers.get('user-agent') || 'unknown',
            ipAddress: clientIP,
          });

          await auditLog({
            event: 'OAUTH_CALLBACK_SUCCESS',
            userId: user.id,
            ip: clientIP,
            userAgent: request.headers.get('user-agent') || 'unknown',
            details: {
              sessionId: secureSession.id,
              riskScore: secureSession.riskScore,
              deviceFingerprint,
              authMethod: 'oauth_pkce',
            },
            severity: 'LOW',
            category: 'AUTH',
          });
        } catch (securityError) {
          console.warn('Failed to create secure session:', securityError);
          // Continue with regular flow even if secure session creation fails
        }

        // Check if this is a password reset flow
        const type = searchParams.get('type');
        if (type === 'recovery') {
          // Enhanced password reset with security context
          const resetParams = new URLSearchParams({
            code,
            type: 'recovery',
            session_id: user.id,
            aal: 'aal1',
            expires_at: sessionData.session.expires_at?.toString() || '',
            // Add security context for enhanced validation
            device_fp: deviceFingerprint,
            client_ip: clientIP,
          });

          await auditLog({
            event: 'PASSWORD_RESET_CALLBACK',
            userId: user.id,
            ip: clientIP,
            details: {
              deviceFingerprint,
              sessionExpiry: sessionData.session.expires_at,
            },
            severity: 'MEDIUM',
            category: 'AUTH',
          });

          return NextResponse.redirect(
            `${origin}/auth/confirm?${resetParams.toString()}`
          );
        }

        // Get user profile to determine redirect path
        const { data: profile } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();

        // Role-based redirection with security logging
        let redirectPath = next;
        if (profile?.role === 'delivery') {
          redirectPath = '/delivery';
        } else if (profile?.role === 'admin') {
          redirectPath = '/admin/dashboard';
        } else if (profile?.role === 'customer') {
          redirectPath = '/customer/dashboard';
        }

        await auditLog({
          event: 'OAUTH_REDIRECT',
          userId: user.id,
          ip: clientIP,
          details: {
            role: profile?.role,
            redirectPath,
            originalNext: next,
          },
          severity: 'LOW',
          category: 'ACCESS',
        });

        return NextResponse.redirect(`${origin}${redirectPath}`);
      }

      // Enhanced error logging for security monitoring
      if (error) {
        await auditLog({
          event: 'OAUTH_CALLBACK_ERROR',
          ip: clientIP,
          userAgent: request.headers.get('user-agent') || 'unknown',
          details: {
            error: error.message,
            code: code ? 'present' : 'missing',
            origin,
            errorCode: error.status,
          },
          severity: 'HIGH',
          category: 'AUTH',
        });

        console.error('Enhanced PKCE authentication error:', {
          error: error.message,
          code: code ? 'present' : 'missing',
          origin,
          timestamp: new Date().toISOString(),
          clientIP,
          userAgent: request.headers.get('user-agent'),
        });
      }
    } catch (error) {
      // Log unexpected errors
      await auditLog({
        event: 'OAUTH_CALLBACK_EXCEPTION',
        ip: clientIP,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          code: code ? 'present' : 'missing',
        },
        severity: 'CRITICAL',
        category: 'SYSTEM',
      });

      console.error('OAuth callback exception:', error);
    }
  } else {
    // Log missing code parameter
    await auditLog({
      event: 'OAUTH_CALLBACK_MISSING_CODE',
      ip: clientIP,
      userAgent: request.headers.get('user-agent') || 'unknown',
      details: { origin },
      severity: 'MEDIUM',
      category: 'AUTH',
    });
  }

  // Return the user to an error page with instructions
  return NextResponse.redirect(`${origin}/auth/auth-code-error`);
}
