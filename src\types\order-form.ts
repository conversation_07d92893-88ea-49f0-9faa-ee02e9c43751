// Production-ready Mexican Logistics Order Types
export interface ProductItem {
  id: string;
  name: string;
  quantity: number;
  unit_measure:
    | 'kg'
    | 'g'
    | 'pieza'
    | 'paquete'
    | 'litro'
    | 'ml'
    | 'unidad'
    | 'caja'
    | 'bulto';
  unit_price: number;
  subtotal: number;
  weight: number;
  weight_unit: 'kg' | 'g';
  dimensions: {
    length: number;
    width: number;
    height: number;
    unit: 'cm' | 'm';
  };
  special_handling: {
    fragile: boolean;
    perishable: boolean;
    valuable: boolean;
    hazardous: boolean;
    refrigerated: boolean;
    oversized: boolean;
  };
  notes?: string;
  // Production fields
  sku?: string;
  category?: string;
  insurance_required?: boolean;
  temperature_range?: {
    min: number;
    max: number;
    unit: 'C' | 'F';
  };
}

export interface DeliveryAddress {
  id: string;
  street: string;
  number?: string;
  colony: string;
  city: string;
  state:
    | 'Aguascalientes'
    | 'Baja California'
    | 'Baja California Sur'
    | 'Campeche'
    | 'Chiapas'
    | 'Chihuahua'
    | 'Ciudad de México'
    | 'Coahuila'
    | 'Colima'
    | 'Durango'
    | 'Estado de México'
    | 'Guanajuato'
    | 'Guerrero'
    | 'Hidalgo'
    | 'Jalisco'
    | 'Michoacán'
    | 'Morelos'
    | 'Nayarit'
    | 'Nuevo León'
    | 'Oaxaca'
    | 'Puebla'
    | 'Querétaro'
    | 'Quintana Roo'
    | 'San Luis Potosí'
    | 'Sinaloa'
    | 'Sonora'
    | 'Tabasco'
    | 'Tamaulipas'
    | 'Tlaxcala'
    | 'Veracruz'
    | 'Yucatán'
    | 'Zacatecas';
  zip: string;
  references?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  // Production fields
  validated?: boolean;
  delivery_zone?: string;
  access_instructions?: string;
  business_hours?: {
    open: string;
    close: string;
    days: string[];
  };
}

// Mexican Logistics Interfaces
export interface FiscalData {
  rfc?: string;
  business_name?: string;
  tax_regime?: string;
}

export interface PaymentMethodDetails {
  card_last_four?: string;
  digital_wallet_provider?: string;
  bank_account_last_four?: string;
}

export interface Stop {
  id: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  scheduled_time?: string;
  instructions?: string;
}

// Production Order Tracking Types
export interface OrderTracking {
  id: string;
  order_id: string;
  status:
    | 'created'
    | 'confirmed'
    | 'picked_up'
    | 'in_transit'
    | 'out_for_delivery'
    | 'delivered'
    | 'failed'
    | 'cancelled';
  location?: {
    lat: number;
    lng: number;
    address: string;
  };
  timestamp: string;
  notes?: string;
  driver_id?: string;
  vehicle_id?: string;
  proof_of_delivery?: {
    signature?: string;
    photo?: string;
    recipient_name?: string;
    delivery_time?: string;
  };
}

export interface OrderFormData {
  // Step 1: Sender Information
  customer_name: string;
  customer_phone: string;
  customer_email?: string;
  customer_type: 'individual' | 'business';
  pickup_address: DeliveryAddress;
  delivery_address: DeliveryAddress;

  // Step 2: Merchandise Details
  products: ProductItem[];

  // Step 3: Vehicle Selection (auto-populated based on products)
  vehicle_type_id?: string;
  vehicle_suggestions?: string[];

  // Step 4: Multi-Destination Management
  delivery_mode: 'home' | 'pickup_point';
  stops: Stop[];

  // Step 5: Delivery Scheduling
  delivery_date: string;
  delivery_time_slot:
    | '08:00-11:00'
    | '11:00-14:00'
    | '14:00-17:00'
    | '17:00-20:00';
  pickup_time_slot?:
    | '08:00-11:00'
    | '11:00-14:00'
    | '14:00-17:00'
    | '17:00-20:00';

  // Step 6: Payment and Fiscal
  payment_method: 'card' | 'cash' | 'digital_wallet' | 'bank_transfer';
  invoice_required: boolean;
  fiscal_data?: FiscalData;
  payment_method_details?: PaymentMethodDetails;

  // Step 7: Operational Instructions
  special_instructions?: string;
  allow_substitutions: boolean;
  communication_preferences?: {
    sms_notifications: boolean;
    email_notifications: boolean;
    whatsapp_notifications: boolean;
    call_before_delivery: boolean;
  };

  // Step 8: Mexican Logistics Configuration
  cargo_type_id?: string;
  route_optimization: 'balanced' | 'fastest' | 'shortest' | 'eco';
  delivery_region: 'local' | 'regional' | 'national' | 'international';
  regulatory_requirements?: {
    hazmat_permit?: boolean;
    oversized_permit?: boolean;
    refrigeration_required?: boolean;
  };

  // Additional Mexican Logistics fields
  scheduled_pickup_time?: string;
  scheduled_delivery_time?: string;
  delivery_instructions?: string;
  special_handling_notes?: string;
  delivery_zone?: string;

  // Step 9: Order Confirmation (auto-calculated)
  total_cost?: number;
  estimated_delivery_time?: string;
  tracking_number?: string;
  terms_accepted?: boolean;
}

export interface OrderFormProps {
  initialData?: OrderFormData;
  onSubmit: (_data: OrderFormData) => void;
  onCancel?: () => void;
  loading?: boolean;
  errors?: Record<string, string>;
}
