export interface ProductItem {
  id: string;
  name: string;
  quantity: number;
  unit_measure: string;
  unit_price: number;
  subtotal: number;
  notes?: string;
}

export interface DeliveryAddress {
  id: string;
  street: string;
  number?: string;
  colony: string;
  city: string;
  state: string;
  zip: string;
  references?: string;
}

// Mexican Logistics Interfaces
export interface FiscalData {
  rfc?: string;
  business_name?: string;
  tax_regime?: string;
}

export interface PaymentMethodDetails {
  card_last_four?: string;
  digital_wallet_provider?: string;
  bank_account_last_four?: string;
}

export interface Stop {
  id: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  scheduled_time?: string;
  instructions?: string;
}

export interface OrderFormData {
  // Customer Information
  customer_name: string;
  customer_phone: string;
  customer_email?: string;

  // Delivery Address
  delivery_address: DeliveryAddress;

  // Shopping Cart
  products: ProductItem[];

  // Delivery Options
  delivery_date: string;
  delivery_time_slot: string;
  delivery_mode: 'home' | 'store';

  // Payment
  payment_method: 'card' | 'cash' | 'digital_wallet' | 'bank_transfer';
  invoice_required: boolean;

  // Additional Notes
  special_instructions?: string;
  allow_substitutions: boolean;

  // Mexican Logistics Fields
  vehicle_type_id?: string;
  cargo_type_id?: string;
  scheduled_pickup_time?: string;
  scheduled_delivery_time?: string;
  delivery_instructions?: string;
  special_handling_notes?: string;
  fiscal_data?: FiscalData;
  payment_method_details?: PaymentMethodDetails;
  stops?: Stop[];
  route_optimization?: 'balanced' | 'fastest' | 'shortest' | 'eco';
  tracking_number?: string;
  delivery_zone?: string;
  delivery_region?: 'local' | 'regional' | 'national' | 'international';
}

export interface OrderFormProps {
  initialData?: OrderFormData;
  onSubmit: (_data: OrderFormData) => void;
  onCancel?: () => void;
  loading?: boolean;
}
