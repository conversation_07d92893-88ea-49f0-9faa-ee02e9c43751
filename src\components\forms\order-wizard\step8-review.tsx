'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { OrderFormData } from '@/types/order-form';

interface Step8Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
}

export function OrderWizardStep8({ formData }: Step8Props) {
  // Calculate total cost
  const totalCost = formData.products.reduce(
    (sum, product) => sum + product.subtotal,
    0
  );

  return (
    <div className='space-y-6'>
      {/* Order Summary */}
      <Card className='bg-blue-50 border-blue-200'>
        <CardHeader>
          <CardTitle className='flex items-center gap-2 text-blue-800'>
            ✅ Resumen del Pedido
          </CardTitle>
          <CardDescription className='text-blue-700'>
            Revisa los detalles antes de confirmar
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <h4 className='font-medium text-blue-800'>Cliente</h4>
              <p className='text-sm text-blue-700'>{formData.customer_name}</p>
              <p className='text-sm text-blue-700'>{formData.customer_phone}</p>
              {formData.customer_email && (
                <p className='text-sm text-blue-700'>
                  {formData.customer_email}
                </p>
              )}
            </div>
            <div className='space-y-2'>
              <h4 className='font-medium text-blue-800'>Entrega</h4>
              <p className='text-sm text-blue-700'>
                {formData.delivery_address.street}{' '}
                {formData.delivery_address.number}
              </p>
              <p className='text-sm text-blue-700'>
                {formData.delivery_address.colony},{' '}
                {formData.delivery_address.city},{' '}
                {formData.delivery_address.state}
              </p>
              <p className='text-sm text-blue-700'>
                CP: {formData.delivery_address.zip}
              </p>
            </div>
          </div>

          <div className='border-t border-blue-300 pt-4'>
            <div className='flex justify-between items-center'>
              <span className='font-medium text-blue-800'>
                Total del Pedido:
              </span>
              <span className='text-2xl font-bold text-blue-800'>
                ${totalCost.toFixed(2)} MXN
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Products Summary */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            🛒 Productos
          </CardTitle>
          <CardDescription>Lista de productos en tu pedido</CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          {formData.products.map(product => (
            <div
              key={product.id}
              className='flex justify-between items-center border-b pb-2'
            >
              <div>
                <p className='font-medium'>{product.name}</p>
                <p className='text-sm text-gray-600'>
                  {product.quantity} {product.unit_measure} @ $
                  {product.unit_price.toFixed(2)} c/u
                </p>
              </div>
              <p className='font-medium'>${product.subtotal.toFixed(2)}</p>
            </div>
          ))}
          <div className='flex justify-between items-center pt-2'>
            <span className='font-medium'>Total:</span>
            <span className='text-lg font-bold'>
              ${totalCost.toFixed(2)} MXN
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Delivery Options Summary */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            ⏰ Opciones de Entrega
          </CardTitle>
          <CardDescription>Detalles de entrega</CardDescription>
        </CardHeader>
        <CardContent className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <h4 className='font-medium'>Fecha y Hora</h4>
            <p className='text-sm text-gray-600'>
              {formData.delivery_date} - {formData.delivery_time_slot}
            </p>
          </div>
          <div className='space-y-2'>
            <h4 className='font-medium'>Modalidad</h4>
            <p className='text-sm text-gray-600'>
              {formData.delivery_mode === 'home'
                ? 'Entrega a domicilio'
                : 'Recoger en tienda'}
            </p>
          </div>
          {formData.special_instructions && (
            <div className='space-y-2 md:col-span-2'>
              <h4 className='font-medium'>Instrucciones Especiales</h4>
              <p className='text-sm text-gray-600'>
                {formData.special_instructions}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Summary */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            💳 Pago y Facturación
          </CardTitle>
          <CardDescription>Información de pago</CardDescription>
        </CardHeader>
        <CardContent className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <h4 className='font-medium'>Método de Pago</h4>
            <p className='text-sm text-gray-600'>
              {formData.payment_method === 'card' &&
                'Tarjeta de crédito/débito'}
              {formData.payment_method === 'cash' && 'Efectivo contra entrega'}
              {formData.payment_method === 'digital_wallet' &&
                'Billetera digital'}
              {formData.payment_method === 'bank_transfer' &&
                'Transferencia bancaria'}
            </p>
          </div>
          <div className='space-y-2'>
            <h4 className='font-medium'>Factura</h4>
            <p className='text-sm text-gray-600'>
              {formData.invoice_required ? 'Sí' : 'No'}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Mexican Logistics Summary (if any fields are filled) */}
      {(formData.vehicle_type_id ||
        formData.cargo_type_id ||
        formData.scheduled_pickup_time ||
        formData.scheduled_delivery_time ||
        formData.delivery_instructions ||
        formData.special_handling_notes ||
        formData.fiscal_data?.rfc) && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              🚚 Logística Mexicana
            </CardTitle>
            <CardDescription>Detalles de logística avanzada</CardDescription>
          </CardHeader>
          <CardContent className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            {formData.vehicle_type_id && (
              <div className='space-y-2'>
                <h4 className='font-medium'>Tipo de Vehículo</h4>
                <p className='text-sm text-gray-600'>
                  {formData.vehicle_type_id}
                </p>
              </div>
            )}
            {formData.cargo_type_id && (
              <div className='space-y-2'>
                <h4 className='font-medium'>Tipo de Carga</h4>
                <p className='text-sm text-gray-600'>
                  {formData.cargo_type_id}
                </p>
              </div>
            )}
            {formData.scheduled_pickup_time && (
              <div className='space-y-2'>
                <h4 className='font-medium'>Recolección Programada</h4>
                <p className='text-sm text-gray-600'>
                  {new Date(formData.scheduled_pickup_time).toLocaleString(
                    'es-MX'
                  )}
                </p>
              </div>
            )}
            {formData.scheduled_delivery_time && (
              <div className='space-y-2'>
                <h4 className='font-medium'>Entrega Programada</h4>
                <p className='text-sm text-gray-600'>
                  {new Date(formData.scheduled_delivery_time).toLocaleString(
                    'es-MX'
                  )}
                </p>
              </div>
            )}
            {formData.fiscal_data?.rfc && (
              <div className='space-y-2 md:col-span-2'>
                <h4 className='font-medium'>Datos Fiscales</h4>
                <p className='text-sm text-gray-600'>
                  RFC: {formData.fiscal_data.rfc}, Razón Social:{' '}
                  {formData.fiscal_data.business_name}, Régimen:{' '}
                  {formData.fiscal_data.tax_regime}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
