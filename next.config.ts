import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  output: 'standalone',
  typedRoutes: true, // Enable typed routes for type safety
  typescript: {
    // Allow build to continue with TypeScript warnings
    ignoreBuildErrors: false,
  },
  eslint: {
    // Allow build to continue with ESLint warnings
    ignoreDuringBuilds: false,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },
  webpack: config => {
    // Add SVG handling rule for webpack builds (production/CI)
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
      type: 'javascript/auto',
    });

    return config;
  },
};

export default nextConfig;
