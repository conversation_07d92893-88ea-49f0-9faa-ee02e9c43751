'use client';

import { useState, useEffect, useCallback } from 'react';
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { useAuthStore } from '@/stores/authStore';
import CompleteProfileModal from '../auth/CompleteProfileModal';
import OrderDetailsModal from './OrderDetailsModal';
import { Order } from '@/delivery/types/order';
import { createClient } from '@/utils/supabase/client';

const supabase = createClient();

interface Notification {
  type: 'success' | 'error' | 'info';
  message: string;
  orderId?: string;
}

export default function Dashboard() {
  const { user, profile, signOut, loading } = useAuthStore();
  const [orders, setOrders] = useState<Order[]>([]);
  const [ordersLoading, setOrdersLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [notification, setNotification] = useState<Notification | null>(null);

  const showStatusUpdateNotification = useCallback(
    (order: Order, oldStatus: string, newStatus: string) => {
      let message = '';
      let type: 'success' | 'info' = 'info';

      switch (newStatus) {
        case 'confirmed':
          message = `✅ Pedido #${order.id.slice(0, 8)} confirmado por administración`;
          type = 'success';
          break;
        case 'in-transit':
          message = `🚚 Pedido #${order.id.slice(0, 8)} marcado como enviado`;
          type = 'info';
          break;
        case 'delivered':
          message = `🎉 Pedido #${order.id.slice(0, 8)} marcado como entregado`;
          type = 'success';
          break;
        case 'cancelled':
          message = `❌ Pedido #${order.id.slice(0, 8)} cancelado`;
          type = 'info';
          break;
        default:
          message = `📝 Estado del pedido #${order.id.slice(0, 8)} actualizado a: ${newStatus}`;
          type = 'info';
      }

      setNotification({
        type,
        message,
        orderId: order.id,
      });

      // Auto-hide notification after 5 seconds
      setTimeout(() => setNotification(null), 5000);
    },
    []
  );

  const handleRealtimeOrderUpdate = useCallback(
    (payload: RealtimePostgresChangesPayload<Record<string, unknown>>) => {
      const updatedOrder = payload.new as Order;
      const oldOrder = payload.old as Partial<Order> | null;

      setOrders(prevOrders => {
        const orderIndex = prevOrders.findIndex(o => o.id === updatedOrder.id);

        if (orderIndex === -1) {
          // New order assigned
          if (updatedOrder.delivery_id === user?.id) {
            return [...prevOrders, updatedOrder];
          }
          return prevOrders;
        }

        // Update existing order
        const newOrders = [...prevOrders];
        newOrders[orderIndex] = updatedOrder;

        // Check if status changed and show notification
        if (
          oldOrder &&
          oldOrder.status &&
          oldOrder.status !== updatedOrder.status
        ) {
          showStatusUpdateNotification(
            updatedOrder,
            oldOrder.status,
            updatedOrder.status
          );
        }

        return newOrders;
      });
    },
    [user, showStatusUpdateNotification]
  );

  const setupRealtimeSubscription = useCallback(() => {
    if (!user) return () => {};

    console.log('Setting up realtime subscription for user:', user.id);

    const ordersSubscription = supabase
      .channel('orders_changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'orders',
          filter: `delivery_id=eq.${user.id}`,
        },
        payload => {
          console.log('Realtime UPDATE received:', payload);
          handleRealtimeOrderUpdate(payload);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'orders',
          filter: `delivery_id=eq.${user.id}`,
        },
        payload => {
          console.log('Realtime INSERT received:', payload);
          handleRealtimeOrderUpdate(payload);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'orders',
          filter: `delivery_id=is.null`,
        },
        payload => {
          console.log('Realtime UPDATE (available order) received:', payload);
          handleRealtimeOrderUpdate(payload);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'orders',
          filter: `delivery_id=is.null`,
        },
        payload => {
          console.log('Realtime INSERT (available order) received:', payload);
          handleRealtimeOrderUpdate(payload);
        }
      )
      .subscribe(status => {
        console.log('Realtime subscription status:', status);
      });

    return () => {
      console.log('Cleaning up realtime subscription');
      supabase.removeChannel(ordersSubscription);
    };
  }, [user, handleRealtimeOrderUpdate]);

  const loadOrders = useCallback(async () => {
    if (!user) {
      console.log('No user found, skipping loadOrders');
      return;
    }

    console.log('Loading orders for user:', user.id);
    setOrdersLoading(true);

    try {
      // Load orders that the delivery user can see (assigned to them or available for assignment)
      const { data: orders, error } = await supabase
        .from('orders')
        .select(
          `
          *,
          customer:profiles!orders_customer_id_fkey(
            id,
            full_name,
            email,
            phone
          )
        `
        )
        .or(`delivery_id.eq.${user.id},delivery_id.is.null`)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading orders:', error);
        console.error('Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
        });
        setNotification({
          type: 'error',
          message: `Error al cargar órdenes: ${error.message}`,
        });
        return;
      }

      console.log('Orders loaded successfully:', orders?.length || 0, 'orders');
      setOrders(orders || []);
    } catch (error) {
      console.error('Exception loading orders:', error);
      console.error('Exception details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      setNotification({
        type: 'error',
        message: `Error inesperado al cargar órdenes: ${error instanceof Error ? error.message : 'Error desconocido'}`,
      });
    } finally {
      setOrdersLoading(false);
    }
  }, [user]);

  // Load orders on component mount
  useEffect(() => {
    console.log(
      'Dashboard useEffect triggered, user:',
      user?.id,
      'loading:',
      loading
    );

    if (user && !loading) {
      console.log('User authenticated, loading orders and setting up realtime');
      loadOrders();
      const cleanup = setupRealtimeSubscription();
      return cleanup;
    } else if (loading) {
      console.log('Still loading authentication...');
    } else {
      console.log('No user or still loading, skipping order load');
    }
  }, [user, loading, loadOrders, setupRealtimeSubscription]);

  // Cleanup subscription on unmount
  useEffect(() => {
    return () => {
      // Cleanup will be handled in the subscription setup
    };
  }, []);

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch {
      // Silent error handling for security
    }
  };

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowOrderModal(true);
  };

  const handleTakeOrder = async (orderId: string) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('orders')
        .update({ delivery_id: user.id })
        .eq('id', orderId);

      if (error) {
        // Silent error handling for security
        setNotification({
          type: 'error',
          message: 'Error al tomar la orden',
        });
        return;
      }

      // Update local state
      setOrders(prev =>
        prev.map(order =>
          order.id === orderId ? { ...order, delivery_id: user.id } : order
        )
      );

      setNotification({
        type: 'success',
        message: 'Orden tomada exitosamente',
      });

      // Reload orders to get fresh data
      loadOrders();
    } catch {
      // Silent error handling for security
      setNotification({
        type: 'error',
        message: 'Error al tomar la orden',
      });
    }
  };

  const handleUpdateStatus = (orderId: string) => {
    const order = orders.find(o => o.id === orderId);
    if (order) {
      setSelectedOrder(order);
      setShowOrderModal(true);
    }
  };

  const handleQuickContact = (order: Order) => {
    setSelectedOrder(order);
    setShowOrderModal(true);
  };

  const handleStatusUpdate = async (
    orderId: string,
    newStatus:
      | 'pending'
      | 'confirmed'
      | 'in-transit'
      | 'pending-admin-confirmation'
      | 'delivered'
      | 'closed'
      | 'cancelled'
  ) => {
    try {
      const response = await fetch('/api/orders/update-status', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId,
          newStatus,
        }),
      });

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch {
          // Silent error handling for security
          const textResponse = await response.text();
          throw new Error(
            `HTTP ${response.status}: ${textResponse.substring(0, 100)}`
          );
        }
        throw new Error(
          errorData.error ||
            `HTTP ${response.status}: Error al actualizar el estado del pedido`
        );
      }

      // Update local state
      setOrders(prev =>
        prev.map(order =>
          order.id === orderId
            ? {
                ...order,
                status: newStatus,
                updated_at: new Date().toISOString(),
              }
            : order
        )
      );

      // Show success notification
      setNotification({
        type: 'success',
        message: `Estado de la orden actualizado a: ${newStatus}`,
      });

      // Auto-hide notification after 5 seconds
      setTimeout(() => setNotification(null), 5000);
    } catch (error) {
      // Silent error handling for security

      // Show error notification
      setNotification({
        type: 'error',
        message:
          error instanceof Error
            ? error.message
            : 'Error al actualizar el estado',
      });

      // Auto-hide notification after 5 seconds
      setTimeout(() => setNotification(null), 5000);

      throw error;
    }
  };

  // Check if profile is complete
  useEffect(() => {
    if (profile && (!profile.full_name || !profile.phone)) {
      setShowProfileModal(true);
    }
  }, [profile]);

  if (!user) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <p className='text-gray-600'>Cargando...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Enhanced Notification System */}
      {notification && (
        <div
          className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-md ${
            notification.type === 'success'
              ? 'bg-green-500 text-white'
              : notification.type === 'error'
                ? 'bg-red-500 text-white'
                : 'bg-blue-500 text-white'
          }`}
        >
          <div className='flex items-start space-x-3'>
            <div className='flex-shrink-0'>
              {notification.type === 'success' && (
                <svg
                  className='h-5 w-5'
                  fill='currentColor'
                  viewBox='0 0 20 20'
                >
                  <path
                    fillRule='evenodd'
                    d='M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z'
                    clipRule='evenodd'
                  />
                </svg>
              )}
              {notification.type === 'error' && (
                <svg
                  className='h-5 w-5'
                  fill='currentColor'
                  viewBox='0 0 20 20'
                >
                  <path
                    fillRule='evenodd'
                    d='M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z'
                    clipRule='evenodd'
                  />
                </svg>
              )}
              {notification.type === 'info' && (
                <svg
                  className='h-5 w-5'
                  fill='currentColor'
                  viewBox='0 0 20 20'
                >
                  <path
                    fillRule='evenodd'
                    d='M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z'
                    clipRule='evenodd'
                  />
                </svg>
              )}
            </div>
            <div className='flex-1'>
              <p className='text-sm font-medium'>{notification.message}</p>
              {notification.orderId && (
                <p className='text-xs opacity-75 mt-1'>
                  ID: {notification.orderId.slice(0, 8)}
                </p>
              )}
            </div>
            <button
              onClick={() => setNotification(null)}
              className='ml-2 text-white hover:text-gray-200 flex-shrink-0'
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Header */}
      <header className='bg-white shadow-sm border-b border-gray-200'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center py-4'>
            <div className='flex items-center space-x-4'>
              <h1 className='text-2xl font-bold text-gray-900'>Mouvers</h1>
              <span className='text-lg font-medium text-gray-600'>
                Repartidor
              </span>
            </div>
            <div className='flex items-center space-x-4'>
              <span className='text-gray-700 font-medium'>
                {profile?.full_name || user?.email}
              </span>
              <button
                onClick={handleSignOut}
                className='px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors'
              >
                Cerrar Sesión
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8'>
        <div className='space-y-6 sm:space-y-8'>
          {/* Estadísticas */}
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6'>
            <div className='bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6'>
              <div className='text-center'>
                <div className='text-2xl sm:text-3xl font-bold text-gray-900 mb-2'>
                  {orders.filter(o => o.delivery_id).length}
                </div>
                <div className='text-gray-600 text-sm sm:text-base'>
                  {orders.filter(o => o.delivery_id).length === 0
                    ? 'Aún no hay pedidos'
                    : 'Pedidos Asignados'}
                </div>
              </div>
            </div>

            <div className='bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6'>
              <div className='text-center'>
                <div className='text-2xl sm:text-3xl font-bold text-gray-900 mb-2'>
                  {orders.filter(o => o.status === 'in-transit').length}
                </div>
                <div className='text-gray-600 text-sm sm:text-base'>
                  {orders.filter(o => o.status === 'in-transit').length === 0
                    ? 'No hay entregas activas'
                    : 'Entregas Activas'}
                </div>
              </div>
            </div>

            <div className='bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6'>
              <div className='text-center'>
                <div className='text-2xl sm:text-3xl font-bold text-gray-900 mb-2'>
                  {orders.filter(o => o.status === 'delivered').length}
                </div>
                <div className='text-gray-600 text-sm sm:text-base'>
                  Entregas Completadas
                </div>
              </div>
            </div>

            <div className='bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6'>
              <div className='text-center'>
                <div className='text-xs sm:text-sm text-gray-500 mb-1'>
                  Este Mes
                </div>
                <div className='text-2xl sm:text-3xl font-bold text-gray-900 mb-2'>
                  {
                    orders.filter(
                      o =>
                        new Date(o.created_at).getMonth() ===
                        new Date().getMonth()
                    ).length
                  }
                </div>
                <div className='text-gray-600 text-sm sm:text-base'>
                  Pedidos del mes
                </div>
              </div>
            </div>
          </div>

          {/* Información del Usuario */}
          <div className='bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6 mb-6 sm:mb-8'>
            <h3 className='text-lg sm:text-xl font-semibold text-gray-900 mb-4'>
              Mi Perfil
            </h3>
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6'>
              <div className='bg-gray-50 rounded-lg p-3 sm:p-4'>
                <span className='text-gray-600 font-medium text-xs sm:text-sm'>
                  Email
                </span>
                <p className='text-gray-900 font-semibold text-sm sm:text-base'>
                  {user?.email}
                </p>
              </div>
              <div className='bg-gray-50 rounded-lg p-3 sm:p-4'>
                <span className='text-gray-600 font-medium text-xs sm:text-sm'>
                  Nombre
                </span>
                <p className='text-gray-900 font-semibold text-sm sm:text-base'>
                  {profile?.full_name || 'No especificado'}
                </p>
              </div>
              <div className='bg-gray-50 rounded-lg p-3 sm:p-4'>
                <span className='text-gray-600 font-medium text-xs sm:text-sm'>
                  Teléfono
                </span>
                <p className='text-gray-900 font-semibold text-sm sm:text-base'>
                  {profile?.phone || 'No especificado'}
                </p>
              </div>
            </div>
          </div>

          {/* Tabla de Órdenes */}
          <div className='bg-white rounded-xl shadow-sm border border-gray-200'>
            <div className='px-4 sm:px-6 py-4 border-b border-gray-200'>
              <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0'>
                <h3 className='text-lg font-semibold text-gray-900'>
                  Mis Órdenes de Entrega
                </h3>
                <button
                  onClick={loadOrders}
                  disabled={ordersLoading}
                  className='w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors text-sm'
                >
                  {ordersLoading ? 'Cargando...' : 'Actualizar'}
                </button>
              </div>
            </div>

            <div className='overflow-x-auto'>
              {ordersLoading ? (
                <div className='p-6 text-center'>
                  <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto'></div>
                  <p className='mt-2 text-gray-600'>Cargando órdenes...</p>
                </div>
              ) : orders.length === 0 ? (
                <div className='p-6 text-center text-gray-500'>
                  <p>No tienes órdenes asignadas actualmente.</p>
                </div>
              ) : (
                <table className='min-w-full divide-y divide-gray-200'>
                  <thead className='bg-gray-50'>
                    <tr>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        ID
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Cliente
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Estado
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Pago
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Asignación
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Fecha
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Acciones
                      </th>
                    </tr>
                  </thead>
                  <tbody className='bg-white divide-y divide-gray-200'>
                    {orders.map(order => (
                      <tr
                        key={order.id}
                        className='hover:bg-gray-50 transition-colors'
                      >
                        <td className='px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900'>
                          #{order.id.slice(0, 8)}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                          {order.customer?.full_name || 'N/A'}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap'>
                          <span
                            className={`px-2 py-1 text-xs font-medium rounded-full ${
                              order.status === 'pending'
                                ? 'bg-yellow-100 text-yellow-800'
                                : order.status === 'confirmed'
                                  ? 'bg-blue-100 text-blue-800'
                                  : order.status === 'in-transit'
                                    ? 'bg-purple-100 text-purple-800'
                                    : order.status ===
                                        'pending-admin-confirmation'
                                      ? 'bg-orange-100 text-orange-800'
                                      : order.status === 'delivered'
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-gray-100 text-gray-800'
                            }`}
                          >
                            {order.status === 'pending-admin-confirmation'
                              ? 'Pendiente Confirmación'
                              : order.status === 'confirmed'
                                ? 'Confirmado'
                                : order.status === 'in-transit'
                                  ? 'En Tránsito'
                                  : order.status === 'delivered'
                                    ? 'Entregado'
                                    : order.status}
                          </span>
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap'>
                          <span
                            className={`px-2 py-1 text-xs font-medium rounded-full ${
                              order.payment_status === 'pending'
                                ? 'bg-yellow-100 text-yellow-800'
                                : order.payment_status === 'paid'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-red-100 text-red-800'
                            }`}
                          >
                            {order.payment_status === 'paid'
                              ? 'Pagado'
                              : order.payment_status}
                          </span>
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap'>
                          {order.delivery_id ? (
                            <span className='px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800'>
                              Asignada a ti
                            </span>
                          ) : (
                            <span className='px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800'>
                              Disponible
                            </span>
                          )}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                          {new Date(order.created_at).toLocaleDateString(
                            'es-ES'
                          )}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm font-medium'>
                          <div className='flex space-x-2'>
                            <button
                              onClick={() => handleViewOrder(order)}
                              className='text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 px-3 py-1 rounded-md transition-colors'
                            >
                              Ver Detalles
                            </button>
                            {!order.delivery_id && (
                              <button
                                onClick={() => handleTakeOrder(order.id)}
                                className='text-green-600 hover:text-green-900 bg-green-50 hover:bg-green-100 px-3 py-1 rounded-md transition-colors'
                              >
                                Tomar
                              </button>
                            )}
                            {order.delivery_id &&
                              order.status !== 'delivered' && (
                                <button
                                  onClick={() => handleUpdateStatus(order.id)}
                                  className='text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 px-3 py-1 rounded-md transition-colors'
                                  disabled={
                                    order.status === 'confirmed' ||
                                    order.status === 'in-transit'
                                  }
                                  title={
                                    order.status === 'confirmed' ||
                                    order.status === 'in-transit'
                                      ? 'No puedes modificar órdenes confirmadas por administración'
                                      : 'Actualizar estado de la orden'
                                  }
                                >
                                  {order.status === 'confirmed' ||
                                  order.status === 'in-transit'
                                    ? 'Bloqueado por Admin'
                                    : 'Actualizar Estado'}
                                </button>
                              )}
                            {order.customer && (
                              <button
                                onClick={() => handleQuickContact(order)}
                                className='text-green-600 hover:text-green-900 bg-green-50 hover:bg-green-100 px-3 py-1 rounded-md transition-colors'
                                title='Contactar al cliente'
                              >
                                📞 Contactar
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>
        </div>
      </main>

      {/* Modal para completar perfil */}
      <CompleteProfileModal
        isOpen={showProfileModal}
        onClose={() => setShowProfileModal(false)}
      />

      {/* Modal para detalles de orden */}
      {selectedOrder && (
        <OrderDetailsModal
          isOpen={showOrderModal}
          onClose={() => setShowOrderModal(false)}
          order={selectedOrder}
          onStatusUpdate={handleStatusUpdate}
        />
      )}
    </div>
  );
}
