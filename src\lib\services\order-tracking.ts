/**
 * Production Order Tracking Service
 *
 * Real-time order tracking with WebSocket integration for Mexican logistics
 */

import { createClient } from '@/utils/supabase/client';
import { OrderTracking } from '@/types/order-form';
import { Order } from '@/delivery/types/order';

export interface TrackingUpdate {
  order_id: string;
  status: OrderTracking['status'];
  location?: {
    lat: number;
    lng: number;
    address: string;
  };
  estimated_arrival?: string;
  driver_info?: {
    name: string;
    phone: string;
    vehicle_info: string;
  };
  notes?: string;
}

export interface TrackingSubscription {
  unsubscribe: () => void;
}

export class OrderTrackingService {
  private static supabase = createClient();
  private static activeSubscriptions = new Map<string, TrackingSubscription>();

  /**
   * Get complete tracking history for an order
   */
  static async getOrderTracking(orderId: string): Promise<{
    success: boolean;
    data?: OrderTracking[];
    error?: string;
  }> {
    try {
      const { data, error } = await this.supabase
        .from('order_tracking')
        .select('*')
        .eq('order_id', orderId)
        .order('timestamp', { ascending: true });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data: data || [] };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get current order status and location
   */
  static async getCurrentStatus(orderId: string): Promise<{
    success: boolean;
    data?: {
      status: OrderTracking['status'];
      location?: OrderTracking['location'];
      estimated_arrival?: string;
      driver_info?: {
        full_name: string;
        phone: string;
        vehicle_info: string;
      };
      last_update: string;
    };
    error?: string;
  }> {
    try {
      const { data, error } = await this.supabase
        .from('order_tracking')
        .select(
          `
          *,
          drivers (
            full_name,
            phone,
            vehicle_info
          )
        `
        )
        .eq('order_id', orderId)
        .order('timestamp', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return {
        success: true,
        data: {
          status: data.status,
          location: data.location,
          estimated_arrival: data.estimated_arrival,
          driver_info: data.drivers,
          last_update: data.timestamp,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Subscribe to real-time tracking updates for an order
   */
  static subscribeToOrderUpdates(
    orderId: string,
    callback: (update: TrackingUpdate) => void
  ): TrackingSubscription {
    // Unsubscribe from any existing subscription for this order
    this.unsubscribeFromOrder(orderId);

    const subscription = this.supabase
      .channel(`order-tracking-${orderId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'order_tracking',
          filter: `order_id=eq.${orderId}`,
        },
        payload => {
          const trackingData = payload.new as OrderTracking;
          callback({
            order_id: trackingData.order_id,
            status: trackingData.status,
            location: trackingData.location,
            notes: trackingData.notes,
          });
        }
      )
      .subscribe();

    const trackingSubscription: TrackingSubscription = {
      unsubscribe: () => {
        subscription.unsubscribe();
        this.activeSubscriptions.delete(orderId);
      },
    };

    this.activeSubscriptions.set(orderId, trackingSubscription);
    return trackingSubscription;
  }

  /**
   * Unsubscribe from order tracking updates
   */
  static unsubscribeFromOrder(orderId: string): void {
    const subscription = this.activeSubscriptions.get(orderId);
    if (subscription) {
      subscription.unsubscribe();
    }
  }

  /**
   * Update order tracking status (for drivers/admin)
   */
  static async updateOrderStatus(
    orderId: string,
    status: OrderTracking['status'],
    location?: OrderTracking['location'],
    notes?: string,
    driverId?: string,
    vehicleId?: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const trackingUpdate: Partial<OrderTracking> = {
        order_id: orderId,
        status,
        location,
        notes,
        driver_id: driverId,
        vehicle_id: vehicleId,
        timestamp: new Date().toISOString(),
      };

      const { error } = await this.supabase
        .from('order_tracking')
        .insert(trackingUpdate);

      if (error) {
        return { success: false, error: error.message };
      }

      // Also update the main order status
      await this.supabase
        .from('orders')
        .update({
          status: this.mapTrackingStatusToOrderStatus(status),
          updated_at: new Date().toISOString(),
        })
        .eq('id', orderId);

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Add proof of delivery
   */
  static async addProofOfDelivery(
    orderId: string,
    proof: {
      signature?: string;
      photo?: string;
      recipient_name: string;
      delivery_time: string;
      notes?: string;
    }
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // Update the latest tracking record with proof of delivery
      const { error } = await this.supabase
        .from('order_tracking')
        .update({
          proof_of_delivery: proof,
          status: 'delivered',
          timestamp: new Date().toISOString(),
        })
        .eq('order_id', orderId)
        .eq('status', 'out_for_delivery');

      if (error) {
        return { success: false, error: error.message };
      }

      // Update main order status
      await this.supabase
        .from('orders')
        .update({
          status: 'delivered',
          updated_at: new Date().toISOString(),
        })
        .eq('id', orderId);

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get estimated delivery time based on current location and traffic
   */
  static async getEstimatedDeliveryTime(
    orderId: string,
    currentLocation: { lat: number; lng: number },
    destinationLocation: { lat: number; lng: number }
  ): Promise<{
    success: boolean;
    data?: {
      estimated_minutes: number;
      estimated_arrival: string;
      distance_km: number;
    };
    error?: string;
  }> {
    try {
      // Calculate distance using Haversine formula
      const distance = this.calculateDistance(
        currentLocation.lat,
        currentLocation.lng,
        destinationLocation.lat,
        destinationLocation.lng
      );

      // Estimate time based on distance and average speed (30 km/h in urban areas)
      const averageSpeed = 30; // km/h
      const estimatedMinutes = Math.round((distance / averageSpeed) * 60);

      const estimatedArrival = new Date();
      estimatedArrival.setMinutes(
        estimatedArrival.getMinutes() + estimatedMinutes
      );

      return {
        success: true,
        data: {
          estimated_minutes: estimatedMinutes,
          estimated_arrival: estimatedArrival.toISOString(),
          distance_km: Math.round(distance * 100) / 100,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get orders for driver dashboard
   */
  static async getDriverOrders(driverId: string): Promise<{
    success: boolean;
    data?: Order[];
    error?: string;
  }> {
    try {
      const { data, error } = await this.supabase
        .from('orders')
        .select(
          `
          *,
          order_tracking!inner (
            status,
            location,
            timestamp
          )
        `
        )
        .eq('order_tracking.driver_id', driverId)
        .in('status', ['confirmed', 'in-transit', 'out-for-delivery'])
        .order('created_at', { ascending: false });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data: data || [] };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Send notification to customer about order status
   */
  static async sendCustomerNotification(
    orderId: string,
    status: OrderTracking['status'],
    customMessage?: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // Get customer contact information
      const { data: order, error: orderError } = await this.supabase
        .from('orders')
        .select(
          `
          customer_id,
          profiles (
            full_name,
            phone,
            email
          )
        `
        )
        .eq('id', orderId)
        .single();

      if (orderError || !order) {
        return { success: false, error: 'Order not found' };
      }

      const statusMessages = {
        created: 'Tu pedido ha sido creado y está siendo procesado.',
        confirmed: 'Tu pedido ha sido confirmado y será recogido pronto.',
        picked_up: 'Tu pedido ha sido recogido y está en camino.',
        in_transit: 'Tu pedido está en tránsito hacia su destino.',
        out_for_delivery: 'Tu pedido está siendo entregado. ¡Llegará pronto!',
        delivered: 'Tu pedido ha sido entregado exitosamente.',
        failed: 'Hubo un problema con la entrega. Te contactaremos pronto.',
        cancelled: 'Tu pedido ha sido cancelado.',
      };

      const message = customMessage || statusMessages[status];

      // Here you would integrate with SMS/Email/WhatsApp services
      // For now, we'll just log the notification
      const profile = Array.isArray(order.profiles)
        ? order.profiles[0]
        : order.profiles;
      console.log(`Notification for order ${orderId}:`, {
        customer: profile?.full_name,
        phone: profile?.phone,
        email: profile?.email,
        message,
      });

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Helper method to map tracking status to order status
   */
  private static mapTrackingStatusToOrderStatus(
    trackingStatus: OrderTracking['status']
  ): string {
    const statusMap = {
      created: 'pending',
      confirmed: 'confirmed',
      picked_up: 'in-transit',
      in_transit: 'in-transit',
      out_for_delivery: 'in-transit',
      delivered: 'delivered',
      failed: 'pending',
      cancelled: 'cancelled',
    };

    return statusMap[trackingStatus] || 'pending';
  }

  /**
   * Calculate distance between two coordinates using Haversine formula
   */
  private static calculateDistance(
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLng / 2) *
        Math.sin(dLng / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private static toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Cleanup all subscriptions
   */
  static cleanup(): void {
    this.activeSubscriptions.forEach(subscription => {
      subscription.unsubscribe();
    });
    this.activeSubscriptions.clear();
  }
}
