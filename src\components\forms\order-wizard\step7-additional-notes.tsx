'use client';

import React from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { OrderFormData } from '@/types/order-form';

interface Step7Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
}

export function OrderWizardStep7({ formData, updateFormData }: Step7Props) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          📝 Instrucciones y Políticas
        </CardTitle>
        <CardDescription>
          Instrucciones especiales y preferencias para tu pedido
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-6'>
        {/* Special Instructions */}
        <div className='space-y-3'>
          <Label htmlFor='special_instructions'>
            Instrucciones Especiales para el Repartidor
          </Label>
          <Textarea
            id='special_instructions'
            value={formData.special_instructions || ''}
            onChange={e =>
              updateFormData({
                special_instructions: e.target.value,
              })
            }
            placeholder='ej., llamar antes de llegar, dejar en recepción, no tocar timbre después de las 8pm, entregar en oficina de seguridad'
            rows={4}
            maxLength={1000}
          />
          <div className="text-sm text-gray-500 text-right">
            {(formData.special_instructions || '').length}/1000 caracteres
          </div>
        </div>

        {/* Substitution Policy */}
        <div className='space-y-3'>
          <Label>Política de Sustitución de Productos</Label>
          <div className='space-y-3'>
            <label className='flex items-start space-x-3 p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors'>
              <input
                type='radio'
                name='allow_substitutions'
                value='true'
                checked={formData.allow_substitutions === true}
                onChange={e =>
                  updateFormData({
                    allow_substitutions: e.target.value === 'true',
                  })
                }
                className='text-blue-600 mt-1'
              />
              <div>
                <div className="flex items-center space-x-2">
                  <span className="text-lg">✅</span>
                  <span className="font-medium">Permitir sustituciones</span>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Si un producto no está disponible, el repartidor puede elegir un reemplazo similar 
                  del mismo precio o menor. Te contactaremos si hay diferencias significativas.
                </p>
              </div>
            </label>
            
            <label className='flex items-start space-x-3 p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors'>
              <input
                type='radio'
                name='allow_substitutions'
                value='false'
                checked={formData.allow_substitutions === false}
                onChange={e =>
                  updateFormData({
                    allow_substitutions: e.target.value === 'true',
                  })
                }
                className='text-blue-600 mt-1'
              />
              <div>
                <div className="flex items-center space-x-2">
                  <span className="text-lg">❌</span>
                  <span className="font-medium">No permitir sustituciones</span>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Si un producto no está disponible, será cancelado del pedido y 
                  se ajustará el precio total. No se realizarán reemplazos.
                </p>
              </div>
            </label>
          </div>
        </div>

        {/* Communication Preferences */}
        <div className='space-y-3'>
          <Label>Preferencias de Comunicación</Label>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-3'>
              <h4 className="font-medium text-gray-900">Notificaciones de Entrega</h4>
              <div className='space-y-2'>
                <label className='flex items-center space-x-2'>
                  <input
                    type='checkbox'
                    defaultChecked
                    className='text-blue-600'
                  />
                  <span className="text-sm">SMS al salir para entrega</span>
                </label>
                <label className='flex items-center space-x-2'>
                  <input
                    type='checkbox'
                    defaultChecked
                    className='text-blue-600'
                  />
                  <span className="text-sm">Llamada 10 min antes de llegar</span>
                </label>
                <label className='flex items-center space-x-2'>
                  <input
                    type='checkbox'
                    defaultChecked
                    className='text-blue-600'
                  />
                  <span className="text-sm">WhatsApp con ubicación en tiempo real</span>
                </label>
              </div>
            </div>
            
            <div className='space-y-3'>
              <h4 className="font-medium text-gray-900">Horarios de Contacto</h4>
              <div className='space-y-2'>
                <label className='flex items-center space-x-2'>
                  <input
                    type='checkbox'
                    defaultChecked
                    className='text-blue-600'
                  />
                  <span className="text-sm">Lunes a Viernes: 8:00 - 20:00</span>
                </label>
                <label className='flex items-center space-x-2'>
                  <input
                    type='checkbox'
                    defaultChecked
                    className='text-blue-600'
                  />
                  <span className="text-sm">Sábados: 9:00 - 18:00</span>
                </label>
                <label className='flex items-center space-x-2'>
                  <input
                    type='checkbox'
                    className='text-blue-600'
                  />
                  <span className="text-sm">Domingos: 10:00 - 16:00</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Delivery Instructions Templates */}
        <div className='space-y-3'>
          <Label>Instrucciones Predefinidas (Opcional)</Label>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
            <button
              type="button"
              onClick={() => {
                const currentInstructions = formData.special_instructions || '';
                const newInstruction = 'Llamar al llegar, no tocar timbre.';
                if (!currentInstructions.includes(newInstruction)) {
                  updateFormData({
                    special_instructions: currentInstructions 
                      ? `${currentInstructions} ${newInstruction}`
                      : newInstruction
                  });
                }
              }}
              className='text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors'
            >
              <div className="font-medium text-sm">🔔 Llamar al llegar</div>
              <div className="text-xs text-gray-600">No tocar timbre</div>
            </button>
            
            <button
              type="button"
              onClick={() => {
                const currentInstructions = formData.special_instructions || '';
                const newInstruction = 'Dejar con portería/seguridad.';
                if (!currentInstructions.includes(newInstruction)) {
                  updateFormData({
                    special_instructions: currentInstructions 
                      ? `${currentInstructions} ${newInstruction}`
                      : newInstruction
                  });
                }
              }}
              className='text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors'
            >
              <div className="font-medium text-sm">🏢 Dejar con portería</div>
              <div className="text-xs text-gray-600">Entregar en recepción</div>
            </button>
            
            <button
              type="button"
              onClick={() => {
                const currentInstructions = formData.special_instructions || '';
                const newInstruction = 'Horario flexible, disponible todo el día.';
                if (!currentInstructions.includes(newInstruction)) {
                  updateFormData({
                    special_instructions: currentInstructions 
                      ? `${currentInstructions} ${newInstruction}`
                      : newInstruction
                  });
                }
              }}
              className='text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors'
            >
              <div className="font-medium text-sm">⏰ Horario flexible</div>
              <div className="text-xs text-gray-600">Disponible todo el día</div>
            </button>
            
            <button
              type="button"
              onClick={() => {
                const currentInstructions = formData.special_instructions || '';
                const newInstruction = 'Productos frágiles, manejar con cuidado.';
                if (!currentInstructions.includes(newInstruction)) {
                  updateFormData({
                    special_instructions: currentInstructions 
                      ? `${currentInstructions} ${newInstruction}`
                      : newInstruction
                  });
                }
              }}
              className='text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors'
            >
              <div className="font-medium text-sm">🔸 Manejo cuidadoso</div>
              <div className="text-xs text-gray-600">Productos frágiles</div>
            </button>
          </div>
        </div>

        {/* Terms and Conditions */}
        <div className='bg-gray-50 border border-gray-200 rounded-lg p-4'>
          <h4 className="font-medium text-gray-800 mb-3">Términos y Condiciones</h4>
          <div className='space-y-2 text-sm text-gray-600'>
            <label className='flex items-start space-x-2'>
              <input
                type='checkbox'
                required
                className='text-blue-600 mt-1'
              />
              <span>
                Acepto los <a href="#" className="text-blue-600 hover:underline">términos y condiciones</a> del servicio de entrega.
              </span>
            </label>
            <label className='flex items-start space-x-2'>
              <input
                type='checkbox'
                required
                className='text-blue-600 mt-1'
              />
              <span>
                Acepto la <a href="#" className="text-blue-600 hover:underline">política de privacidad</a> y el tratamiento de mis datos personales.
              </span>
            </label>
            <label className='flex items-start space-x-2'>
              <input
                type='checkbox'
                className='text-blue-600 mt-1'
              />
              <span>
                Deseo recibir promociones y ofertas especiales por correo electrónico y SMS.
              </span>
            </label>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
