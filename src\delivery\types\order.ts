export interface Order {
  id: string;
  status:
    | 'pending'
    | 'confirmed'
    | 'in-transit'
    | 'pending-admin-confirmation'
    | 'delivered'
    | 'closed'
    | 'cancelled';
  delivery_id: string | null;
  created_at: string;
  updated_at: string;
  payment_status: string;
  total_cost: number;
  payment_method: string;
  pickup_address:
    | {
        street?: string;
        number?: string;
        city?: string;
        state?: string;
        zip?: string;
      }
    | string;
  delivery_addresses:
    | {
        street?: string;
        number?: string;
        city?: string;
        state?: string;
        zip?: string;
      }
    | string;
  package_details: {
    weight?: string | number;
    dimensions?: string | number;
    description?: string;
  } | null;
  customer?: {
    full_name: string;
    email: string;
    phone: string;
  };
}
