'use client';

import { useAuthStore } from '@/stores/authStore';
import AuthContainer from '@/delivery/components/auth/AuthContainer';
import Dashboard from '@/delivery/components/dashboard/Dashboard';

export default function DeliveryPage() {
  // Middleware guarantees user is authenticated and has delivery role
  const { user } = useAuthStore();

  // If no user in store yet (during hydration), show auth container
  // Middleware will redirect unauthenticated users before they reach this page
  return <div>{user ? <Dashboard /> : <AuthContainer />}</div>;
}
