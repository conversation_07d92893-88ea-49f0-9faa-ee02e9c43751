'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useRouteOptimization } from '@/src/delivery/hooks/useRouteOptimization';
import { Location } from '@/src/delivery/hooks/useRouteOptimization';
import { OptimizedRouteMap } from '@/src/delivery/components/route-optimization';

export default function RouteOptimizationDemo() {
  const { optimizeRoute, getTrafficInsights } = useRouteOptimization();
  const [locations, setLocations] = useState<Location[]>([
    { id: '1', name: 'Almacén Central', lat: 25.6866, lng: -100.3161 },
    { id: '2', name: 'Cliente 1', lat: 25.6595, lng: -100.3624 },
    { id: '3', name: 'Cliente 2', lat: 25.6746, lng: -100.3084 },
  ]);
  const [optimizedRoute, setOptimizedRoute] = useState<Location[] | null>(null);
  const [trafficInsights, setTrafficInsights] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [newLocation, setNewLocation] = useState({
    name: '',
    lat: '',
    lng: '',
  });

  const handleOptimizeRoute = () => {
    setLoading(true);
    const result = optimizeRoute(locations);
    setOptimizedRoute(result);
    setLoading(false);
  };

  const handleGetTrafficInsights = () => {
    const insights = getTrafficInsights(locations);
    setTrafficInsights(insights);
  };

  const handleAddLocation = () => {
    if (newLocation.name && newLocation.lat && newLocation.lng) {
      const location: Location = {
        id: Date.now().toString(),
        name: newLocation.name,
        lat: parseFloat(newLocation.lat),
        lng: parseFloat(newLocation.lng),
      };
      setLocations([...locations, location]);
      setNewLocation({ name: '', lat: '', lng: '' });
    }
  };

  const handleRemoveLocation = (id: string) => {
    setLocations(locations.filter(loc => loc.id !== id));
  };

  return (
    <div className='container mx-auto py-8'>
      <h1 className='text-3xl font-bold mb-6'>Demo de Optimización de Rutas</h1>

      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Input Section */}
        <div className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Ubicaciones</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              {locations.map(location => (
                <div
                  key={location.id}
                  className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'
                >
                  <div>
                    <div className='font-medium'>{location.name}</div>
                    <div className='text-sm text-gray-500'>
                      {location.lat.toFixed(4)}, {location.lng.toFixed(4)}
                    </div>
                  </div>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => handleRemoveLocation(location.id)}
                  >
                    Eliminar
                  </Button>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Agregar Nueva Ubicación</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-2'>
                <Label htmlFor='name'>Nombre</Label>
                <Input
                  id='name'
                  value={newLocation.name}
                  onChange={e =>
                    setNewLocation({ ...newLocation, name: e.target.value })
                  }
                  placeholder='Nombre de la ubicación'
                />
              </div>
              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label htmlFor='lat'>Latitud</Label>
                  <Input
                    id='lat'
                    value={newLocation.lat}
                    onChange={e =>
                      setNewLocation({ ...newLocation, lat: e.target.value })
                    }
                    placeholder='25.6866'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='lng'>Longitud</Label>
                  <Input
                    id='lng'
                    value={newLocation.lng}
                    onChange={e =>
                      setNewLocation({ ...newLocation, lng: e.target.value })
                    }
                    placeholder='-100.3161'
                  />
                </div>
              </div>
              <Button onClick={handleAddLocation} className='w-full'>
                Agregar Ubicación
              </Button>
            </CardContent>
          </Card>

          <div className='flex gap-3'>
            <Button
              onClick={handleOptimizeRoute}
              disabled={loading}
              className='flex-1'
            >
              {loading ? 'Optimizando...' : 'Optimizar Ruta'}
            </Button>
            <Button
              onClick={handleGetTrafficInsights}
              variant='outline'
              className='flex-1'
            >
              Obtener Insights de Tráfico
            </Button>
          </div>
        </div>

        {/* Results Section */}
        <div className='space-y-6'>
          {optimizedRoute && (
            <Card>
              <CardHeader>
                <CardTitle>Ruta Optimizada</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-3 mb-4'>
                  {optimizedRoute.map((location, index) => (
                    <div
                      key={location.id}
                      className='flex items-center p-3 bg-blue-50 rounded-lg'
                    >
                      <div className='w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold mr-3'>
                        {index + 1}
                      </div>
                      <div>
                        <div className='font-medium'>{location.name}</div>
                        <div className='text-sm text-gray-500'>
                          {location.lat.toFixed(4)}, {location.lng.toFixed(4)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Map Visualization */}
                <div className='mt-4'>
                  <h3 className='font-medium mb-2'>Visualización de Ruta</h3>
                  <div className='h-64 rounded-lg overflow-hidden'>
                    <OptimizedRouteMap locations={optimizedRoute} />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {trafficInsights && (
            <Card>
              <CardHeader>
                <CardTitle>Insights de Tráfico</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <div>
                    <h3 className='font-medium mb-2'>
                      Factores de Tráfico por Ciudad
                    </h3>
                    <ul className='space-y-2'>
                      {Object.entries(trafficInsights.cityFactors).map(
                        ([city, factor]) => (
                          <li key={city} className='flex justify-between'>
                            <span>{city}</span>
                            <span>{(factor as number).toFixed(2)}x</span>
                          </li>
                        )
                      )}
                    </ul>
                  </div>

                  <div>
                    <h3 className='font-medium mb-2'>
                      Factores por Tipo de Vía
                    </h3>
                    <ul className='space-y-2'>
                      {Object.entries(trafficInsights.roadTypeFactors).map(
                        ([road, factor]) => (
                          <li key={road} className='flex justify-between'>
                            <span>{road}</span>
                            <span>{(factor as number).toFixed(2)}x</span>
                          </li>
                        )
                      )}
                    </ul>
                  </div>

                  <div>
                    <h3 className='font-medium mb-2'>
                      Factores por Hora del Día
                    </h3>
                    <ul className='space-y-2'>
                      {Object.entries(trafficInsights.timeFactors).map(
                        ([time, factor]) => (
                          <li key={time} className='flex justify-between'>
                            <span>{time}</span>
                            <span>{(factor as number).toFixed(2)}x</span>
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {!optimizedRoute && !trafficInsights && (
            <Card>
              <CardHeader>
                <CardTitle>Instrucciones</CardTitle>
              </CardHeader>
              <CardContent>
                <p className='text-gray-600'>
                  Agrega ubicaciones y haz clic en "Optimizar Ruta" para ver el
                  orden recomendado de visitas que minimiza la distancia total.
                  Haz clic en "Obtener Insights de Tráfico" para ver información
                  sobre factores que afectan el tiempo de entrega en diferentes
                  condiciones de tráfico.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
