'use client';

import { AlertCircle, XCircle, CheckCircle, Info, X } from 'lucide-react';
import { useState } from 'react';

export type AlertType = 'error' | 'warning' | 'success' | 'info';

interface ErrorAlertProps {
  type?: AlertType;
  title?: string;
  message: string;
  details?: string[];
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
  showIcon?: boolean;
}

const alertStyles = {
  error: {
    container: 'bg-red-50 border-red-200 text-red-800',
    icon: 'text-red-400',
    title: 'text-red-800',
    message: 'text-red-700',
    button: 'text-red-400 hover:text-red-600',
  },
  warning: {
    container: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    icon: 'text-yellow-400',
    title: 'text-yellow-800',
    message: 'text-yellow-700',
    button: 'text-yellow-400 hover:text-yellow-600',
  },
  success: {
    container: 'bg-green-50 border-green-200 text-green-800',
    icon: 'text-green-400',
    title: 'text-green-800',
    message: 'text-green-700',
    button: 'text-green-400 hover:text-green-600',
  },
  info: {
    container: 'bg-blue-50 border-blue-200 text-blue-800',
    icon: 'text-blue-400',
    title: 'text-blue-800',
    message: 'text-blue-700',
    button: 'text-blue-400 hover:text-blue-600',
  },
};

const icons = {
  error: XCircle,
  warning: AlertCircle,
  success: CheckCircle,
  info: Info,
};

export function ErrorAlert({
  type = 'error',
  title,
  message,
  details,
  dismissible = false,
  onDismiss,
  className = '',
  showIcon = true,
}: ErrorAlertProps) {
  const [isVisible, setIsVisible] = useState(true);

  const styles = alertStyles[type];
  const IconComponent = icons[type];

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss?.();
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className={`rounded-lg border p-4 ${styles.container} ${className}`}>
      <div className='flex'>
        {showIcon && (
          <div className='flex-shrink-0'>
            <IconComponent className={`h-5 w-5 ${styles.icon}`} />
          </div>
        )}
        <div className={`${showIcon ? 'ml-3' : ''} flex-1`}>
          {title && (
            <h3 className={`text-sm font-medium ${styles.title}`}>{title}</h3>
          )}
          <div className={`${title ? 'mt-2' : ''} text-sm ${styles.message}`}>
            <p>{message}</p>
            {details && details.length > 0 && (
              <ul className='mt-2 list-disc list-inside space-y-1'>
                {details.map((detail, index) => (
                  <li key={index}>{detail}</li>
                ))}
              </ul>
            )}
          </div>
        </div>
        {dismissible && (
          <div className='ml-auto pl-3'>
            <div className='-mx-1.5 -my-1.5'>
              <button
                type='button'
                onClick={handleDismiss}
                className={`inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 ${styles.button}`}
              >
                <span className='sr-only'>Dismiss</span>
                <X className='h-5 w-5' />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Specialized components for common use cases
export function PasswordResetErrorAlert({
  error,
  onDismiss,
  className = '',
}: {
  error: string;
  onDismiss?: () => void;
  className?: string;
}) {
  // Parse common error types and provide better messages
  let title = 'Error de restablecimiento de contraseña';
  let message = error;
  let details: string[] = [];

  if (error.includes('rate limit') || error.includes('Too many')) {
    title = 'Demasiados intentos';
    message =
      'Has excedido el límite de intentos de restablecimiento de contraseña.';
    details = [
      'Espera unos minutos antes de intentar nuevamente',
      'Si continúas teniendo problemas, contacta al soporte',
    ];
  } else if (error.includes('invalid') || error.includes('expired')) {
    title = 'Enlace inválido o expirado';
    message =
      'El enlace de restablecimiento de contraseña no es válido o ha expirado.';
    details = [
      'Solicita un nuevo enlace de restablecimiento',
      'Los enlaces expiran después de 1 hora por seguridad',
    ];
  } else if (error.includes('password') && error.includes('weak')) {
    title = 'Contraseña muy débil';
    message = 'La contraseña no cumple con los requisitos de seguridad.';
    details = [
      'Debe tener al menos 8 caracteres',
      'Incluir mayúsculas, minúsculas, números y símbolos',
      'No usar contraseñas comunes o información personal',
    ];
  } else if (error.includes('connection') || error.includes('network')) {
    title = 'Error de conexión';
    message = 'No se pudo conectar con el servidor.';
    details = [
      'Verifica tu conexión a internet',
      'Intenta nuevamente en unos momentos',
    ];
  }

  return (
    <ErrorAlert
      type='error'
      title={title}
      message={message}
      details={details}
      dismissible={!!onDismiss}
      onDismiss={onDismiss}
      className={className}
    />
  );
}

export function PasswordResetSuccessAlert({
  message = 'Contraseña actualizada exitosamente',
  onDismiss,
  className = '',
}: {
  message?: string;
  onDismiss?: () => void;
  className?: string;
}) {
  return (
    <ErrorAlert
      type='success'
      title='¡Éxito!'
      message={message}
      details={[
        'Tu contraseña ha sido actualizada',
        'Ahora puedes iniciar sesión con tu nueva contraseña',
      ]}
      dismissible={!!onDismiss}
      onDismiss={onDismiss}
      className={className}
    />
  );
}

export function PasswordResetInfoAlert({
  message,
  onDismiss,
  className = '',
}: {
  message: string;
  onDismiss?: () => void;
  className?: string;
}) {
  return (
    <ErrorAlert
      type='info'
      title='Información'
      message={message}
      dismissible={!!onDismiss}
      onDismiss={onDismiss}
      className={className}
    />
  );
}
