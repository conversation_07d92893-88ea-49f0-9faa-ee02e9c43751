'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import {
  OrderTrackingService,
  TrackingUpdate,
} from '@/lib/services/order-tracking';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { OrderTracking, ProductItem, Stop } from '@/types/order-form';
import { createClient } from '@/utils/supabase/client';

interface OrderDetails {
  id: string;
  tracking_number: string;
  customer_name: string;
  customer_phone: string;
  total_cost: number;
  status: string;
  created_at: string;
  products: ProductItem[];
  stops: Stop[];
  delivery_date: string;
  delivery_time_slot: string;
  special_instructions?: string;
}

export default function OrderTrackingPage() {
  const params = useParams();
  const router = useRouter();
  const { user, loading } = useAuthStore();
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null);
  const [trackingHistory, setTrackingHistory] = useState<OrderTracking[]>([]);
  interface CurrentStatusData {
    status: OrderTracking['status'];
    location?: OrderTracking['location'];
    estimated_arrival?: string;
    driver_info?: {
      full_name: string;
      phone: string;
      vehicle_info: string;
    };
    last_update: string;
  }

  const [currentStatus, setCurrentStatus] = useState<CurrentStatusData | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const orderId = params.id as string;
  const supabase = createClient();

  // Load order details and tracking data
  const loadOrderData = useCallback(async () => {
    if (!orderId) return;

    setIsLoading(true);
    setError(null);

    try {
      // Get order details
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .select('*')
        .eq('id', orderId)
        .single();

      if (orderError) {
        throw new Error(orderError.message);
      }

      setOrderDetails(order);

      // Get tracking history
      const trackingResult =
        await OrderTrackingService.getOrderTracking(orderId);
      if (trackingResult.success && trackingResult.data) {
        setTrackingHistory(trackingResult.data);
      }

      // Get current status
      const statusResult = await OrderTrackingService.getCurrentStatus(orderId);
      if (statusResult.success && statusResult.data) {
        setCurrentStatus(statusResult.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  }, [orderId, supabase]);

  // Handle real-time updates
  const handleTrackingUpdate = useCallback(
    (update: TrackingUpdate) => {
      // Update current status
      setCurrentStatus(prev => ({
        ...prev,
        status: update.status,
        location: update.location,
        last_update: new Date().toISOString(),
      }));

      // Reload tracking history to get the latest data
      loadOrderData();
    },
    [loadOrderData]
  );

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
      return;
    }

    if (user && orderId) {
      loadOrderData();

      // Subscribe to real-time updates
      const subscription = OrderTrackingService.subscribeToOrderUpdates(
        orderId,
        handleTrackingUpdate
      );

      return () => {
        subscription.unsubscribe();
      };
    }
  }, [user, loading, orderId, loadOrderData, handleTrackingUpdate, router]);

  const getStatusColor = (status: string) => {
    const colors = {
      created: 'bg-blue-100 text-blue-800',
      confirmed: 'bg-green-100 text-green-800',
      picked_up: 'bg-yellow-100 text-yellow-800',
      in_transit: 'bg-orange-100 text-orange-800',
      out_for_delivery: 'bg-purple-100 text-purple-800',
      delivered: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      cancelled: 'bg-gray-100 text-gray-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getStatusIcon = (status: string) => {
    const icons = {
      created: '📝',
      confirmed: '✅',
      picked_up: '📦',
      in_transit: '🚛',
      out_for_delivery: '🚚',
      delivered: '✅',
      failed: '❌',
      cancelled: '🚫',
    };
    return icons[status as keyof typeof icons] || '📋';
  };

  const getStatusText = (status: string) => {
    const texts = {
      created: 'Pedido Creado',
      confirmed: 'Confirmado',
      picked_up: 'Recogido',
      in_transit: 'En Tránsito',
      out_for_delivery: 'En Entrega',
      delivered: 'Entregado',
      failed: 'Falló',
      cancelled: 'Cancelado',
    };
    return texts[status as keyof typeof texts] || status;
  };

  if (loading || isLoading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600'></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <Card>
          <CardContent className='p-8 text-center'>
            <div className='text-red-600 text-xl mb-4'>❌ Error</div>
            <p className='text-gray-600 mb-4'>{error}</p>
            <Button onClick={() => router.push('/customer/orders')}>
              Volver a Pedidos
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!orderDetails) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <Card>
          <CardContent className='p-8 text-center'>
            <div className='text-gray-600 text-xl mb-4'>
              📦 Pedido no encontrado
            </div>
            <Button onClick={() => router.push('/customer/orders')}>
              Volver a Pedidos
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='max-w-4xl mx-auto space-y-6'>
        {/* Header */}
        <div className='flex justify-between items-start'>
          <div>
            <h1 className='text-3xl font-bold text-gray-900 mb-2'>
              Seguimiento de Pedido
            </h1>
            <p className='text-gray-600'>
              Número de seguimiento:{' '}
              <span className='font-mono font-semibold'>
                {orderDetails.tracking_number}
              </span>
            </p>
          </div>
          <Button
            variant='outline'
            onClick={() => router.push('/customer/orders')}
          >
            ← Volver a Pedidos
          </Button>
        </div>

        {/* Current Status */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              {getStatusIcon(currentStatus?.status || orderDetails.status)}
              Estado Actual
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex items-center justify-between mb-4'>
              <Badge
                className={getStatusColor(
                  currentStatus?.status || orderDetails.status
                )}
              >
                {getStatusText(currentStatus?.status || orderDetails.status)}
              </Badge>
              <span className='text-sm text-gray-600'>
                Última actualización:{' '}
                {currentStatus?.last_update
                  ? new Date(currentStatus.last_update).toLocaleString('es-MX')
                  : 'No disponible'}
              </span>
            </div>

            {currentStatus?.location && (
              <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
                <h4 className='font-semibold text-blue-800 mb-2'>
                  📍 Ubicación Actual
                </h4>
                <p className='text-blue-700'>
                  {currentStatus.location.address}
                </p>
                <p className='text-sm text-blue-600'>
                  Coordenadas: {currentStatus.location.lat.toFixed(6)},{' '}
                  {currentStatus.location.lng.toFixed(6)}
                </p>
              </div>
            )}

            {currentStatus?.estimated_arrival && (
              <div className='mt-4 bg-green-50 border border-green-200 rounded-lg p-4'>
                <h4 className='font-semibold text-green-800 mb-2'>
                  ⏱️ Tiempo Estimado de Entrega
                </h4>
                <p className='text-green-700'>
                  {new Date(currentStatus.estimated_arrival).toLocaleString(
                    'es-MX'
                  )}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Order Details */}
        <Card>
          <CardHeader>
            <CardTitle>📋 Detalles del Pedido</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4 text-sm'>
              <div>
                <strong>Cliente:</strong> {orderDetails.customer_name}
              </div>
              <div>
                <strong>Teléfono:</strong> {orderDetails.customer_phone}
              </div>
              <div>
                <strong>Fecha de Entrega:</strong> {orderDetails.delivery_date}
              </div>
              <div>
                <strong>Hora de Entrega:</strong>{' '}
                {orderDetails.delivery_time_slot}
              </div>
              <div>
                <strong>Total:</strong> $
                {orderDetails.total_cost?.toFixed(2) || '0.00'} MXN
              </div>
              <div>
                <strong>Productos:</strong> {orderDetails.products?.length || 0}{' '}
                artículos
              </div>
            </div>

            {orderDetails.special_instructions && (
              <div className='mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg'>
                <h4 className='font-semibold text-yellow-800 mb-1'>
                  📝 Instrucciones Especiales
                </h4>
                <p className='text-yellow-700'>
                  {orderDetails.special_instructions}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tracking History */}
        <Card>
          <CardHeader>
            <CardTitle>📊 Historial de Seguimiento</CardTitle>
          </CardHeader>
          <CardContent>
            {trackingHistory.length === 0 ? (
              <p className='text-gray-600 text-center py-4'>
                No hay historial de seguimiento disponible
              </p>
            ) : (
              <div className='space-y-4'>
                {trackingHistory.map((tracking, index) => (
                  <div
                    key={tracking.id}
                    className='flex items-start gap-3 p-3 border-b border-gray-200 last:border-0'
                  >
                    <div className='flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-800 font-semibold'>
                      {index + 1}
                    </div>
                    <div className='flex-1'>
                      <div className='flex justify-between items-start'>
                        <div>
                          <Badge className={getStatusColor(tracking.status)}>
                            {getStatusText(tracking.status)}
                          </Badge>
                          {tracking.notes && (
                            <p className='mt-1 text-gray-700'>
                              {tracking.notes}
                            </p>
                          )}
                        </div>
                        <span className='text-xs text-gray-500 whitespace-nowrap'>
                          {new Date(tracking.timestamp).toLocaleString('es-MX')}
                        </span>
                      </div>
                      {tracking.location && (
                        <div className='mt-2 text-sm text-gray-600'>
                          📍 {tracking.location.address}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
