'use client';

import { useState } from 'react';
import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/ui/button';
import { Input, PasswordInput } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import Link from 'next/link';

export function RegisterForm() {
  const { signUp, loading } = useAuthStore();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [phone, setPhone] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }

    // Build metadata object, only include phone if it has a value
    const metadata: Record<string, string> = {
      full_name: fullName,
    };

    if (phone.trim()) {
      metadata.phone = phone;
    }

    const { error } = await signUp(email, password, metadata);

    if (error) {
      // Handle specific error types
      if (
        error.message.includes('429') ||
        error.message.includes('Too Many Requests')
      ) {
        setError(
          'Too many registration attempts. Please wait a few minutes before trying again.'
        );
      } else if (error.message.includes('User already registered')) {
        setError(
          'An account with this email already exists. Please try signing in instead.'
        );
      } else if (error.message.includes('Invalid email')) {
        setError('Please enter a valid email address.');
      } else if (error.message.includes('Password')) {
        setError('Password must be at least 6 characters long.');
      } else {
        setError(error.message);
      }
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className='space-y-6 bg-gradient-to-br from-blue-900/20 to-green-900/20 backdrop-blur-sm border border-blue-400/30 rounded-lg p-6 shadow-xl'
    >
      {error && (
        <div className='p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md'>
          {error}
        </div>
      )}

      <div className='space-y-4'>
        <div>
          <Label htmlFor='fullName' className='text-white'>
            Nombre completo
          </Label>
          <Input
            id='fullName'
            type='text'
            value={fullName}
            onChange={e => setFullName(e.target.value)}
            required
            placeholder='Ingresa tu nombre completo'
          />
        </div>

        <div>
          <Label htmlFor='email' className='text-white'>
            Correo electrónico
          </Label>
          <Input
            id='email'
            type='email'
            value={email}
            onChange={e => setEmail(e.target.value)}
            required
            placeholder='Ingresa tu correo electrónico'
          />
        </div>

        <div>
          <Label htmlFor='phone' className='text-white'>
            Número de teléfono (Opcional)
          </Label>
          <Input
            id='phone'
            type='tel'
            value={phone}
            onChange={e => setPhone(e.target.value)}
            placeholder='Ingresa tu número de teléfono'
          />
          <p className='text-xs text-gray-300 mt-1'>
            El número de teléfono es opcional pero recomendado para
            actualizaciones de entrega
          </p>
        </div>

        <div>
          <Label htmlFor='password' className='text-white'>
            Contraseña
          </Label>
          <PasswordInput
            id='password'
            value={password}
            onChange={e => setPassword(e.target.value)}
            required
            placeholder='Crea una contraseña'
          />
        </div>

        <div>
          <Label htmlFor='confirmPassword' className='text-white'>
            Confirmar contraseña
          </Label>
          <PasswordInput
            id='confirmPassword'
            value={confirmPassword}
            onChange={e => setConfirmPassword(e.target.value)}
            required
            placeholder='Confirma tu contraseña'
          />
        </div>
      </div>

      <Button
        type='submit'
        className='w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white border-0'
        disabled={loading}
      >
        {loading ? 'Creando cuenta...' : 'Crear cuenta'}
      </Button>

      <div className='text-center'>
        <p className='text-sm text-white'>
          ¿Ya tienes una cuenta?{' '}
          <Link
            href='/auth/login'
            className='text-green-300 hover:text-blue-300 hover:underline font-medium transition-colors'
          >
            Inicia sesión
          </Link>
        </p>
      </div>
    </form>
  );
}
