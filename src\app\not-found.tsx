'use client';

import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Home, ArrowLeft } from 'lucide-react';

export default function NotFound() {
  const router = useRouter();

  return (
    <div className='min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8'>
      <div className='max-w-md w-full space-y-8 text-center'>
        <div>
          <h1 className='text-9xl font-bold text-gray-200'>404</h1>
          <h2 className='mt-4 text-3xl font-bold text-gray-900'>
            Página no encontrada
          </h2>
          <p className='mt-2 text-lg text-gray-600'>
            Lo sentimos, la página que estás buscando no existe o ha sido
            movida.
          </p>
        </div>

        <div className='mt-8 flex flex-col sm:flex-row items-center justify-center gap-4'>
          <Button
            onClick={() => router.back()}
            variant='outline'
            className='flex items-center gap-2'
          >
            <ArrowLeft className='w-4 h-4' />
            Volver atrás
          </Button>
          <Button
            onClick={() => router.push('/')}
            className='flex items-center gap-2'
          >
            <Home className='w-4 h-4' />
            Ir al inicio
          </Button>
        </div>
      </div>
    </div>
  );
}
