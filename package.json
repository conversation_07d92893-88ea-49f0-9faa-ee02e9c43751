{"name": "mouvers", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint .", "lint:fix": "eslint . --fix", "prettier": "prettier", "prettier:check": "prettier --check .", "prettier:write": "prettier --write .", "format": "prettier --write . && eslint . --fix", "type-check": "tsc --noEmit", "typegen": "next typegen", "quality-check": "pnpm typegen && pnpm prettier:check && pnpm lint && pnpm type-check", "pre-commit": "bash scripts/pre-commit.sh"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.2", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jose": "^6.1.0", "leaflet": "^1.9.4", "lucide-react": "^0.523.0", "next": "15.5.2", "react": "19.1.1", "react-dom": "19.1.1", "react-leaflet": "^5.0.0", "stripe": "^14.0.0", "styled-jsx": "^5.1.7", "tailwind-merge": "^3.3.1", "zod": "^4.1.5", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/leaflet": "^1.9.8", "@types/node": "^20", "@types/react": "19.1.12", "@types/react-dom": "19.1.9", "eslint": "^9", "eslint-config-next": "15.5.2", "prettier": "^3.3.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}, "overrides": {"@types/react": "19.1.12", "@types/react-dom": "19.1.9"}}