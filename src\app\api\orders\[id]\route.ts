import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import {
  secureAPI,
  sanitizeInput,
  isValidUUID,
} from '@/lib/security/api-security';
import { auditLog } from '@/lib/security/audit-logger';
import { Order } from '@/delivery/types/order';

// Define interfaces for the request body
interface PickupAddress {
  street?: string;
  number?: string;
  city?: string;
  state?: string;
  zip?: string;
}

interface DeliveryAddress {
  street?: string;
  number?: string;
  city?: string;
  state?: string;
  zip?: string;
}

interface PackageDetails {
  weight?: string | number;
  dimensions?: string | number;
  description?: string;
}

interface FiscalData {
  rfc?: string;
  business_name?: string;
  tax_regime?: string;
}

interface PaymentMethodDetails {
  card_last_four?: string;
  digital_wallet_provider?: string;
  bank_account_last_four?: string;
}

interface Stop {
  id: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  scheduled_time?: string;
  instructions?: string;
}

interface RouteOptimization {
  mode?: string;
  preferences?: Record<string, unknown>;
}

interface UpdateOrderRequestBody {
  // New Mexican logistics fields
  vehicle_type_id?: string;
  cargo_type_id?: string;
  delivery_mode?: string;
  scheduled_pickup_time?: string;
  scheduled_delivery_time?: string;
  delivery_instructions?: string;
  special_handling_notes?: string;
  fiscal_data?: FiscalData;
  payment_method_details?: PaymentMethodDetails;
  stops?: Stop[];
  route_optimization?: RouteOptimization;
  tracking_number?: string;
  delivery_zone?: string;
  delivery_region?: string;
  // Existing fields
  status?: string;
  pickup_address?: PickupAddress | string;
  delivery_addresses?: (DeliveryAddress | string)[];
  package_details?: PackageDetails;
  total_cost?: number;
  payment_status?: string;
  payment_method?: string;
  delivery_id?: string;
}

interface OrderFromDB extends Order {
  vehicle_type_id?: string;
  cargo_type_id?: string;
  delivery_mode?: string;
  scheduled_pickup_time?: string;
  scheduled_delivery_time?: string;
  delivery_instructions?: string;
  special_handling_notes?: string;
  fiscal_data?: FiscalData;
  payment_method_details?: PaymentMethodDetails;
  stops?: Stop[];
  route_optimization?: RouteOptimization;
  tracking_number?: string;
  delivery_zone?: string;
  delivery_region?: string;
  customer_id: string;
}

// PUT /api/orders/[id] - Update an order with Mexican logistics fields
export const PUT = secureAPI(
  {
    PUT: async (request, context) => {
      try {
        // Extract order ID from URL parameters
        const url = new URL(request.url);
        const orderId = url.pathname.split('/').slice(-2)[0];

        // Validate UUID format
        if (!orderId || !isValidUUID(orderId)) {
          return NextResponse.json(
            { error: 'ID de orden inválido' },
            { status: 400 }
          );
        }

        // Parse and sanitize request body
        let requestBody: UpdateOrderRequestBody;
        try {
          requestBody = sanitizeInput(
            await request.json()
          ) as UpdateOrderRequestBody;
        } catch {
          return NextResponse.json(
            { error: 'Formato de cuerpo de solicitud inválido' },
            { status: 400 }
          );
        }

        const {
          vehicle_type_id,
          cargo_type_id,
          delivery_mode,
          scheduled_pickup_time,
          scheduled_delivery_time,
          delivery_instructions,
          special_handling_notes,
          fiscal_data,
          payment_method_details,
          stops,
          route_optimization,
          tracking_number,
          delivery_zone,
          delivery_region,
          // Other existing fields
          status,
          pickup_address,
          delivery_addresses,
          package_details,
          total_cost,
          payment_status,
          payment_method,
          delivery_id,
        } = requestBody;

        // Validate UUID fields if provided
        if (vehicle_type_id && !isValidUUID(vehicle_type_id)) {
          return NextResponse.json(
            { error: 'ID de tipo de vehículo inválido' },
            { status: 400 }
          );
        }

        if (cargo_type_id && !isValidUUID(cargo_type_id)) {
          return NextResponse.json(
            { error: 'ID de tipo de carga inválido' },
            { status: 400 }
          );
        }

        if (delivery_id && !isValidUUID(delivery_id)) {
          return NextResponse.json(
            { error: 'ID de repartidor inválido' },
            { status: 400 }
          );
        }

        const supabase = await createClient();

        // Check if user is authorized to update this order
        // Customers can update their own orders, admins can update any order, delivery personnel can update assigned orders
        const { data: order, error: orderError } = await supabase
          .from('orders')
          .select('customer_id, delivery_id')
          .eq('id', orderId)
          .single();

        if (orderError) {
          console.error('Database error:', orderError);
          await auditLog({
            event: 'ORDER_UPDATE_CHECK_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { error: orderError.message, orderId },
            severity: 'MEDIUM',
            category: 'DATA',
          });

          if (orderError.code === 'PGRST116') {
            return NextResponse.json(
              { error: 'Orden no encontrada' },
              { status: 404 }
            );
          }

          return NextResponse.json(
            { error: 'Error al verificar la orden' },
            { status: 500 }
          );
        }

        if (!order) {
          return NextResponse.json(
            { error: 'Orden no encontrada' },
            { status: 404 }
          );
        }

        // Authorization check
        const userRole = context.user?.role;
        const userId = context.user?.id;

        if (userRole === 'customer' && order.customer_id !== userId) {
          await auditLog({
            event: 'UNAUTHORIZED_ORDER_UPDATE_ATTEMPT',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { orderId, userRole, userId },
            severity: 'HIGH',
            category: 'SECURITY',
          });
          return NextResponse.json(
            { error: 'No tienes permiso para actualizar esta orden' },
            { status: 403 }
          );
        }

        if (userRole === 'delivery' && order.delivery_id !== userId) {
          await auditLog({
            event: 'UNAUTHORIZED_ORDER_UPDATE_ATTEMPT',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { orderId, userRole, userId },
            severity: 'HIGH',
            category: 'SECURITY',
          });
          return NextResponse.json(
            { error: 'Solo puedes actualizar órdenes asignadas a ti' },
            { status: 403 }
          );
        }

        // Prepare update data
        const updateData: Record<string, string | number | object | null> = {
          updated_at: new Date().toISOString(),
        };

        // Add fields to update if provided
        if (vehicle_type_id !== undefined)
          updateData.vehicle_type_id = vehicle_type_id;
        if (cargo_type_id !== undefined)
          updateData.cargo_type_id = cargo_type_id;
        if (delivery_mode !== undefined)
          updateData.delivery_mode = delivery_mode;
        if (scheduled_pickup_time !== undefined)
          updateData.scheduled_pickup_time = scheduled_pickup_time;
        if (scheduled_delivery_time !== undefined)
          updateData.scheduled_delivery_time = scheduled_delivery_time;
        if (delivery_instructions !== undefined)
          updateData.delivery_instructions = delivery_instructions;
        if (special_handling_notes !== undefined)
          updateData.special_handling_notes = special_handling_notes;
        if (fiscal_data !== undefined) updateData.fiscal_data = fiscal_data;
        if (payment_method_details !== undefined)
          updateData.payment_method_details = payment_method_details;
        if (stops !== undefined) updateData.stops = stops;
        if (route_optimization !== undefined)
          updateData.route_optimization = route_optimization;
        if (tracking_number !== undefined)
          updateData.tracking_number = tracking_number;
        if (delivery_zone !== undefined)
          updateData.delivery_zone = delivery_zone;
        if (delivery_region !== undefined)
          updateData.delivery_region = delivery_region;

        // Existing fields
        if (status !== undefined) updateData.status = status;
        if (pickup_address !== undefined)
          updateData.pickup_address = pickup_address;
        if (delivery_addresses !== undefined)
          updateData.delivery_addresses = delivery_addresses;
        if (package_details !== undefined)
          updateData.package_details = package_details;
        if (total_cost !== undefined) updateData.total_cost = total_cost;
        if (payment_status !== undefined)
          updateData.payment_status = payment_status;
        if (payment_method !== undefined)
          updateData.payment_method = payment_method;
        if (delivery_id !== undefined) updateData.delivery_id = delivery_id;

        // Update order
        const { data, error } = await supabase
          .from('orders')
          .update(updateData)
          .eq('id', orderId)
          .select()
          .single();

        if (error) {
          console.error('Database error:', error);
          await auditLog({
            event: 'ORDER_UPDATE_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { error: error.message, orderId, updateData },
            severity: 'MEDIUM',
            category: 'DATA',
          });
          return NextResponse.json(
            { error: 'Error al actualizar la orden' },
            { status: 500 }
          );
        }

        if (!data) {
          return NextResponse.json(
            { error: 'Orden no encontrada' },
            { status: 404 }
          );
        }

        await auditLog({
          event: 'ORDER_UPDATED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { orderId, updatedFields: Object.keys(updateData) },
          severity: 'MEDIUM',
          category: 'DATA',
        });

        return NextResponse.json({
          success: true,
          message: 'Orden actualizada exitosamente',
          data,
        });
      } catch (error) {
        await auditLog({
          event: 'ORDER_UPDATE_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: true,
    allowedRoles: ['admin', 'customer', 'delivery'],
    requireMFA: false,
    validateCSRF: true,
    rateLimitRpm: 30,
  }
);
