'use client';

import { useState, useMemo } from 'react';
import { useUserFiltering } from '@/hooks/useUserFiltering';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

import {
  User as UserIcon,
  Mail,
  Phone,
  Calendar,
  Shield,
  Truck,
  Users,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  ArrowLeft,
} from 'lucide-react';

interface User {
  id: string;
  email: string;
  full_name: string | null;
  role: 'customer' | 'delivery' | 'admin';
  phone: string | null;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

interface UsersManagementProps {
  users: User[];
  isLoading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
  onUpdateRole: (userId: string, newRole: User['role']) => Promise<boolean>;
  onToggleStatus: (userId: string, isActive: boolean) => Promise<boolean>;
  loading?: boolean;
  onBack?: () => void;
}

const roleConfig = {
  customer: {
    label: 'Cliente',
    color: 'bg-blue-100 text-blue-800',
    icon: <UserIcon className='w-3 h-3' />,
  },
  delivery: {
    label: 'Repartidor',
    color: 'bg-green-100 text-green-800',
    icon: (
      <span title='Repartidor' aria-label='Repartidor'>
        <Truck className='w-3 h-3' />
      </span>
    ),
  },
  admin: {
    label: 'Administrador',
    color: 'bg-purple-100 text-purple-800',
    icon: (
      <span title='Administrador' aria-label='Administrador'>
        <Shield className='w-3 h-3' />
      </span>
    ),
  },
};

const getStatusConfig = (isActive: boolean) => {
  if (isActive) {
    return {
      label: 'Activo',
      color: 'bg-green-100 text-green-800',
      icon: <CheckCircle className='w-3 h-3' />,
    };
  }
  return {
    label: 'Inactivo',
    color: 'bg-red-100 text-red-800',
    icon: <XCircle className='w-3 h-3' />,
  };
};

export function UsersManagement({
  users,
  onUpdateRole,
  onToggleStatus,
  isLoading,
  onBack,
}: UsersManagementProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [updatingUser, setUpdatingUser] = useState<string | null>(null);

  const filteredUsers = useUserFiltering({
    users,
    searchTerm,
    roleFilter,
    statusFilter,
  });

  const handleRoleUpdate = async (userId: string, newRole: User['role']) => {
    setUpdatingUser(userId);
    try {
      await onUpdateRole(userId, newRole);
    } finally {
      setUpdatingUser(null);
    }
  };

  const handleStatusToggle = async (userId: string, currentStatus: boolean) => {
    setUpdatingUser(userId);
    try {
      await onToggleStatus(userId, !currentStatus);
    } finally {
      setUpdatingUser(null);
    }
  };

  const roleCounts = useMemo(() => {
    const counts = { customer: 0, delivery: 0, admin: 0, total: users.length };
    users.forEach(user => {
      if (user.role in counts) {
        counts[user.role]++;
      }
    });
    return counts;
  }, [users]);

  if (isLoading) {
    return (
      <div className='flex items-center justify-center py-12'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600'></div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Back Button */}
      {onBack && (
        <div className='flex justify-start'>
          <Button
            variant='outline'
            onClick={onBack}
            className='flex items-center gap-2'
          >
            <ArrowLeft className='w-4 h-4' />
            Volver al Dashboard
          </Button>
        </div>
      )}

      {/* Statistics Cards */}
      <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Usuarios
            </CardTitle>
            <Users className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{roleCounts.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Clientes</CardTitle>
            <UserIcon className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-blue-600'>
              {roleCounts.customer}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Repartidores</CardTitle>
            <Shield className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-green-600'>
              {roleCounts.delivery}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Administradores
            </CardTitle>
            <Shield className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-purple-600'>
              {roleCounts.admin}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Filter className='w-4 h-4' />
            Filtros
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div>
              <label className='text-sm font-medium text-gray-700 mb-2 block'>
                Buscar
              </label>
              <div className='relative'>
                <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4' />
                <Input
                  placeholder='Buscar por nombre, email o teléfono...'
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className='pl-10'
                />
              </div>
            </div>

            <div>
              <label className='text-sm font-medium text-gray-700 mb-2 block'>
                Rol
              </label>
              <select
                value={roleFilter}
                onChange={e => setRoleFilter(e.target.value)}
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              >
                <option value='all'>Todos los Roles</option>
                <option value='customer'>Clientes</option>
                <option value='delivery'>Repartidores</option>
                <option value='admin'>Administradores</option>
              </select>
            </div>

            <div>
              <label className='text-sm font-medium text-gray-700 mb-2 block'>
                Estado
              </label>
              <select
                value={statusFilter}
                onChange={e => setStatusFilter(e.target.value)}
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              >
                <option value='all'>Todos los Estados</option>
                <option value='active'>Activos</option>
                <option value='not_active'>Inactivos</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Usuarios ({filteredUsers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='overflow-x-auto'>
            <table className='w-full'>
              <thead>
                <tr className='border-b'>
                  <th className='text-left py-3 px-4 font-medium text-gray-700'>
                    Usuario
                  </th>
                  <th className='text-left py-3 px-4 font-medium text-gray-700'>
                    Rol
                  </th>
                  <th className='text-left py-3 px-4 font-medium text-gray-700'>
                    Estado
                  </th>
                  <th className='text-left py-3 px-4 font-medium text-gray-700'>
                    Fecha de Registro
                  </th>
                  <th className='text-left py-3 px-4 font-medium text-gray-700'>
                    Última Actualización
                  </th>
                  <th className='text-left py-3 px-4 font-medium text-gray-700'>
                    Acciones
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map(user => {
                  const roleConf = roleConfig[user.role];
                  const statusConf = getStatusConfig(user.is_active);

                  return (
                    <tr key={user.id} className='border-b hover:bg-gray-50'>
                      <td className='py-4 px-4'>
                        <div className='flex items-center space-x-3'>
                          <div className='flex-shrink-0'>
                            <div className='w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center'>
                              <UserIcon className='w-5 h-5 text-gray-600' />
                            </div>
                          </div>
                          <div>
                            <div className='font-medium text-gray-900'>
                              {user.full_name || 'Sin nombre'}
                            </div>
                            <div className='text-sm text-gray-500 flex items-center gap-1'>
                              <Mail className='w-3 h-3' />
                              {user.email}
                            </div>
                            {user.phone && (
                              <div className='text-sm text-gray-500 flex items-center gap-1'>
                                <Phone className='w-3 h-3' />
                                {user.phone}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>

                      <td className='py-4 px-4'>
                        <Badge className={roleConf.color}>
                          <span className='mr-1'>{roleConf.icon}</span>
                          {roleConf.label}
                        </Badge>
                      </td>

                      <td className='py-4 px-4'>
                        <Badge className={statusConf.color}>
                          <span className='mr-1'>{statusConf.icon}</span>
                          {statusConf.label}
                        </Badge>
                      </td>

                      <td className='py-4 px-4 text-sm text-gray-500'>
                        <div className='flex items-center gap-1'>
                          <Calendar className='w-3 h-3' />
                          {new Date(user.created_at).toLocaleDateString(
                            'es-MX'
                          )}
                        </div>
                      </td>

                      <td className='py-4 px-4 text-sm text-gray-500'>
                        <div className='flex items-center gap-1'>
                          <Calendar className='w-3 h-3' />
                          {new Date(user.updated_at).toLocaleDateString(
                            'es-MX'
                          )}
                        </div>
                      </td>

                      <td className='py-4 px-4'>
                        <div className='flex items-center gap-2'>
                          {/* Role Update */}
                          <select
                            className='border rounded-md px-2 py-1 text-xs'
                            value={user.role}
                            onChange={e =>
                              handleRoleUpdate(
                                user.id,
                                e.target.value as User['role']
                              )
                            }
                            disabled={
                              updatingUser === user.id || user.role === 'admin'
                            }
                          >
                            <option value='customer'>Cliente</option>
                            <option value='delivery'>Repartidor</option>
                            <option value='admin'>Administrador</option>
                          </select>

                          {/* Status Toggle */}
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() =>
                              handleStatusToggle(user.id, user.is_active)
                            }
                            disabled={
                              updatingUser === user.id || user.role === 'admin'
                            }
                            className={
                              user.is_active ? 'text-red-600' : 'text-green-600'
                            }
                          >
                            {user.is_active ? 'Desactivar' : 'Activar'}
                          </Button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>

            {filteredUsers.length === 0 && (
              <div className='text-center py-8 text-gray-500'>
                No se encontraron usuarios que coincidan con los filtros
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
