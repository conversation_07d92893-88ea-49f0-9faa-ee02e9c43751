'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface Order {
  id: string;
  status:
    | 'pending'
    | 'confirmed'
    | 'in-transit'
    | 'pending-admin-confirmation'
    | 'delivered'
    | 'closed'
    | 'cancelled';
  pickup_address: {
    street_address: string;
    city: string;
    state: string;
    postal_code: string;
    contact_name: string;
    contact_phone: string;
  };
  delivery_addresses: {
    street_address: string;
    city: string;
    state: string;
    postal_code: string;
    recipient_name: string;
    phone: string;
  };
  package_details: {
    description: string;
    weight: string;
    dimensions: string;
    value: string;
    special_instructions?: string;
  };
  total_cost: number;
  created_at: string;
  updated_at: string;
  payment_status?: string;
  payment_method?: string;
}

interface OrderTrackingProps {
  orders: Order[];
  loading?: boolean;
}

const statusConfig = {
  pending: {
    label: 'Pendiente',
    color: 'bg-yellow-100 text-yellow-800',
    icon: '⏳',
    description: 'El pedido está siendo procesado',
    progress: 'w-1/4',
    progressColor: 'bg-yellow-500',
  },
  confirmed: {
    label: 'Confirmado',
    color: 'bg-blue-100 text-blue-800',
    icon: '✅',
    description: 'Pedido confirmado y listo para recoger',
    progress: 'w-1/2',
    progressColor: 'bg-blue-500',
  },
  'in-transit': {
    label: 'En Tránsito',
    color: 'bg-orange-100 text-orange-800',
    icon: '🚚',
    description: 'El paquete está en camino',
    progress: 'w-3/4',
    progressColor: 'bg-orange-500',
  },
  'pending-admin-confirmation': {
    label: 'Pendiente Confirmación',
    color: 'bg-purple-100 text-purple-800',
    icon: '⏳',
    description: 'Paquete entregado, pendiente confirmación',
    progress: 'w-5/6',
    progressColor: 'bg-purple-500',
  },
  delivered: {
    label: 'Entregado',
    color: 'bg-green-100 text-green-800',
    icon: '📦',
    description: 'Paquete entregado exitosamente',
    progress: 'w-full',
    progressColor: 'bg-green-500',
  },
  closed: {
    label: 'Cerrado',
    color: 'bg-gray-100 text-gray-800',
    icon: '🔒',
    description: 'Orden completada y cerrada',
    progress: 'w-full',
    progressColor: 'bg-gray-500',
  },
  cancelled: {
    label: 'Cancelado',
    color: 'bg-red-100 text-red-800',
    icon: '❌',
    description: 'El pedido ha sido cancelado',
    progress: 'w-1/4',
    progressColor: 'bg-red-500',
  },
};

// Order Details Modal Component
function OrderDetailsModal({
  order,
  isOpen,
  onClose,
}: {
  order: Order;
  isOpen: boolean;
  onClose: () => void;
}) {
  if (!isOpen) return null;

  const config = statusConfig[order.status];
  const getStatusDescription = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Tu pedido está siendo procesado por nuestro equipo. Te notificaremos cuando esté confirmado.';
      case 'confirmed':
        return 'Tu pedido ha sido confirmado y está listo para ser recogido por nuestro equipo de delivery.';
      case 'in-transit':
        return '¡Tu paquete está en camino! Nuestro equipo de delivery lo está transportando hacia ti.';
      case 'pending-admin-confirmation':
        return 'El paquete ha sido entregado y está pendiente de confirmación por parte de nuestro equipo.';
      case 'delivered':
        return '¡Felicitaciones! Tu pedido ha sido entregado exitosamente. ¡Gracias por tu preferencia!';
      case 'closed':
        return 'Tu pedido ha sido completado y cerrado oficialmente. ¡Gracias por usar nuestros servicios!';
      case 'cancelled':
        return 'Este pedido ha sido cancelado. Si tienes alguna pregunta, por favor contáctanos.';
      default:
        return 'Estado del pedido actualizado.';
    }
  };

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
      <div className='bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto'>
        <div className='p-6'>
          {/* Header */}
          <div className='flex items-center justify-between mb-6'>
            <div>
              <h2 className='text-2xl font-bold text-gray-900'>
                Pedido #{order.id.slice(-8)}
              </h2>
              <p className='text-gray-600'>
                Creado{' '}
                {new Date(order.created_at).toLocaleDateString('es-ES', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </p>
            </div>
            <div className='flex items-center gap-2'>
              <span className='text-2xl'>{config.icon}</span>
              <Badge className={`${config.color} text-sm px-3 py-1`}>
                {config.label}
              </Badge>
            </div>
          </div>

          {/* Status Description */}
          <div className='bg-blue-50 border border-blue-200 p-4 rounded-md mb-6'>
            <p className='text-blue-800'>
              {getStatusDescription(order.status)}
            </p>
          </div>

          {/* Progress Bar */}
          <div className='mb-6'>
            <div className='flex items-center justify-between text-sm text-gray-600 mb-2'>
              <span className='font-medium'>Progreso del Pedido</span>
              <span className='text-green-600 font-medium'>
                {config.description}
              </span>
            </div>
            <div className='w-full bg-gray-200 rounded-full h-3'>
              <div
                className={`h-3 rounded-full transition-all duration-500 ${config.progress} ${config.progressColor}`}
              ></div>
            </div>
            <div className='flex justify-between text-xs text-gray-500 mt-2'>
              <span>Pendiente</span>
              <span>Confirmado</span>
              <span>En Tránsito</span>
              <span>Entregado</span>
            </div>
          </div>

          {/* Order Details Grid */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6 mb-6'>
            {/* Pickup Information */}
            <div className='bg-gray-50 p-4 rounded-lg'>
              <h3 className='font-semibold text-gray-900 mb-3 flex items-center'>
                <span className='text-red-500 mr-2'>📍</span>
                Información de Recogida
              </h3>
              <div className='space-y-2 text-sm'>
                <p>
                  <span className='font-medium'>Dirección:</span>{' '}
                  {order.pickup_address?.street_address}
                </p>
                <p>
                  <span className='font-medium'>Ciudad:</span>{' '}
                  {order.pickup_address?.city}, {order.pickup_address?.state}
                </p>
                <p>
                  <span className='font-medium'>Código Postal:</span>{' '}
                  {order.pickup_address?.postal_code}
                </p>
                <p>
                  <span className='font-medium'>Contacto:</span>{' '}
                  {order.pickup_address?.contact_name}
                </p>
                <p>
                  <span className='font-medium'>Teléfono:</span>{' '}
                  {order.pickup_address?.contact_phone}
                </p>
              </div>
            </div>

            {/* Delivery Information */}
            <div className='bg-gray-50 p-4 rounded-lg'>
              <h3 className='font-semibold text-gray-900 mb-3 flex items-center'>
                <span className='text-blue-500 mr-2'>🚚</span>
                Información de Entrega
              </h3>
              <div className='space-y-2 text-sm'>
                <p>
                  <span className='font-medium'>Dirección:</span>{' '}
                  {order.delivery_addresses?.street_address}
                </p>
                <p>
                  <span className='font-medium'>Ciudad:</span>{' '}
                  {order.delivery_addresses?.city},{' '}
                  {order.delivery_addresses?.state}
                </p>
                <p>
                  <span className='font-medium'>Código Postal:</span>{' '}
                  {order.delivery_addresses?.postal_code}
                </p>
                <p>
                  <span className='font-medium'>Destinatario:</span>{' '}
                  {order.delivery_addresses?.recipient_name}
                </p>
                <p>
                  <span className='font-medium'>Teléfono:</span>{' '}
                  {order.delivery_addresses?.phone}
                </p>
              </div>
            </div>
          </div>

          {/* Package Details */}
          <div className='bg-gray-50 p-4 rounded-lg mb-6'>
            <h3 className='font-semibold text-gray-900 mb-3 flex items-center'>
              <span className='text-orange-500 mr-2'>📦</span>
              Detalles del Paquete
            </h3>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='space-y-2 text-sm'>
                <p>
                  <span className='font-medium'>Descripción:</span>{' '}
                  {order.package_details?.description}
                </p>
                <p>
                  <span className='font-medium'>Peso:</span>{' '}
                  {order.package_details?.weight} kg
                </p>
                <p>
                  <span className='font-medium'>Dimensiones:</span>{' '}
                  {order.package_details?.dimensions}
                </p>
                <p>
                  <span className='font-medium'>Valor Declarado:</span> $
                  {order.package_details?.value}
                </p>
              </div>
              <div className='space-y-2 text-sm'>
                <p>
                  <span className='font-medium'>Costo Total:</span>{' '}
                  <span className='text-lg font-bold text-green-600'>
                    ${order.total_cost?.toFixed(2)} MXN
                  </span>
                </p>
                <p>
                  <span className='font-medium'>Estado de Pago:</span>
                  <Badge
                    className={
                      order.payment_status === 'paid'
                        ? 'bg-green-100 text-green-800 ml-2'
                        : 'bg-yellow-100 text-yellow-800 ml-2'
                    }
                  >
                    {order.payment_status === 'paid' ? 'Pagado' : 'Pendiente'}
                  </Badge>
                </p>
                {order.package_details?.special_instructions && (
                  <p>
                    <span className='font-medium'>
                      Instrucciones Especiales:
                    </span>{' '}
                    {order.package_details.special_instructions}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Timestamps */}
          <div className='bg-gray-50 p-4 rounded-lg mb-6'>
            <h3 className='font-semibold text-gray-900 mb-3 flex items-center'>
              <span className='text-gray-500 mr-2'>🕒</span>
              Historial de Tiempos
            </h3>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4 text-sm'>
              <div>
                <p>
                  <span className='font-medium'>Fecha de Creación:</span>{' '}
                  {new Date(order.created_at).toLocaleString('es-ES')}
                </p>
                <p>
                  <span className='font-medium'>Última Actualización:</span>{' '}
                  {new Date(order.updated_at).toLocaleString('es-ES')}
                </p>
              </div>
            </div>
          </div>

          {/* Close Button */}
          <div className='flex justify-end'>
            <button
              onClick={onClose}
              className='px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors'
            >
              Cerrar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export function OrderTracking({ orders, loading }: OrderTrackingProps) {
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);

  if (loading) {
    return (
      <div className='space-y-4'>
        {[1, 2, 3].map(i => (
          <Card key={i} className='animate-pulse'>
            <CardContent className='p-6'>
              <div className='h-4 bg-gray-200 rounded w-1/4 mb-2'></div>
              <div className='h-3 bg-gray-200 rounded w-1/2 mb-4'></div>
              <div className='h-3 bg-gray-200 rounded w-3/4'></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <Card>
        <CardContent className='text-center py-12'>
          <div className='text-6xl mb-4'>📦</div>
          <h3 className='text-lg font-medium text-gray-900 mb-2'>
            Aún no hay pedidos
          </h3>
          <p className='text-gray-600'>
            Crea tu primer pedido para comenzar a rastrear entregas
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className='space-y-4'>
      {orders.map(order => {
        const config = statusConfig[order.status];
        return (
          <Card key={order.id} className='hover:shadow-md transition-shadow'>
            <CardHeader>
              <div className='flex items-center justify-between'>
                <div>
                  <CardTitle className='text-lg'>
                    Pedido #{order.id.slice(-8)}
                  </CardTitle>
                  <CardDescription>
                    Creado {new Date(order.created_at).toLocaleDateString()}
                  </CardDescription>
                </div>
                <div className='flex items-center gap-2'>
                  <span className='text-lg'>{config.icon}</span>
                  <Badge className={config.color}>{config.label}</Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                {/* Pickup Info */}
                <div>
                  <h4 className='font-medium text-gray-900 mb-2'>
                    📍 Recogida
                  </h4>
                  <p className='text-sm text-gray-600'>
                    {order.pickup_address?.street_address}
                    <br />
                    {order.pickup_address?.city}, {order.pickup_address?.state}
                  </p>
                </div>

                {/* Delivery Info */}
                <div>
                  <h4 className='font-medium text-gray-900 mb-2'>🚚 Entrega</h4>
                  {order.delivery_addresses && (
                    <p className='text-sm text-gray-600 mb-1'>
                      {order.delivery_addresses.street_address}
                      <br />
                      {order.delivery_addresses.city},{' '}
                      {order.delivery_addresses.state}
                    </p>
                  )}
                </div>

                {/* Package Info */}
                <div>
                  <h4 className='font-medium text-gray-900 mb-2'>📦 Paquete</h4>
                  <p className='text-sm text-gray-600'>
                    {order.package_details?.description}
                    <br />
                    Peso: {order.package_details?.weight}kg
                  </p>
                  {order.total_cost && (
                    <p className='text-sm font-medium text-gray-900 mt-2'>
                      ${order.total_cost.toFixed(2)} MXN
                    </p>
                  )}
                </div>
              </div>

              {/* Status Progress */}
              <div className='mt-6'>
                <div className='flex items-center justify-between text-sm text-gray-600 mb-2'>
                  <span>Progreso del Pedido</span>
                  <span>{config.description}</span>
                </div>
                <div className='w-full bg-gray-200 rounded-full h-2'>
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${config.progress} ${config.progressColor}`}
                  ></div>
                </div>
                <div className='flex justify-between text-xs text-gray-500 mt-1'>
                  <span>Pendiente</span>
                  <span>Confirmado</span>
                  <span>En Tránsito</span>
                  <span>Entregado</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className='flex justify-end mt-4 space-x-2'>
                <button
                  onClick={() => setSelectedOrder(order)}
                  className='text-sm text-blue-600 hover:text-blue-800'
                >
                  Ver Detalles
                </button>
                {order.status !== 'delivered' &&
                  order.status !== 'cancelled' && (
                    <button className='text-sm text-red-600 hover:text-red-800'>
                      Cancelar Pedido
                    </button>
                  )}
              </div>
            </CardContent>
          </Card>
        );
      })}

      {selectedOrder && (
        <OrderDetailsModal
          order={selectedOrder}
          isOpen={!!selectedOrder}
          onClose={() => setSelectedOrder(null)}
        />
      )}
    </div>
  );
}
