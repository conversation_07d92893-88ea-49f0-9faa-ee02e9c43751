'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LogoutButton } from '@/components/auth/logout-button';
import { createClient } from '@/utils/supabase/client';

const supabase = createClient();

// Helper functions
const getStatusText = (status: string): string => {
  const statusMap: { [key: string]: string } = {
    pending: 'Pendiente',
    confirmed: 'Confirmado',
    'in-transit': 'En tránsito',
    'pending-admin-confirmation': 'Esperando confirmación',
    delivered: 'Entregado',
    closed: 'Cerrado',
    cancelled: 'Cancelado',
  };
  return statusMap[status] || status;
};

const getStatusVariant = (
  status: string
): 'default' | 'secondary' | 'destructive' | 'outline' => {
  switch (status) {
    case 'delivered':
    case 'closed':
      return 'default';
    case 'in-transit':
    case 'confirmed':
      return 'secondary';
    case 'cancelled':
      return 'destructive';
    default:
      return 'outline';
  }
};

// Interfaces for data types
interface CustomerStats {
  totalOrders: number;
  activeDeliveries: number;
  monthlySpending: number;
  totalSpent: number;
}

interface RecentOrder {
  id: string;
  status: string;
  total_cost: number;
  created_at: string;
  pickup_address:
    | {
        street?: string;
        number?: string;
        city?: string;
        state?: string;
        zip?: string;
        contact_name?: string;
        phone?: string;
      }
    | string;
  delivery_addresses: (
    | {
        street?: string;
        number?: string;
        city?: string;
        state?: string;
        zip?: string;
        contact_name?: string;
        phone?: string;
      }
    | string
  )[];
}

interface CustomerProfile {
  id: string;
  user_id: string;
  preferred_name?: string;
  customer_tier?: string;
  loyalty_points?: number;
  total_orders?: number;
  total_spent?: number;
  created_at?: string;
  updated_at?: string;
}

export default function CustomerDashboard() {
  const { user, profile, loading, isCustomer } = useAuthStore();
  const router = useRouter();

  // State for dashboard data
  const [customerStats, setCustomerStats] = useState<CustomerStats>({
    totalOrders: 0,
    activeDeliveries: 0,
    monthlySpending: 0,
    totalSpent: 0,
  });
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([]);
  const [customerProfile, setCustomerProfile] =
    useState<CustomerProfile | null>(null);
  const [dataLoading, setDataLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch customer dashboard data
  const fetchDashboardData = useCallback(async () => {
    if (!user) return;

    setDataLoading(true);
    setError(null);

    try {
      // Fetch customer profile
      const { data: customerData, error: customerError } = await supabase
        .from('customer_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (customerError && customerError.code !== 'PGRST116') {
        console.error('Error fetching customer profile:', customerError);
      } else if (customerData) {
        setCustomerProfile(customerData);
      }

      // Fetch orders for statistics and recent activity
      const { data: orders, error: ordersError } = await supabase
        .from('orders')
        .select('*')
        .eq('customer_id', user.id)
        .order('created_at', { ascending: false });

      if (ordersError) {
        console.error('Error fetching orders:', ordersError);
        setError('Error al cargar los datos del dashboard');
        return;
      }

      // Calculate statistics
      const totalOrders = orders?.length || 0;
      const activeDeliveries =
        orders?.filter(order =>
          ['confirmed', 'in-transit', 'pending-admin-confirmation'].includes(
            order.status || ''
          )
        ).length || 0;

      // Calculate monthly spending (current month)
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const monthlySpending =
        orders
          ?.filter(order => {
            const orderDate = new Date(order.created_at || '');
            return (
              orderDate.getMonth() === currentMonth &&
              orderDate.getFullYear() === currentYear
            );
          })
          .reduce((sum, order) => sum + (order.total_cost || 0), 0) || 0;

      // Calculate total spent
      const totalSpent =
        orders?.reduce((sum, order) => sum + (order.total_cost || 0), 0) || 0;

      setCustomerStats({
        totalOrders,
        activeDeliveries,
        monthlySpending,
        totalSpent,
      });

      // Set recent orders (last 5)
      setRecentOrders(orders?.slice(0, 5) || []);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError('Error al cargar los datos del dashboard');
    } finally {
      setDataLoading(false);
    }
  }, [user]);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/');
      return;
    }

    if (!loading && profile && !isCustomer) {
      router.push('/dashboard');
      return;
    }

    // Fetch dashboard data when user is authenticated and is a customer
    if (!loading && user && isCustomer) {
      fetchDashboardData();
    }
  }, [user, profile, loading, isCustomer, router, fetchDashboardData]);

  if (loading || dataLoading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-black'></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  // Show loading UI if profile is still being fetched
  if (!profile) {
    return (
      <div className='min-h-screen bg-gray-50'>
        <header className='bg-white shadow-sm border-b'>
          <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
            <div className='flex justify-between items-center h-16'>
              <h1 className='text-xl font-bold text-black'>Mouvers</h1>
              <div className='animate-pulse h-6 w-20 bg-gray-200 rounded'></div>
            </div>
          </div>
        </header>
        <main className='max-w-7xl mx-auto py-6 sm:px-6 lg:px-8'>
          <div className='px-4 py-6 sm:px-0'>
            <div className='animate-pulse'>
              <div className='h-8 bg-gray-200 rounded w-1/2 mb-4'></div>
              <div className='h-4 bg-gray-200 rounded w-1/3 mb-8'></div>
              <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
                <div className='h-32 bg-gray-200 rounded'></div>
                <div className='h-32 bg-gray-200 rounded'></div>
                <div className='h-32 bg-gray-200 rounded'></div>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (!isCustomer) {
    return null;
  }

  // Show error state if there's an error
  if (error) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <Card className='w-full max-w-md'>
          <CardHeader>
            <CardTitle className='text-red-600'>Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-gray-600 mb-4'>{error}</p>
            <Button onClick={fetchDashboardData} className='w-full'>
              Reintentar
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Header */}
      <header className='bg-white shadow-sm border-b'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center h-16'>
            <h1 className='text-xl font-bold text-black'>Mouvers</h1>
            <div className='flex items-center space-x-4'>
              <Badge variant='outline'>Cliente</Badge>

              {/* User Dropdown */}
              <div className='relative group'>
                <Button
                  variant='ghost'
                  className='text-sm flex items-center space-x-1'
                >
                  <span>Mi Cuenta</span>
                  <svg
                    className='w-4 h-4'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M19 9l-7 7-7-7'
                    />
                  </svg>
                </Button>

                {/* Dropdown Menu */}
                <div className='absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10'>
                  <div className='py-1'>
                    <Link
                      href='/customer/profile'
                      className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors'
                    >
                      <div className='flex items-center space-x-2'>
                        <svg
                          className='w-4 h-4'
                          fill='none'
                          stroke='currentColor'
                          viewBox='0 0 24 24'
                        >
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'
                          />
                        </svg>
                        <span>Mi Perfil</span>
                      </div>
                    </Link>
                    <Link
                      href='/customer/orders'
                      className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors'
                    >
                      <div className='flex items-center space-x-2'>
                        <svg
                          className='w-4 h-4'
                          fill='none'
                          stroke='currentColor'
                          viewBox='0 0 24 24'
                        >
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4'
                          />
                        </svg>
                        <span>Mis Pedidos</span>
                      </div>
                    </Link>
                    <div className='border-t border-gray-100 my-1'></div>
                    <LogoutButton
                      variant='ghost'
                      className='w-full justify-start px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors h-auto'
                    >
                      <div className='flex items-center space-x-2'>
                        <svg
                          className='w-4 h-4'
                          fill='none'
                          stroke='currentColor'
                          viewBox='0 0 24 24'
                        >
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1'
                          />
                        </svg>
                        <span>Cerrar Sesión</span>
                      </div>
                    </LogoutButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='max-w-7xl mx-auto py-6 sm:px-6 lg:px-8'>
        <div className='px-4 py-6 sm:px-0'>
          {/* Welcome Section */}
          <div className='mb-8'>
            <div className='flex justify-between items-start'>
              <div>
                <h2 className='text-2xl font-bold text-gray-900 mb-2'>
                  Bienvenido,{' '}
                  {customerProfile?.preferred_name ||
                    profile?.full_name ||
                    'Cliente'}
                </h2>
                <p className='text-gray-600'>
                  Gestiona tus entregas y rastrea tus pedidos de manera
                  eficiente
                </p>
                {customerProfile?.customer_tier && (
                  <div className='mt-2'>
                    <Badge variant='outline' className='capitalize'>
                      {customerProfile.customer_tier}
                    </Badge>
                    {customerProfile.loyalty_points &&
                      customerProfile.loyalty_points > 0 && (
                        <Badge variant='secondary' className='ml-2'>
                          {customerProfile.loyalty_points} puntos
                        </Badge>
                      )}
                  </div>
                )}
              </div>
              <Button
                variant='outline'
                size='sm'
                onClick={fetchDashboardData}
                disabled={dataLoading}
                className='flex items-center space-x-2'
              >
                <svg
                  className={`w-4 h-4 ${dataLoading ? 'animate-spin' : ''}`}
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
                  />
                </svg>
                <span>Actualizar</span>
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-8'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>
                  Pedidos Totales
                </CardTitle>
                <Badge variant='secondary'>{customerStats.totalOrders}</Badge>
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>
                  {customerStats.totalOrders}
                </div>
                <p className='text-xs text-gray-600'>
                  {customerStats.totalOrders === 0
                    ? 'Aún no hay pedidos'
                    : `${customerStats.totalOrders} pedido${customerStats.totalOrders !== 1 ? 's' : ''} realizados`}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>
                  Entregas Activas
                </CardTitle>
                <Badge
                  variant={
                    customerStats.activeDeliveries > 0 ? 'default' : 'secondary'
                  }
                >
                  {customerStats.activeDeliveries}
                </Badge>
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>
                  {customerStats.activeDeliveries}
                </div>
                <p className='text-xs text-gray-600'>
                  {customerStats.activeDeliveries === 0
                    ? 'No hay entregas activas'
                    : `${customerStats.activeDeliveries} entrega${customerStats.activeDeliveries !== 1 ? 's' : ''} en proceso`}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Este Mes</CardTitle>
                <Badge variant='secondary'>
                  ${customerStats.monthlySpending.toFixed(2)}
                </Badge>
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>
                  ${customerStats.monthlySpending.toFixed(2)}
                </div>
                <p className='text-xs text-gray-600'>Total gastado este mes</p>
                {customerStats.totalSpent > 0 && (
                  <p className='text-xs text-gray-500 mt-1'>
                    Total histórico: ${customerStats.totalSpent.toFixed(2)}
                  </p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6 mb-8'>
            <Card>
              <CardHeader>
                <CardTitle>Crear Nuevo Pedido</CardTitle>
                <CardDescription>
                  Inicia un nuevo pedido de entrega con uno o múltiples destinos
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href='/customer/orders/new'>
                  <Button className='w-full'>Crear Pedido</Button>
                </Link>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Historial de Pedidos</CardTitle>
                <CardDescription>
                  Ver y rastrear todos tus pedidos anteriores
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href='/customer/orders'>
                  <Button variant='outline' className='w-full'>
                    Ver Pedidos
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>

          {/* Address Management */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6 mb-8'>
            <Card>
              <CardHeader>
                <CardTitle>Mis Direcciones</CardTitle>
                <CardDescription>
                  Gestiona tus direcciones guardadas para entregas rápidas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant='outline' className='w-full'>
                  Gestionar Direcciones
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Mi Perfil</CardTitle>
                <CardDescription>
                  Actualiza tu información personal y preferencias
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href='/customer/profile'>
                  <Button variant='outline' className='w-full'>
                    Ver Perfil
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Actividad Reciente</CardTitle>
              <CardDescription>
                Tus últimos pedidos y actualizaciones de entrega
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentOrders.length === 0 ? (
                <div className='text-center py-8 text-gray-500'>
                  <p>No hay actividad reciente</p>
                  <p className='text-sm'>Crea tu primer pedido para comenzar</p>
                </div>
              ) : (
                <div className='space-y-4'>
                  {recentOrders.map(order => (
                    <div
                      key={order.id}
                      className='flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors'
                    >
                      <div className='flex-1'>
                        <div className='flex items-center space-x-2 mb-1'>
                          <span className='font-medium text-sm'>
                            Pedido #{order.id.slice(-8)}
                          </span>
                          <Badge
                            variant={getStatusVariant(order.status)}
                            className='text-xs'
                          >
                            {getStatusText(order.status)}
                          </Badge>
                        </div>
                        <p className='text-sm text-gray-600'>
                          {new Date(order.created_at).toLocaleDateString(
                            'es-ES',
                            {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit',
                            }
                          )}
                        </p>
                        {order.pickup_address &&
                          typeof order.pickup_address === 'object' && (
                            <p className='text-xs text-gray-500 mt-1'>
                              Desde:{' '}
                              {order.pickup_address.city ||
                                'Dirección de recogida'}
                            </p>
                          )}
                      </div>
                      <div className='text-right'>
                        <p className='font-semibold text-sm'>
                          ${order.total_cost?.toFixed(2) || '0.00'}
                        </p>
                        <Link
                          href={`/customer/orders`}
                          className='text-xs text-blue-600 hover:text-blue-800 transition-colors'
                        >
                          Ver detalles
                        </Link>
                      </div>
                    </div>
                  ))}
                  {recentOrders.length >= 5 && (
                    <div className='text-center pt-4'>
                      <Link href='/customer/orders'>
                        <Button variant='outline' size='sm'>
                          Ver todos los pedidos
                        </Button>
                      </Link>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
