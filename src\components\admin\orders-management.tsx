'use client';

import { useState } from 'react';
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Clock,
  CheckCircle,
  Truck,
  Package,
  XCircle,
  ClipboardList,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { OrderDetailsModal } from './order-details-modal';
import { DriverAssignmentModal } from './driver-assignment-modal';
import { ORDER_STATUS_CONFIG, ORDER_STATUSES } from '@/lib/order-status';

interface Order {
  id: string;
  customer_id: string;
  customer_name: string;
  customer_email: string;
  status:
    | 'pending'
    | 'confirmed'
    | 'in-transit'
    | 'pending-admin-confirmation'
    | 'delivered'
    | 'closed'
    | 'cancelled';
  pickup_address: {
    street_address: string;
    city: string;
    state: string;
    contact_name: string;
    contact_phone: string;
  };
  delivery_addresses: {
    street_address: string;
    city: string;
    state: string;
    recipient_name: string;
    phone: string;
  };
  package_details: {
    description: string;
    weight: string;
    dimensions: string;
    value: string;
  };
  total_cost: number;
  payment_status: 'pending' | 'paid' | 'failed';
  created_at: string;
  updated_at: string;
}

interface OrdersManagementProps {
  orders: Order[];
  isLoading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
  onUpdateOrderStatus: (_orderId: string, _status: Order['status']) => void;
}

const statusConfig = {
  pending: {
    label: 'Pendiente',
    color: 'bg-yellow-100 text-yellow-800',
    icon: <Clock className='w-4 h-4' />,
  },
  confirmed: {
    label: 'Confirmado',
    color: 'bg-blue-100 text-blue-800',
    icon: <CheckCircle className='w-4 h-4' />,
  },
  'in-transit': {
    label: 'En Tránsito',
    color: 'bg-orange-100 text-orange-800',
    icon: <Truck className='w-4 h-4' />,
  },
  'pending-admin-confirmation': {
    label: 'Pendiente Confirmación',
    color: 'bg-orange-100 text-orange-800',
    icon: <Package className='w-4 h-4' />,
  },
  delivered: {
    label: 'Entregado',
    color: 'bg-green-100 text-green-800',
    icon: <Package className='w-4 h-4' />,
  },
  closed: {
    label: 'Cerrado',
    color: 'bg-gray-100 text-gray-800',
    icon: <CheckCircle className='w-4 h-4' />,
  },
  cancelled: {
    label: 'Cancelado',
    color: 'bg-red-100 text-red-800',
    icon: <XCircle className='w-4 h-4' />,
  },
};

const paymentStatusConfig = {
  pending: { label: 'Pago Pendiente', color: 'bg-yellow-100 text-yellow-800' },
  paid: { label: 'Pagado', color: 'bg-green-100 text-green-800' },
  failed: { label: 'Pago Fallido', color: 'bg-red-100 text-red-800' },
};

// Component for managing orders in admin panel
export function OrdersManagement({
  orders,
  onUpdateOrderStatus,
  isLoading,
}: OrdersManagementProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [updatingOrder, setUpdatingOrder] = useState<string | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isDriverAssignmentModalOpen, setIsDriverAssignmentModalOpen] =
    useState(false);
  const [orderForDriverAssignment, setOrderForDriverAssignment] =
    useState<Order | null>(null);

  const filteredOrders = orders.filter(order => {
    const matchesSearch =
      order.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.id.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === 'all' || order.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const handleStatusUpdate = async (
    orderId: string,
    newStatus: Order['status']
  ) => {
    setUpdatingOrder(orderId);
    try {
      await onUpdateOrderStatus(orderId, newStatus);
    } finally {
      setUpdatingOrder(null);
    }
  };

  const handleViewDetails = (order: Order) => {
    setSelectedOrder(order);
    setIsDetailsModalOpen(true);
  };

  const handleAssignDriver = (order: Order) => {
    setOrderForDriverAssignment(order);
    setIsDriverAssignmentModalOpen(true);
  };

  const handleDriverAssignment = async (orderId: string, driverId: string) => {
    try {
      const response = await fetch('/api/orders/assign-driver', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId,
          driverId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Error al asignar repartidor');
      }

      // Update the order status to confirmed after successful assignment
      await onUpdateOrderStatus(orderId, 'confirmed');

      // Close the modal
      setIsDriverAssignmentModalOpen(false);
      setOrderForDriverAssignment(null);
    } catch (error) {
      console.error('Error assigning driver:', error);
      throw error; // Re-throw to let the modal handle the error display
    }
  };

  const handleCloseDetails = () => {
    setIsDetailsModalOpen(false);
    setSelectedOrder(null);
  };

  if (isLoading) {
    return (
      <div className='space-y-4'>
        <div className='animate-pulse'>
          <div className='h-8 bg-gray-200 rounded w-1/4 mb-4'></div>
          <div className='h-10 bg-gray-200 rounded mb-4'></div>
          <div className='space-y-3'>
            {[1, 2, 3, 4, 5].map(i => (
              <div key={i} className='h-20 bg-gray-200 rounded'></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold text-gray-900'>
            Gestión de Pedidos
          </h2>
          <p className='text-gray-600'>
            Gestiona todos los pedidos de clientes y el estado de entrega
          </p>
        </div>
        <div className='text-sm text-gray-500'>
          {filteredOrders.length} de {orders.length} pedidos
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className='p-4'>
          <div className='flex flex-col md:flex-row gap-4'>
            <div className='flex-1'>
              <Input
                placeholder='Buscar por nombre del cliente, email o ID del pedido...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='w-full'
              />
            </div>
            <div className='md:w-48'>
              <Select
                value={statusFilter}
                onChange={e => setStatusFilter(e.target.value)}
                placeholder='Todos los Estados'
              >
                <option value='all'>Todos los Estados</option>
                <option value='pending'>Pendiente</option>
                <option value='confirmed'>Confirmado</option>
                <option value='in-transit'>En Tránsito</option>
                <option value='delivered'>Entregado</option>
                <option value='closed'>Cerrado</option>
                <option value='cancelled'>Cancelado</option>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className='grid grid-cols-1 md:grid-cols-5 gap-4'>
        {Object.entries(statusConfig).map(([status, config]) => {
          const count = orders.filter(order => order.status === status).length;
          return (
            <Card key={status}>
              <CardContent className='p-4'>
                <div className='flex items-center justify-between'>
                  <div>
                    <p className='text-sm font-medium text-gray-600'>
                      {config.label}
                    </p>
                    <p className='text-2xl font-bold text-gray-900'>{count}</p>
                  </div>
                  <div className='text-gray-600'>{config.icon}</div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Orders List */}
      <div className='space-y-4'>
        {filteredOrders.length === 0 ? (
          <Card>
            <CardContent className='text-center py-12'>
              <div className='mb-4'>
                <ClipboardList className='w-16 h-16 mx-auto text-gray-400' />
              </div>
              <h3 className='text-lg font-medium text-gray-900 mb-2'>
                No se encontraron pedidos
              </h3>
              <p className='text-gray-600'>
                {searchTerm || statusFilter !== 'all'
                  ? 'Intenta ajustar tu búsqueda o criterios de filtro'
                  : 'Aún no se han creado pedidos'}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredOrders.map(order => {
            const statusConf = statusConfig[order.status];
            const paymentConf = paymentStatusConfig[order.payment_status];
            const isUpdating = updatingOrder === order.id;

            return (
              <Card
                key={order.id}
                className='hover:shadow-md transition-shadow'
              >
                <CardHeader>
                  <div className='flex items-center justify-between'>
                    <div>
                      <CardTitle className='text-lg'>
                        Pedido #{order.id.slice(-8)}
                      </CardTitle>
                      <CardDescription>
                        Cliente: {order.customer_name} ({order.customer_email})
                      </CardDescription>
                    </div>
                    <div className='flex items-center gap-2'>
                      <Badge className={paymentConf.color}>
                        {paymentConf.label}
                      </Badge>
                      <Badge className={statusConf.color}>
                        <span className='mr-1'>{statusConf.icon}</span>
                        {statusConf.label}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className='grid grid-cols-1 md:grid-cols-4 gap-4 mb-4'>
                    {/* Pickup */}
                    <div>
                      <h4 className='font-medium text-gray-900 mb-1'>
                        📍 Recogida
                      </h4>
                      <p className='text-sm text-gray-600'>
                        {order.pickup_address.contact_name}
                        <br />
                        {order.pickup_address.street_address}
                        <br />
                        {order.pickup_address.city},{' '}
                        {order.pickup_address.state}
                      </p>
                    </div>

                    {/* Delivery */}
                    <div>
                      <h4 className='font-medium text-gray-900 mb-1 flex items-center gap-2'>
                        <Truck className='w-4 h-4' />
                        Entrega
                      </h4>
                      {order.delivery_addresses && (
                        <p className='text-sm text-gray-600 mb-1'>
                          {order.delivery_addresses.recipient_name}
                          <br />
                          {order.delivery_addresses.street_address}
                          <br />
                          {order.delivery_addresses.city},{' '}
                          {order.delivery_addresses.state}
                        </p>
                      )}
                    </div>

                    {/* Package */}
                    <div>
                      <h4 className='font-medium text-gray-900 mb-1 flex items-center gap-2'>
                        <Package className='w-4 h-4' />
                        Paquete
                      </h4>
                      <p className='text-sm text-gray-600'>
                        {order.package_details.description}
                        <br />
                        {order.package_details.weight}
                        <br />
                        Valor: ${order.package_details.value}
                      </p>
                    </div>

                    {/* Order Info */}
                    <div>
                      <h4 className='font-medium text-gray-900 mb-1'>
                        💰 Total del Pedido
                      </h4>
                      <p className='text-lg font-bold text-gray-900'>
                        ${order.total_cost?.toFixed(2) || '0.00'} MXN
                      </p>
                      <p className='text-sm text-gray-600'>
                        Creado:{' '}
                        {new Date(order.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  {/* Status Update */}
                  <div className='border-t pt-4'>
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center gap-4'>
                        <span className='text-sm font-medium text-gray-700'>
                          Actualizar Estado:
                        </span>
                        <Select
                          value={order.status}
                          onChange={e =>
                            handleStatusUpdate(
                              order.id,
                              e.target.value as Order['status']
                            )
                          }
                          disabled={isUpdating}
                          className='w-40'
                        >
                          {ORDER_STATUSES.map(status => (
                            <option key={status} value={status}>
                              {ORDER_STATUS_CONFIG[status].label}
                            </option>
                          ))}
                        </Select>
                        {isUpdating && (
                          <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600'></div>
                        )}
                      </div>

                      <div className='flex items-center gap-2'>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleViewDetails(order)}
                        >
                          Ver Detalles
                        </Button>
                        {order.status === 'pending' && (
                          <>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() => handleAssignDriver(order)}
                            >
                              Asignar Repartidor
                            </Button>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() => {
                                // TODO: Implement customer contact flow
                              }}
                            >
                              Contactar Cliente
                            </Button>
                          </>
                        )}
                        {order.status === 'confirmed' && (
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() => handleAssignDriver(order)}
                          >
                            Asignar Repartidor
                          </Button>
                        )}
                        {order.status === 'in-transit' && (
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() => {
                              // TODO: Implement delivery tracking
                            }}
                          >
                            Seguimiento
                          </Button>
                        )}
                        {order.status === 'pending-admin-confirmation' && (
                          <div className='flex flex-col space-y-2'>
                            <div className='text-xs text-orange-600 bg-orange-50 p-2 rounded border border-orange-200'>
                              📦 El repartidor marcó este paquete como
                              entregado. Confirma la entrega para completar el
                              proceso.
                            </div>
                            <Button
                              variant='outline'
                              size='sm'
                              className='bg-orange-50 text-orange-700 border-orange-200'
                              onClick={() =>
                                handleStatusUpdate(order.id, 'delivered')
                              }
                            >
                              ✅ Confirmar Entrega
                            </Button>
                          </div>
                        )}
                        {order.status === 'delivered' && (
                          <Button
                            variant='outline'
                            size='sm'
                            className='bg-blue-50 text-blue-700 border-blue-200'
                            onClick={() =>
                              handleStatusUpdate(order.id, 'closed')
                            }
                            disabled={updatingOrder === order.id}
                          >
                            {updatingOrder === order.id ? (
                              <>
                                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2'></div>
                                Cerrando...
                              </>
                            ) : (
                              '🔒 Cerrar Orden'
                            )}
                          </Button>
                        )}
                        {order.status === 'closed' && (
                          <Button
                            variant='outline'
                            size='sm'
                            className='bg-gray-50 text-gray-700 border-gray-200'
                            disabled
                          >
                            🔒 Orden Cerrada
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      {/* Order Details Modal */}
      <OrderDetailsModal
        order={selectedOrder}
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetails}
        onUpdateStatus={onUpdateOrderStatus}
      />

      {/* Driver Assignment Modal */}
      <DriverAssignmentModal
        order={orderForDriverAssignment}
        isOpen={isDriverAssignmentModalOpen}
        onClose={() => {
          setIsDriverAssignmentModalOpen(false);
          setOrderForDriverAssignment(null);
        }}
        onAssignDriver={handleDriverAssignment}
      />
    </div>
  );
}
