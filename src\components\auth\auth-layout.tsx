interface AuthLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
}

export function AuthLayout({ children, title, subtitle }: AuthLayoutProps) {
  return (
    <div className='min-h-screen bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8 relative'>
      {/* Imagen de fondo */}
      <div
        className='absolute inset-0 bg-cover bg-center bg-no-repeat'
        style={{ backgroundImage: 'url(/back.png)' }}
      />
      {/* Overlay oscuro para mejorar legibilidad */}
      <div className='absolute inset-0 bg-black/50' />

      {/* Contenido */}
      <div className='sm:mx-auto sm:w-full sm:max-w-md relative z-10'>
        {title && (
          <div className='text-center mb-6'>
            <h1 className='text-3xl font-bold text-white mb-2'>{title}</h1>
            {subtitle && <p className='text-gray-300'>{subtitle}</p>}
          </div>
        )}
        {children}
      </div>
    </div>
  );
}
