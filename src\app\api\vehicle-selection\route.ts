import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { secureAPI, sanitizeInput } from '@/lib/security/api-security';
import { auditLog } from '@/lib/security/audit-logger';

// POST /api/vehicle-selection - Select optimal vehicle for an order
export const POST = secureAPI(
  {
    POST: async (request, context) => {
      try {
        // Parse and sanitize request body
        let requestBody;
        try {
          requestBody = sanitizeInput(await request.json());
        } catch {
          return NextResponse.json(
            { error: 'Formato de cuerpo de solicitud inválido' },
            { status: 400 }
          );
        }

        const {
          packageWeight,
          packageVolume,
          deliveryMode,
          cargoTypeId,
          specialRequirements,
          origin,
          destination,
          stops,
        } = requestBody as {
          packageWeight?: number;
          packageVolume?: number;
          deliveryMode?: 'primera-milla' | 'media-milla' | 'ultima-milla';
          cargoTypeId?: string;
          specialRequirements?: string[];
          origin?: {
            lat: number;
            lng: number;
            address: string;
          };
          destination?: {
            lat: number;
            lng: number;
            address: string;
          };
          stops?: Array<{
            lat: number;
            lng: number;
            address: string;
          }>;
        };

        // Validate required fields
        if (!packageWeight || !packageVolume) {
          return NextResponse.json(
            { error: 'Se requiere peso y volumen del paquete' },
            { status: 400 }
          );
        }

        const supabase = await createClient();

        // Get all vehicle types
        const { data: vehicleTypes, error: vehicleError } = await supabase
          .from('vehicle_types')
          .select('*')
          .order('category', { ascending: true });

        if (vehicleError) {
          console.error('Vehicle types fetch error:', vehicleError);
          return NextResponse.json(
            { error: 'Error al obtener los tipos de vehículos' },
            { status: 500 }
          );
        }

        // Get cargo type details if provided
        let cargoTypeData = null;
        if (cargoTypeId) {
          const { data, error } = await supabase
            .from('cargo_types')
            .select('*')
            .eq('id', cargoTypeId)
            .single();

          if (error) {
            console.error('Cargo type fetch error:', error);
            return NextResponse.json(
              { error: 'Error al obtener el tipo de carga' },
              { status: 500 }
            );
          }
          cargoTypeData = data;
        }

        // Filter and rank vehicles based on criteria
        const rankedVehicles = vehicleTypes
          .map(vehicle => {
            // Calculate suitability score (0-100)
            let score = 0;
            const reasons: string[] = [];

            // Capacity check (weight and volume)
            const weightCapacityScore = Math.min(
              100,
              (packageWeight / vehicle.max_weight_kg) * 100
            );
            const volumeCapacityScore = Math.min(
              100,
              (packageVolume / vehicle.max_volume_m3) * 100
            );

            // If package exceeds vehicle capacity, this vehicle is not suitable
            if (
              packageWeight > vehicle.max_weight_kg ||
              packageVolume > vehicle.max_volume_m3
            ) {
              score = 0;
              reasons.push('Capacidad insuficiente');
              return { ...vehicle, score, reasons };
            }

            // Base score from capacity utilization (higher is better, but not over 80%)
            const capacityUtilization = Math.max(
              weightCapacityScore,
              volumeCapacityScore
            );
            score += 30 - Math.abs(50 - capacityUtilization) * 0.6; // Peak at 50% utilization
            reasons.push(
              `Utilización de capacidad: ${capacityUtilization.toFixed(1)}%`
            );

            // Delivery mode matching
            if (deliveryMode) {
              const modeMatch = checkDeliveryModeCompatibility(
                vehicle.category,
                deliveryMode
              );
              if (modeMatch) {
                score += 20;
                reasons.push(`Compatible con modo ${deliveryMode}`);
              } else {
                score -= 10;
                reasons.push(`No óptimo para modo ${deliveryMode}`);
              }
            }

            // Cargo type compatibility
            if (cargoTypeData && vehicle.special_capabilities) {
              const cargoMatch = checkCargoCompatibility(
                vehicle.special_capabilities,
                cargoTypeData.special_requirements
              );
              if (cargoMatch.match) {
                score += 15;
                reasons.push(
                  `Compatible con tipo de carga: ${cargoMatch.reason}`
                );
              } else {
                score -= 5;
                reasons.push(`Limitaciones con tipo de carga`);
              }
            }

            // Special requirements matching
            if (specialRequirements && specialRequirements.length > 0) {
              const specialMatch = checkSpecialRequirements(
                vehicle.special_capabilities || [],
                specialRequirements
              );
              if (specialMatch.match) {
                score += 15;
                reasons.push(
                  `Cumple requisitos especiales: ${specialMatch.reason}`
                );
              } else {
                score = 0; // Critical requirements not met
                reasons.push(`No cumple requisitos especiales`);
                return { ...vehicle, score: 0, reasons };
              }
            }

            // Distance efficiency (if coordinates provided)
            if (origin && destination) {
              const distance = calculateDistance(
                origin.lat,
                origin.lng,
                destination.lat,
                destination.lng
              );

              // Efficiency based on vehicle category
              const efficiency = getVehicleEfficiency(
                vehicle.category,
                distance
              );
              score += efficiency * 10;
              reasons.push(
                `Eficiencia de vehículo: ${efficiency.toFixed(1)}/10`
              );
            }

            // Multi-stop capability
            const stopsCount = stops?.length || 0;
            if (stopsCount > 0) {
              // Larger vehicles are better for multi-stop deliveries
              if (vehicle.category === 'large') {
                score += 5;
                reasons.push('Apto para múltiples paradas');
              } else if (vehicle.category === 'medium') {
                score += 3;
                reasons.push('Moderadamente apto para múltiples paradas');
              }
            }

            return { ...vehicle, score, reasons };
          })
          .filter(vehicle => vehicle.score > 0) // Only vehicles with positive scores
          .sort((a, b) => b.score - a.score); // Sort by score descending

        // Get top 3 recommendations
        const recommendations = rankedVehicles.slice(0, 3);

        const result = {
          success: true,
          data: {
            recommendations,
            bestMatch: recommendations[0] || null,
            totalVehicles: vehicleTypes.length,
            suitableVehicles: rankedVehicles.length,
          },
        };

        await auditLog({
          event: 'VEHICLE_SELECTED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            packageWeight,
            packageVolume,
            deliveryMode,
            cargoTypeId,
            specialRequirements,
            stopsCount: stops?.length || 0,
            result,
          },
          severity: 'LOW',
          category: 'DATA',
        });

        return NextResponse.json(result);
      } catch (error) {
        await auditLog({
          event: 'VEHICLE_SELECTION_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: false,
    rateLimitRpm: 60,
  }
);

// GET /api/vehicle-selection - Get vehicle selection parameters
export const GET = secureAPI(
  {
    GET: async (request, context) => {
      try {
        const supabase = await createClient();

        // Get all vehicle categories
        const { data: vehicleTypes, error: vehicleError } = await supabase
          .from('vehicle_types')
          .select('category')
          .order('category');

        if (vehicleError) {
          console.error('Vehicle types fetch error:', vehicleError);
          return NextResponse.json(
            { error: 'Error al obtener los tipos de vehículos' },
            { status: 500 }
          );
        }

        // Get unique categories
        const categories = [
          ...new Set(vehicleTypes.map(vt => vt.category)),
        ].sort();

        // Delivery modes
        const deliveryModes = ['primera-milla', 'media-milla', 'ultima-milla'];

        const result = {
          success: true,
          data: {
            vehicleCategories: categories,
            deliveryModes,
          },
        };

        await auditLog({
          event: 'VEHICLE_SELECTION_PARAMETERS_FETCHED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: result,
          severity: 'LOW',
          category: 'DATA',
        });

        return NextResponse.json(result);
      } catch (error) {
        await auditLog({
          event: 'VEHICLE_SELECTION_PARAMETERS_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: false,
    rateLimitRpm: 120,
  }
);

// Helper function to check delivery mode compatibility
function checkDeliveryModeCompatibility(
  vehicleCategory: string,
  deliveryMode: string
): boolean {
  // Define compatibility matrix
  const compatibility: Record<string, string[]> = {
    small: ['ultima-milla'],
    medium: ['media-milla', 'ultima-milla'],
    large: ['primera-milla', 'media-milla'],
    motorcycle: ['ultima-milla'],
    van: ['media-milla', 'ultima-milla'],
    truck: ['primera-milla', 'media-milla'],
  };

  return compatibility[vehicleCategory]?.includes(deliveryMode) || false;
}

// Helper function to check cargo compatibility
function checkCargoCompatibility(
  vehicleCapabilities: string[],
  cargoRequirements: Record<string, unknown> | null
): { match: boolean; reason: string } {
  if (!cargoRequirements) {
    return { match: true, reason: 'Sin requisitos especiales' };
  }

  // Check each requirement
  for (const [requirement, value] of Object.entries(cargoRequirements)) {
    if (value === true && !vehicleCapabilities.includes(requirement)) {
      return { match: false, reason: `Falta capacidad: ${requirement}` };
    }
  }

  return { match: true, reason: 'Todos los requisitos cumplidos' };
}

// Helper function to check special requirements
function checkSpecialRequirements(
  vehicleCapabilities: string[],
  specialRequirements: string[]
): { match: boolean; reason: string } {
  if (specialRequirements.length === 0) {
    return { match: true, reason: 'Sin requisitos especiales' };
  }

  const missing = specialRequirements.filter(
    req => !vehicleCapabilities.includes(req)
  );

  if (missing.length > 0) {
    return {
      match: false,
      reason: `Faltan capacidades: ${missing.join(', ')}`,
    };
  }

  return {
    match: true,
    reason: `Todas las capacidades especiales cumplidas: ${specialRequirements.join(
      ', '
    )}`,
  };
}

// Helper function to calculate distance between two points (simplified)
function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  // Using Haversine formula for distance calculation
  const R = 6371; // Radius of the Earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) *
      Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const d = R * c; // Distance in km
  return d;
}

function deg2rad(deg: number): number {
  return deg * (Math.PI / 180);
}

// Helper function to get vehicle efficiency based on category and distance
function getVehicleEfficiency(category: string, distance: number): number {
  // Efficiency scores (1-10) based on category and distance
  if (distance < 10) {
    // Short distance
    switch (category) {
      case 'motorcycle':
        return 9;
      case 'small':
        return 8;
      case 'medium':
        return 6;
      default:
        return 4;
    }
  } else if (distance < 50) {
    // Medium distance
    switch (category) {
      case 'motorcycle':
        return 6;
      case 'small':
        return 7;
      case 'medium':
        return 8;
      case 'van':
        return 9;
      default:
        return 5;
    }
  } else {
    // Long distance
    switch (category) {
      case 'truck':
        return 9;
      case 'large':
        return 8;
      case 'van':
        return 7;
      default:
        return 4;
    }
  }
}
