/**
 * Complete Mexican Vehicle Types Catalog
 * 
 * Comprehensive vehicle specifications for Mexican logistics operations
 */

export interface VehicleType {
  id: string;
  name: string;
  category: 'motorcycle' | 'light' | 'medium' | 'heavy' | 'specialized';
  subcategory: string;
  max_weight_kg: number;
  max_volume_m3: number;
  base_rate_per_km: number;
  special_capabilities: string[];
  description: string;
  image_path: string;
  dimensions: {
    length_m: number;
    width_m: number;
    height_m: number;
  };
  fuel_efficiency_km_per_liter: number;
  typical_uses: string[];
  restrictions: string[];
  mexican_classification: string;
}

export const MEXICAN_VEHICLE_TYPES: VehicleType[] = [
  // Light Vehicles
  {
    id: 'moto-delivery',
    name: 'Motocicleta de Reparto',
    category: 'motorcycle',
    subcategory: 'Moto/Bicimoto',
    max_weight_kg: 150,
    max_volume_m3: 0.3,
    base_rate_per_km: 8.50,
    special_capabilities: ['urban_delivery', 'fast_delivery', 'traffic_navigation'],
    description: 'Ideal para entregas rápidas en zonas urbanas con tráfico denso',
    image_path: '/vehicules/moto.jpg',
    dimensions: { length_m: 2.2, width_m: 0.8, height_m: 1.3 },
    fuel_efficiency_km_per_liter: 35,
    typical_uses: ['Documentos', 'Paquetes pequeños', 'Comida', 'Medicamentos'],
    restrictions: ['No lluvia intensa', 'Máximo 2 cajas'],
    mexican_classification: 'Vehículo Ligero Clase A'
  },
  {
    id: 'van-panel',
    name: 'Van Panel',
    category: 'light',
    subcategory: 'Van/Panel (1-3 tons)',
    max_weight_kg: 3000,
    max_volume_m3: 12,
    base_rate_per_km: 15.00,
    special_capabilities: ['enclosed_cargo', 'weather_protection', 'security'],
    description: 'Vehículo cerrado ideal para mercancía que requiere protección',
    image_path: '/vehicules/van.jpg',
    dimensions: { length_m: 5.5, width_m: 2.0, height_m: 2.2 },
    fuel_efficiency_km_per_liter: 12,
    typical_uses: ['Electrodomésticos', 'Ropa', 'Productos farmacéuticos', 'Electrónicos'],
    restrictions: ['Altura máxima 2.1m', 'Acceso urbano limitado'],
    mexican_classification: 'Vehículo Ligero Clase B'
  },
  {
    id: 'pickup-truck',
    name: 'Camioneta Pickup',
    category: 'light',
    subcategory: 'Pickup (1-2 tons)',
    max_weight_kg: 2000,
    max_volume_m3: 8,
    base_rate_per_km: 12.00,
    special_capabilities: ['open_cargo', 'construction_materials', 'rural_access'],
    description: 'Versátil para carga abierta y acceso a zonas rurales',
    image_path: '/vehicules/car.jpg',
    dimensions: { length_m: 5.2, width_m: 1.8, height_m: 1.8 },
    fuel_efficiency_km_per_liter: 10,
    typical_uses: ['Materiales de construcción', 'Muebles', 'Productos agrícolas'],
    restrictions: ['Carga no protegida de lluvia'],
    mexican_classification: 'Vehículo Ligero Clase B'
  },

  // Medium Trucks
  {
    id: 'rabon-single',
    name: 'Camión Rabón',
    category: 'medium',
    subcategory: 'Rabón (4-8 tons, single axle)',
    max_weight_kg: 8000,
    max_volume_m3: 35,
    base_rate_per_km: 22.00,
    special_capabilities: ['medium_cargo', 'urban_delivery', 'loading_dock'],
    description: 'Camión mediano ideal para distribución urbana y regional',
    image_path: '/vehicules/rabon.jpg',
    dimensions: { length_m: 7.5, width_m: 2.4, height_m: 3.2 },
    fuel_efficiency_km_per_liter: 8,
    typical_uses: ['Distribución comercial', 'Mudanzas medianas', 'Productos industriales'],
    restrictions: ['Restricciones horarias en algunas ciudades'],
    mexican_classification: 'Vehículo Mediano Clase C1'
  },
  {
    id: 'rabon-extended',
    name: 'Camión Rabón Extendido',
    category: 'medium',
    subcategory: 'Rabón Extendido (6-10 tons)',
    max_weight_kg: 10000,
    max_volume_m3: 45,
    base_rate_per_km: 28.00,
    special_capabilities: ['extended_cargo', 'hydraulic_lift', 'side_loading'],
    description: 'Versión extendida del rabón para mayor capacidad de carga',
    image_path: '/vehicules/truck.jpg',
    dimensions: { length_m: 9.0, width_m: 2.4, height_m: 3.2 },
    fuel_efficiency_km_per_liter: 7,
    typical_uses: ['Distribución mayorista', 'Mudanzas grandes', 'Maquinaria ligera'],
    restrictions: ['Requiere permiso SCT para algunas rutas'],
    mexican_classification: 'Vehículo Mediano Clase C2'
  },

  // Heavy Trucks
  {
    id: 'torton-standard',
    name: 'Camión Tortón',
    category: 'heavy',
    subcategory: 'Tortón (up to 17 tons, double axle)',
    max_weight_kg: 17000,
    max_volume_m3: 65,
    base_rate_per_km: 35.00,
    special_capabilities: ['heavy_cargo', 'long_distance', 'industrial_delivery'],
    description: 'Camión pesado para cargas industriales y larga distancia',
    image_path: '/vehicules/truck.jpg',
    dimensions: { length_m: 12.0, width_m: 2.5, height_m: 3.8 },
    fuel_efficiency_km_per_liter: 5,
    typical_uses: ['Carga industrial', 'Materiales pesados', 'Distribución nacional'],
    restrictions: ['Requiere permiso SCT', 'Restricciones de peso por puente'],
    mexican_classification: 'Vehículo Pesado Clase C3'
  },
  {
    id: 'trailer-dry',
    name: 'Tráiler Caja Seca',
    category: 'heavy',
    subcategory: 'Tráiler/Caja seca (22-25 tons)',
    max_weight_kg: 25000,
    max_volume_m3: 90,
    base_rate_per_km: 45.00,
    special_capabilities: ['maximum_capacity', 'weather_protection', 'long_haul'],
    description: 'Máxima capacidad para transporte de larga distancia',
    image_path: '/vehicules/truck.jpg',
    dimensions: { length_m: 16.5, width_m: 2.6, height_m: 4.0 },
    fuel_efficiency_km_per_liter: 4,
    typical_uses: ['Transporte nacional', 'Carga consolidada', 'Distribución mayorista'],
    restrictions: ['Solo carreteras federales', 'Requiere permisos especiales'],
    mexican_classification: 'Vehículo Articulado Clase T3-S2'
  },
  {
    id: 'double-trailer',
    name: 'Doble Remolque',
    category: 'heavy',
    subcategory: 'Doble remolque (up to 50 tons)',
    max_weight_kg: 50000,
    max_volume_m3: 150,
    base_rate_per_km: 65.00,
    special_capabilities: ['maximum_volume', 'bulk_transport', 'specialized_routes'],
    description: 'Máxima capacidad volumétrica para cargas especializadas',
    image_path: '/vehicules/truck.jpg',
    dimensions: { length_m: 25.0, width_m: 2.6, height_m: 4.0 },
    fuel_efficiency_km_per_liter: 3.5,
    typical_uses: ['Transporte de contenedores', 'Carga a granel', 'Logística industrial'],
    restrictions: ['Rutas específicas autorizadas', 'Permisos especiales obligatorios'],
    mexican_classification: 'Vehículo Articulado Clase T3-S2-R4'
  },

  // Specialized Vehicles
  {
    id: 'refrigerated-truck',
    name: 'Camión Refrigerado',
    category: 'specialized',
    subcategory: 'Refrigerated',
    max_weight_kg: 12000,
    max_volume_m3: 40,
    base_rate_per_km: 40.00,
    special_capabilities: ['temperature_control', 'cold_chain', 'perishable_goods'],
    description: 'Transporte con control de temperatura para productos perecederos',
    image_path: '/vehicules/truck.jpg',
    dimensions: { length_m: 10.0, width_m: 2.4, height_m: 3.5 },
    fuel_efficiency_km_per_liter: 6,
    typical_uses: ['Alimentos frescos', 'Medicamentos', 'Productos lácteos', 'Carnes'],
    restrictions: ['Requiere certificación sanitaria', 'Mantenimiento especializado'],
    mexican_classification: 'Vehículo Especializado Refrigerado'
  },
  {
    id: 'platform-truck',
    name: 'Camión Plataforma',
    category: 'specialized',
    subcategory: 'Platform/low bed',
    max_weight_kg: 20000,
    max_volume_m3: 80,
    base_rate_per_km: 50.00,
    special_capabilities: ['oversized_cargo', 'machinery_transport', 'construction_equipment'],
    description: 'Plataforma baja para maquinaria y cargas sobredimensionadas',
    image_path: '/vehicules/truck.jpg',
    dimensions: { length_m: 15.0, width_m: 2.5, height_m: 1.2 },
    fuel_efficiency_km_per_liter: 4.5,
    typical_uses: ['Maquinaria pesada', 'Vehículos', 'Estructuras metálicas'],
    restrictions: ['Requiere escoltas', 'Permisos de sobredimensionado'],
    mexican_classification: 'Vehículo Especializado Plataforma'
  },
  {
    id: 'livestock-carrier',
    name: 'Camión Ganadero',
    category: 'specialized',
    subcategory: 'Livestock carriers',
    max_weight_kg: 15000,
    max_volume_m3: 55,
    base_rate_per_km: 38.00,
    special_capabilities: ['livestock_transport', 'ventilation', 'animal_welfare'],
    description: 'Transporte especializado para ganado y animales vivos',
    image_path: '/vehicules/truck.jpg',
    dimensions: { length_m: 12.0, width_m: 2.4, height_m: 3.0 },
    fuel_efficiency_km_per_liter: 5.5,
    typical_uses: ['Ganado bovino', 'Cerdos', 'Aves de corral', 'Caballos'],
    restrictions: ['Certificación SENASICA', 'Rutas sanitarias específicas'],
    mexican_classification: 'Vehículo Especializado Ganadero'
  }
];

// Cargo type definitions
export interface CargoType {
  id: string;
  name: string;
  category: 'general' | 'fragile' | 'perishable' | 'hazardous' | 'valuable' | 'oversized';
  rate_multiplier: number;
  special_requirements: {
    temperature_control?: boolean;
    special_handling?: boolean;
    insurance_required?: boolean;
    permits_required?: boolean;
    specialized_vehicle?: boolean;
  };
  description: string;
  compatible_vehicles: string[]; // Vehicle category compatibility
}

export const MEXICAN_CARGO_TYPES: CargoType[] = [
  {
    id: 'general-cargo',
    name: 'Carga General',
    category: 'general',
    rate_multiplier: 1.0,
    special_requirements: {},
    description: 'Mercancía general sin requerimientos especiales',
    compatible_vehicles: ['motorcycle', 'light', 'medium', 'heavy']
  },
  {
    id: 'fragile-cargo',
    name: 'Carga Frágil',
    category: 'fragile',
    rate_multiplier: 1.3,
    special_requirements: {
      special_handling: true,
      insurance_required: true
    },
    description: 'Productos que requieren manejo cuidadoso',
    compatible_vehicles: ['light', 'medium', 'heavy']
  },
  {
    id: 'perishable-cargo',
    name: 'Carga Perecedera',
    category: 'perishable',
    rate_multiplier: 1.5,
    special_requirements: {
      temperature_control: true,
      specialized_vehicle: true
    },
    description: 'Productos que requieren control de temperatura',
    compatible_vehicles: ['specialized']
  },
  {
    id: 'hazardous-cargo',
    name: 'Carga Peligrosa',
    category: 'hazardous',
    rate_multiplier: 2.0,
    special_requirements: {
      permits_required: true,
      special_handling: true,
      insurance_required: true
    },
    description: 'Materiales peligrosos que requieren permisos especiales',
    compatible_vehicles: ['medium', 'heavy', 'specialized']
  },
  {
    id: 'valuable-cargo',
    name: 'Carga Valiosa',
    category: 'valuable',
    rate_multiplier: 1.8,
    special_requirements: {
      insurance_required: true,
      special_handling: true
    },
    description: 'Productos de alto valor que requieren seguridad adicional',
    compatible_vehicles: ['light', 'medium', 'heavy']
  },
  {
    id: 'oversized-cargo',
    name: 'Carga Sobredimensionada',
    category: 'oversized',
    rate_multiplier: 2.5,
    special_requirements: {
      permits_required: true,
      specialized_vehicle: true
    },
    description: 'Carga que excede dimensiones estándar',
    compatible_vehicles: ['specialized']
  }
];

// Vehicle selection algorithm
export function suggestVehicleTypes(
  totalWeight: number,
  totalVolume: number,
  specialHandling: any,
  cargoTypes: string[] = []
): VehicleType[] {
  const suggestions = MEXICAN_VEHICLE_TYPES.filter(vehicle => {
    // Check weight and volume capacity
    if (totalWeight > vehicle.max_weight_kg || totalVolume > vehicle.max_volume_m3) {
      return false;
    }

    // Check special handling requirements
    if (specialHandling.refrigerated && !vehicle.special_capabilities.includes('temperature_control')) {
      return false;
    }

    if (specialHandling.hazardous && vehicle.category === 'motorcycle') {
      return false;
    }

    if (specialHandling.oversized && vehicle.category !== 'specialized') {
      return false;
    }

    return true;
  });

  // Sort by efficiency (cost per kg capacity)
  return suggestions.sort((a, b) => {
    const efficiencyA = a.base_rate_per_km / a.max_weight_kg;
    const efficiencyB = b.base_rate_per_km / b.max_weight_kg;
    return efficiencyA - efficiencyB;
  });
}
