'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  CheckCircle,
  Wallet,
  ArrowLeft,
  ExternalLink,
  Clock,
} from 'lucide-react';
import { Suspense } from 'react';

export default function PaymentSuccessPage() {
  return (
    <Suspense
      fallback={
        <div className='min-h-screen flex items-center justify-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-black' />
        </div>
      }
    >
      <PaymentSuccessContent />
    </Suspense>
  );
}

interface Transaction {
  id: string;
  type: string;
  amount: number;
  description: string;
  status: string;
  stripe_payment_intent_id: string | null;
  created_at: string;
  transaction_ref: string | null;
}

interface BalanceData {
  balance: number;
  wallet_id: string | null;
  recent_transactions: Transaction[];
  success: boolean;
}

function PaymentSuccessContent() {
  const { user, loading } = useAuthStore();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [balanceData, setBalanceData] = useState<BalanceData | null>(null);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/');
      return;
    }

    // Fetch updated balance and transaction history
    const fetchBalanceData = async () => {
      if (!user) return;

      try {
        const response = await fetch('/api/user/balance', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ userId: user.id }),
        });

        if (response.ok) {
          const data = await response.json();
          setBalanceData(data);
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error fetching balance data:', error);
        }
      }
    };

    fetchBalanceData();
  }, [user, loading, router]);

  const sessionId = searchParams.get('session_id');
  const amount = searchParams.get('amount');
  const transactionRef = searchParams.get('ref');

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-black'></div>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-MX', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTransactionColor = (type: string, status: string) => {
    if (status === 'failed') return 'text-red-600';
    if (status === 'pending') return 'text-yellow-600';
    if (type === 'deposit') return 'text-green-600';
    if (type === 'charge' || type === 'withdrawal') return 'text-red-600';
    return 'text-gray-600';
  };

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Header */}
      <header className='bg-white shadow-sm border-b'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center h-16'>
            <div className='flex items-center space-x-4'>
              <Button
                variant='ghost'
                onClick={() => router.push('/customer/profile')}
                className='text-blue-600'
              >
                <ArrowLeft className='w-4 h-4 mr-2' />
                Back to Profile
              </Button>
              <h1 className='text-xl font-bold text-black'>
                Payment Successful
              </h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8 space-y-6'>
        {/* Success Card */}
        <Card>
          <CardHeader className='text-center'>
            <div className='mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4'>
              <CheckCircle className='w-8 h-8 text-green-600' />
            </div>
            <CardTitle className='text-2xl text-green-600'>
              Payment Successful!
            </CardTitle>
            <CardDescription>
              Your wallet has been topped up successfully
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='text-center'>
              {amount && (
                <div className='mb-4'>
                  <p className='text-sm text-gray-600'>Amount Added</p>
                  <p className='text-2xl font-bold text-green-600'>
                    ${parseFloat(amount).toFixed(2)} MXN
                  </p>
                </div>
              )}

              {balanceData && (
                <div className='bg-gray-50 p-4 rounded-lg'>
                  <div className='flex items-center justify-center gap-2 mb-2'>
                    <Wallet className='w-5 h-5 text-gray-600' />
                    <span className='text-sm font-medium text-gray-700'>
                      Current Balance
                    </span>
                  </div>
                  <p className='text-xl font-bold text-gray-900'>
                    ${balanceData.balance.toFixed(2)} MXN
                  </p>
                </div>
              )}
            </div>

            {/* Transaction Details */}
            <div className='space-y-3'>
              {transactionRef && (
                <div className='bg-blue-50 p-4 rounded-lg'>
                  <p className='text-sm text-blue-700'>
                    <strong>Transaction Reference:</strong> {transactionRef}
                  </p>
                  <p className='text-xs text-blue-600 mt-1'>
                    Internal tracking reference
                  </p>
                </div>
              )}

              {sessionId && (
                <div className='bg-purple-50 p-4 rounded-lg'>
                  <p className='text-sm text-purple-700'>
                    <strong>Stripe Session ID:</strong> {sessionId}
                  </p>
                  <p className='text-xs text-purple-600 mt-1'>
                    Keep this for your records
                  </p>
                </div>
              )}
            </div>

            <div className='flex gap-3'>
              <Button
                variant='outline'
                onClick={() =>
                  router.push('/customer/profile?payment_success=true')
                }
                className='flex-1'
              >
                View Profile
              </Button>
              <Button
                onClick={() => router.push('/customer/orders/new')}
                className='flex-1'
              >
                Create Order
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Transactions */}
        {balanceData?.recent_transactions &&
          balanceData.recent_transactions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Clock className='w-5 h-5' />
                  Recent Transactions
                </CardTitle>
                <CardDescription>
                  Your wallet transaction history with Stripe correlation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-3'>
                  {balanceData.recent_transactions.map(tx => (
                    <div
                      key={tx.id}
                      className='flex items-center justify-between p-3 border rounded-lg'
                    >
                      <div className='flex-1'>
                        <div className='flex items-center gap-2'>
                          <span
                            className={`font-medium ${getTransactionColor(tx.type, tx.status)}`}
                          >
                            {tx.type === 'deposit' ? '+' : '-'}$
                            {tx.amount.toFixed(2)} MXN
                          </span>
                          <span
                            className={`text-xs px-2 py-1 rounded-full ${
                              tx.status === 'completed'
                                ? 'bg-green-100 text-green-700'
                                : tx.status === 'pending'
                                  ? 'bg-yellow-100 text-yellow-700'
                                  : 'bg-red-100 text-red-700'
                            }`}
                          >
                            {tx.status}
                          </span>
                        </div>
                        <p className='text-sm text-gray-600 mt-1'>
                          {tx.description}
                        </p>
                        <div className='flex items-center gap-4 mt-2 text-xs text-gray-500'>
                          <span>{formatDate(tx.created_at)}</span>
                          {tx.transaction_ref && (
                            <span>Ref: {tx.transaction_ref}</span>
                          )}
                        </div>
                      </div>
                      {tx.stripe_payment_intent_id && (
                        <div className='flex items-center gap-2 text-xs text-blue-600'>
                          <ExternalLink className='w-3 h-3' />
                          <span>
                            Stripe: {tx.stripe_payment_intent_id.slice(-8)}
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
      </main>
    </div>
  );
}
