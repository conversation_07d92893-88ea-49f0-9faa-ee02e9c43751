'use client';

import { useState } from 'react';
import { useAuthStore } from '@/stores/authStore';
import { createClient } from '@/utils/supabase/client';
import { Button } from '@/components/ui/button';

interface TestResult {
  clientSession?: {
    hasSession: boolean;
    hasAccessToken: boolean;
    userId?: string;
    userEmail?: string;
    sessionError?: string;
  };
  authStore?: {
    hasUser: boolean;
    hasProfile: boolean;
    isAdmin: boolean;
    userId?: string;
    userEmail?: string;
    profileRole?: string;
  };
  apiTest?: {
    status: number;
    result: unknown;
  };
  orderUpdateTest?: {
    status: number;
    result: unknown;
  };
  error?: string;
}

export default function TestAuthPage() {
  const { user, profile, isAdmin } = useAuthStore();
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [loading, setLoading] = useState(false);
  const supabase = createClient();

  const testAuthentication = async () => {
    setLoading(true);
    try {
      // Test 1: Get current session
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();

      // Test 2: Test API call with authorization header
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (session?.access_token) {
        headers['Authorization'] = `Bearer ${session.access_token}`;
      }

      const response = await fetch('/api/orders/debug', {
        method: 'GET',
        headers,
      });

      const apiResult = await response.json();

      setTestResult({
        clientSession: {
          hasSession: !!session,
          hasAccessToken: !!session?.access_token,
          userId: session?.user?.id,
          userEmail: session?.user?.email,
          sessionError: sessionError?.message,
        },
        authStore: {
          hasUser: !!user,
          hasProfile: !!profile,
          isAdmin,
          userId: user?.id,
          userEmail: user?.email,
          profileRole: profile?.role,
        },
        apiTest: {
          status: response.status,
          result: apiResult,
        },
      });
    } catch (error) {
      setTestResult({
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setLoading(false);
    }
  };

  const testOrderUpdate = async () => {
    setLoading(true);
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (session?.access_token) {
        headers['Authorization'] = `Bearer ${session.access_token}`;
      }

      // Test with a fake order ID to see what error we get
      const response = await fetch('/api/orders/update-status', {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          orderId: '12345678-1234-1234-1234-123456789012', // Valid UUID format
          newStatus: 'confirmed',
        }),
      });

      const result = await response.json();

      setTestResult({
        orderUpdateTest: {
          status: response.status,
          result,
        },
      });
    } catch (error) {
      setTestResult({
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='container mx-auto p-6'>
      <h1 className='text-2xl font-bold mb-6'>Authentication Test Page</h1>

      <div className='space-y-4 mb-6'>
        <Button onClick={testAuthentication} disabled={loading}>
          Test Authentication
        </Button>
        <Button onClick={testOrderUpdate} disabled={loading}>
          Test Order Update API
        </Button>
      </div>

      {loading && <p>Testing...</p>}

      {testResult && (
        <div className='bg-gray-100 p-4 rounded-lg'>
          <h2 className='text-lg font-semibold mb-2'>Test Results:</h2>
          <pre className='text-sm overflow-auto'>
            {JSON.stringify(testResult, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
