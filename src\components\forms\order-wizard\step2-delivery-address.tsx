'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { OrderFormData } from '@/types/order-form';

interface Step2Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
}

export function OrderWizardStep2({ formData, updateFormData }: Step2Props) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          📍 Dirección de Entrega
        </CardTitle>
        <CardDescription>
          Dirección donde se entregará tu pedido
        </CardDescription>
      </CardHeader>
      <CardContent className='grid grid-cols-1 md:grid-cols-2 gap-4'>
        <div className='space-y-2'>
          <Label htmlFor='street'>Calle</Label>
          <Input
            id='street'
            value={formData.delivery_address.street}
            onChange={e =>
              updateFormData({
                delivery_address: {
                  ...formData.delivery_address,
                  street: e.target.value,
                },
              })
            }
            placeholder='ej., Av. Reforma, Calle 5 de Febrero'
            required
          />
        </div>
        <div className='space-y-2'>
          <Label htmlFor='number'>Número Exterior</Label>
          <Input
            id='number'
            value={formData.delivery_address.number || ''}
            onChange={e =>
              updateFormData({
                delivery_address: {
                  ...formData.delivery_address,
                  number: e.target.value,
                },
              })
            }
            placeholder='ej., 123, S/N'
          />
        </div>
        <div className='space-y-2'>
          <Label htmlFor='colony'>Colonia</Label>
          <Input
            id='colony'
            value={formData.delivery_address.colony}
            onChange={e =>
              updateFormData({
                delivery_address: {
                  ...formData.delivery_address,
                  colony: e.target.value,
                },
              })
            }
            placeholder='ej., Roma Norte, Polanco'
            required
          />
        </div>
        <div className='space-y-2'>
          <Label htmlFor='city'>Ciudad</Label>
          <Input
            id='city'
            value={formData.delivery_address.city}
            onChange={e =>
              updateFormData({
                delivery_address: {
                  ...formData.delivery_address,
                  city: e.target.value,
                },
              })
            }
            placeholder='ej., Ciudad de México, Guadalajara'
            required
          />
        </div>
        <div className='space-y-2'>
          <Label htmlFor='state'>Estado</Label>
          <select
            id='state'
            value={formData.delivery_address.state}
            onChange={e =>
              updateFormData({
                delivery_address: {
                  ...formData.delivery_address,
                  state: e.target.value,
                },
              })
            }
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            required
          >
            <option value=''>Seleccionar estado</option>
            <option value='Aguascalientes'>Aguascalientes</option>
            <option value='Baja California'>Baja California</option>
            <option value='Baja California Sur'>Baja California Sur</option>
            <option value='Campeche'>Campeche</option>
            <option value='Chiapas'>Chiapas</option>
            <option value='Chihuahua'>Chihuahua</option>
            <option value='Ciudad de México'>Ciudad de México</option>
            <option value='Coahuila'>Coahuila</option>
            <option value='Colima'>Colima</option>
            <option value='Durango'>Durango</option>
            <option value='Guanajuato'>Guanajuato</option>
            <option value='Guerrero'>Guerrero</option>
            <option value='Hidalgo'>Hidalgo</option>
            <option value='Jalisco'>Jalisco</option>
            <option value='México'>México</option>
            <option value='Michoacán'>Michoacán</option>
            <option value='Morelos'>Morelos</option>
            <option value='Nayarit'>Nayarit</option>
            <option value='Nuevo León'>Nuevo León</option>
            <option value='Oaxaca'>Oaxaca</option>
            <option value='Puebla'>Puebla</option>
            <option value='Querétaro'>Querétaro</option>
            <option value='Quintana Roo'>Quintana Roo</option>
            <option value='San Luis Potosí'>San Luis Potosí</option>
            <option value='Sinaloa'>Sinaloa</option>
            <option value='Sonora'>Sonora</option>
            <option value='Tabasco'>Tabasco</option>
            <option value='Tamaulipas'>Tamaulipas</option>
            <option value='Tlaxcala'>Tlaxcala</option>
            <option value='Veracruz'>Veracruz</option>
            <option value='Yucatán'>Yucatán</option>
            <option value='Zacatecas'>Zacatecas</option>
          </select>
        </div>
        <div className='space-y-2'>
          <Label htmlFor='zip'>Código Postal</Label>
          <Input
            id='zip'
            value={formData.delivery_address.zip}
            onChange={e =>
              updateFormData({
                delivery_address: {
                  ...formData.delivery_address,
                  zip: e.target.value,
                },
              })
            }
            placeholder='ej., 06700'
            required
          />
        </div>
        <div className='space-y-2 md:col-span-2'>
          <Label htmlFor='references'>Referencias (Opcional)</Label>
          <Input
            id='references'
            value={formData.delivery_address.references || ''}
            onChange={e =>
              updateFormData({
                delivery_address: {
                  ...formData.delivery_address,
                  references: e.target.value,
                },
              })
            }
            placeholder='ej., cerca del parque, frente a la farmacia'
          />
        </div>
      </CardContent>
    </Card>
  );
}
