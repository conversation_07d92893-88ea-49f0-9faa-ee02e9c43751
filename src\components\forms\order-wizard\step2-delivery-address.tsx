'use client';

import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { OrderFormData } from '@/types/order-form';
import {
  AddressAutocompleteService,
  AddressSuggestion,
} from '@/lib/services/address-autocomplete';

interface Step2Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
  errors?: Record<string, string>;
}

export function OrderWizardStep2({
  formData,
  updateFormData,
  errors = {},
}: Step2Props) {
  const [addressSuggestions, setAddressSuggestions] = useState<
    AddressSuggestion[]
  >([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [useCurrentLocation, setUseCurrentLocation] = useState(false);

  // Debounced address search
  useEffect(() => {
    if (searchQuery.length < 3) {
      setAddressSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    const timeoutId = setTimeout(async () => {
      setIsSearching(true);
      try {
        const suggestions = await AddressAutocompleteService.searchAddresses(
          searchQuery,
          5
        );
        setAddressSuggestions(suggestions);
        setShowSuggestions(true);
      } catch (error) {
        console.error('Address search error:', error);
      } finally {
        setIsSearching(false);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  const handleAddressSelect = (suggestion: AddressSuggestion) => {
    updateFormData({
      delivery_address: {
        ...formData.delivery_address,
        street: suggestion.street,
        number: suggestion.number || '',
        colony: suggestion.colony,
        city: suggestion.city,
        state: suggestion.state as OrderFormData['delivery_address']['state'],
        zip: suggestion.zip,
        coordinates: suggestion.coordinates,
      },
    });
    setShowSuggestions(false);
    setSearchQuery('');
  };

  const handleCurrentLocation = async () => {
    setUseCurrentLocation(true);
    try {
      const result = await AddressAutocompleteService.getCurrentLocation();
      if (result.success && result.coordinates) {
        updateFormData({
          delivery_address: {
            ...formData.delivery_address,
            coordinates: result.coordinates,
          },
        });
        if (result.address) {
          setSearchQuery(result.address);
        }
      } else {
        alert(result.error || 'No se pudo obtener la ubicación actual');
      }
    } catch (error) {
      console.error('Geolocation error:', error);
      alert('Error al obtener la ubicación actual');
    } finally {
      setUseCurrentLocation(false);
    }
  };

  // Helper function to get error for nested fields
  const getNestedError = (fieldPath: string): string | undefined => {
    return errors[fieldPath];
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          📍 Dirección de Entrega
        </CardTitle>
        <CardDescription>
          Dirección donde se entregará tu pedido
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-6'>
        {/* Address Search */}
        <div className='space-y-2'>
          <Label htmlFor='address_search'>Buscar Dirección</Label>
          <div className='relative'>
            <div className='flex gap-2'>
              <Input
                id='address_search'
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                placeholder='Escribe tu dirección completa...'
                className='flex-1'
              />
              <Button
                type='button'
                variant='outline'
                onClick={handleCurrentLocation}
                disabled={useCurrentLocation}
                className='whitespace-nowrap'
              >
                {useCurrentLocation ? '📍 Obteniendo...' : '📍 Mi Ubicación'}
              </Button>
            </div>

            {/* Address Suggestions */}
            {showSuggestions && addressSuggestions.length > 0 && (
              <div className='absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto'>
                {addressSuggestions.map(suggestion => (
                  <button
                    key={suggestion.id}
                    type='button'
                    onClick={() => handleAddressSelect(suggestion)}
                    className='w-full text-left px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0'
                  >
                    <div className='font-medium text-sm'>
                      {suggestion.display_name}
                    </div>
                    <div className='text-xs text-gray-600'>
                      Confianza: {Math.round(suggestion.confidence * 100)}%
                    </div>
                  </button>
                ))}
              </div>
            )}

            {isSearching && (
              <div className='absolute right-3 top-3'>
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600'></div>
              </div>
            )}
          </div>
        </div>

        {/* Manual Address Entry */}
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label htmlFor='street'>Calle</Label>
            <Input
              id='street'
              value={formData.delivery_address.street}
              onChange={e =>
                updateFormData({
                  delivery_address: {
                    ...formData.delivery_address,
                    street: e.target.value,
                  },
                })
              }
              placeholder='ej., Av. Reforma, Calle 5 de Febrero'
              required
              className={
                getNestedError('delivery_address.street')
                  ? 'border-red-500'
                  : ''
              }
            />
            {getNestedError('delivery_address.street') && (
              <p className='text-sm text-red-600'>
                {getNestedError('delivery_address.street')}
              </p>
            )}
          </div>
          <div className='space-y-2'>
            <Label htmlFor='number'>Número Exterior</Label>
            <Input
              id='number'
              value={formData.delivery_address.number || ''}
              onChange={e =>
                updateFormData({
                  delivery_address: {
                    ...formData.delivery_address,
                    number: e.target.value,
                  },
                })
              }
              placeholder='ej., 123, S/N'
              className={
                getNestedError('delivery_address.number')
                  ? 'border-red-500'
                  : ''
              }
            />
            {getNestedError('delivery_address.number') && (
              <p className='text-sm text-red-600'>
                {getNestedError('delivery_address.number')}
              </p>
            )}
          </div>
          <div className='space-y-2'>
            <Label htmlFor='colony'>Colonia</Label>
            <Input
              id='colony'
              value={formData.delivery_address.colony}
              onChange={e =>
                updateFormData({
                  delivery_address: {
                    ...formData.delivery_address,
                    colony: e.target.value,
                  },
                })
              }
              placeholder='ej., Roma Norte, Polanco'
              required
              className={
                getNestedError('delivery_address.colony')
                  ? 'border-red-500'
                  : ''
              }
            />
            {getNestedError('delivery_address.colony') && (
              <p className='text-sm text-red-600'>
                {getNestedError('delivery_address.colony')}
              </p>
            )}
          </div>
          <div className='space-y-2'>
            <Label htmlFor='city'>Ciudad</Label>
            <Input
              id='city'
              value={formData.delivery_address.city}
              onChange={e =>
                updateFormData({
                  delivery_address: {
                    ...formData.delivery_address,
                    city: e.target.value,
                  },
                })
              }
              placeholder='ej., Ciudad de México, Guadalajara'
              required
              className={
                getNestedError('delivery_address.city') ? 'border-red-500' : ''
              }
            />
            {getNestedError('delivery_address.city') && (
              <p className='text-sm text-red-600'>
                {getNestedError('delivery_address.city')}
              </p>
            )}
          </div>
          <div className='space-y-2'>
            <Label htmlFor='state'>Estado</Label>
            <select
              id='state'
              value={formData.delivery_address.state}
              onChange={e =>
                updateFormData({
                  delivery_address: {
                    ...formData.delivery_address,
                    state: e.target
                      .value as OrderFormData['delivery_address']['state'],
                  },
                })
              }
              className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                getNestedError('delivery_address.state') ? 'border-red-500' : ''
              }`}
              required
            >
              <option value=''>Seleccionar estado</option>
              <option value='Aguascalientes'>Aguascalientes</option>
              <option value='Baja California'>Baja California</option>
              <option value='Baja California Sur'>Baja California Sur</option>
              <option value='Campeche'>Campeche</option>
              <option value='Chiapas'>Chiapas</option>
              <option value='Chihuahua'>Chihuahua</option>
              <option value='Ciudad de México'>Ciudad de México</option>
              <option value='Coahuila'>Coahuila</option>
              <option value='Colima'>Colima</option>
              <option value='Durango'>Durango</option>
              <option value='Guanajuato'>Guanajuato</option>
              <option value='Guerrero'>Guerrero</option>
              <option value='Hidalgo'>Hidalgo</option>
              <option value='Jalisco'>Jalisco</option>
              <option value='México'>México</option>
              <option value='Michoacán'>Michoacán</option>
              <option value='Morelos'>Morelos</option>
              <option value='Nayarit'>Nayarit</option>
              <option value='Nuevo León'>Nuevo León</option>
              <option value='Oaxaca'>Oaxaca</option>
              <option value='Puebla'>Puebla</option>
              <option value='Querétaro'>Querétaro</option>
              <option value='Quintana Roo'>Quintana Roo</option>
              <option value='San Luis Potosí'>San Luis Potosí</option>
              <option value='Sinaloa'>Sinaloa</option>
              <option value='Sonora'>Sonora</option>
              <option value='Tabasco'>Tabasco</option>
              <option value='Tamaulipas'>Tamaulipas</option>
              <option value='Tlaxcala'>Tlaxcala</option>
              <option value='Veracruz'>Veracruz</option>
              <option value='Yucatán'>Yucatán</option>
              <option value='Zacatecas'>Zacatecas</option>
            </select>
            {getNestedError('delivery_address.state') && (
              <p className='text-sm text-red-600'>
                {getNestedError('delivery_address.state')}
              </p>
            )}
          </div>
          <div className='space-y-2'>
            <Label htmlFor='zip'>Código Postal</Label>
            <Input
              id='zip'
              value={formData.delivery_address.zip}
              onChange={e =>
                updateFormData({
                  delivery_address: {
                    ...formData.delivery_address,
                    zip: e.target.value,
                  },
                })
              }
              placeholder='ej., 06700'
              required
              className={
                getNestedError('delivery_address.zip') ? 'border-red-500' : ''
              }
            />
            {getNestedError('delivery_address.zip') && (
              <p className='text-sm text-red-600'>
                {getNestedError('delivery_address.zip')}
              </p>
            )}
          </div>
          <div className='space-y-2 md:col-span-2'>
            <Label htmlFor='references'>Referencias (Opcional)</Label>
            <Input
              id='references'
              value={formData.delivery_address.references || ''}
              onChange={e =>
                updateFormData({
                  delivery_address: {
                    ...formData.delivery_address,
                    references: e.target.value,
                  },
                })
              }
              placeholder='ej., cerca del parque, frente a la farmacia'
              className={
                getNestedError('delivery_address.references')
                  ? 'border-red-500'
                  : ''
              }
            />
            {getNestedError('delivery_address.references') && (
              <p className='text-sm text-red-600'>
                {getNestedError('delivery_address.references')}
              </p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
