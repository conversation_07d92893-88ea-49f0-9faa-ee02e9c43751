import { useState, useEffect, useRef } from 'react';
import { supabase } from '@/lib/supabase';

interface DeliveryLocation {
  lat: number;
  lng: number;
  timestamp: string;
  speed?: number;
  heading?: number;
  accuracy?: number;
}

interface DeliveryTrackingData {
  orderId: string;
  driverId: string;
  status: string;
  currentLocation: DeliveryLocation | null;
  estimatedArrival: string | null;
  routeProgress: number; // 0-100%
  locationHistory: DeliveryLocation[];
  trackingNumber: string | null;
  driverName?: string; // Add driver name to tracking data
  driverPhone?: string; // Add driver phone to tracking data
}

interface PickupAddress {
  street?: string;
  number?: string;
  city?: string;
  state?: string;
  zip?: string;
}

interface DeliveryAddress {
  street?: string;
  number?: string;
  city?: string;
  state?: string;
  zip?: string;
}

interface PackageDetails {
  weight?: string | number;
  dimensions?: string | number;
  description?: string;
}

interface Stop {
  id: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  scheduled_time?: string;
  instructions?: string;
}

interface RouteOptimization {
  mode?: string;
  preferences?: Record<string, unknown>;
}

interface OrderData {
  id: string;
  status: string | null;
  delivery_id: string | null;
  created_at: string;
  updated_at: string;
  payment_status: string;
  total_cost: number;
  payment_method: string;
  scheduled_delivery_time: string | null;
  tracking_number: string | null;
  pickup_address: PickupAddress | string;
  delivery_addresses: (DeliveryAddress | string)[];
  package_details: PackageDetails | null;
  stops: Stop[];
  route_optimization: RouteOptimization;
}

export function useDeliveryTracking(orderId: string) {
  const [trackingData, setTrackingData] = useState<DeliveryTrackingData>({
    orderId,
    driverId: '',
    status: 'pending',
    currentLocation: null,
    estimatedArrival: null,
    routeProgress: 0,
    locationHistory: [],
    trackingNumber: null,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const locationWatchRef = useRef<number | null>(null);

  // Fetch initial tracking data
  useEffect(() => {
    const fetchTrackingData = async () => {
      try {
        setLoading(true);

        // Fetch order details with tracking information
        const { data: order, error: orderError } = await supabase
          .from('orders')
          .select(
            `
            id,
            status,
            delivery_id,
            created_at,
            updated_at,
            payment_status,
            total_cost,
            payment_method,
            scheduled_delivery_time,
            tracking_number,
            pickup_address,
            delivery_addresses,
            package_details,
            stops,
            route_optimization
          `
          )
          .eq('id', orderId)
          .single();

        if (orderError) {
          throw new Error(`Error fetching order: ${orderError.message}`);
        }

        if (!order) {
          throw new Error('Order not found');
        }

        // Fetch driver information
        const { data: driver, error: driverError } = await supabase
          .from('profiles')
          .select('id, full_name, phone')
          .eq('id', order.delivery_id)
          .single();

        if (driverError && driverError.code !== 'PGRST116') {
          throw new Error(`Error fetching driver: ${driverError.message}`);
        }

        // Fetch latest location data
        const { data: locations, error: locationsError } = await supabase
          .from('delivery_locations')
          .select('*')
          .eq('order_id', orderId)
          .order('timestamp', { ascending: false })
          .limit(50);

        if (locationsError) {
          throw new Error(
            `Error fetching locations: ${locationsError.message}`
          );
        }

        // Calculate route progress
        const routeProgress = calculateRouteProgress(
          order as OrderData,
          locations
        );

        setTrackingData({
          orderId: order.id,
          driverId: order.delivery_id || '',
          status: order.status || 'pending',
          currentLocation:
            locations && locations.length > 0 ? locations[0] : null,
          estimatedArrival: order.scheduled_delivery_time || null,
          routeProgress,
          locationHistory: locations || [],
          trackingNumber: order.tracking_number || null,
          // Add driver information to tracking data
          driverName: driver?.full_name || undefined,
          driverPhone: driver?.phone || undefined,
        });
      } catch (err) {
        console.error('Error in fetchTrackingData:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    if (orderId) {
      fetchTrackingData();
    }
  }, [orderId]);

  // Set up real-time location tracking
  useEffect(() => {
    if (!orderId) return;

    // Subscribe to real-time updates
    const channel = supabase
      .channel(`delivery-tracking-${orderId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'delivery_locations',
          filter: `order_id=eq.${orderId}`,
        },
        payload => {
          const newLocation: DeliveryLocation = payload.new as DeliveryLocation;

          setTrackingData(prev => ({
            ...prev,
            currentLocation: newLocation,
            locationHistory: [newLocation, ...prev.locationHistory].slice(
              0,
              50
            ), // Keep last 50 locations
            routeProgress: calculateRouteProgressFromLocation(
              prev,
              newLocation
            ),
          }));
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'orders',
          filter: `id=eq.${orderId}`,
        },
        payload => {
          const updatedOrder = payload.new as Partial<OrderData>;

          setTrackingData(prev => ({
            ...prev,
            status: updatedOrder.status || prev.status,
            estimatedArrival:
              updatedOrder.scheduled_delivery_time || prev.estimatedArrival,
          }));
        }
      )
      .subscribe();

    // Clean up subscription
    return () => {
      supabase.removeChannel(channel);
    };
  }, [orderId]);

  // Simulate location updates for demonstration
  // In a real implementation, this would come from the driver's device
  useEffect(() => {
    if (!orderId || trackingData.status !== 'in-transit') return;

    const simulateLocationUpdate = () => {
      // This is just for demonstration - in real implementation,
      // location would come from driver's device GPS
      setTrackingData(prev => {
        if (!prev.currentLocation) return prev;

        // Simulate movement
        const newLocation: DeliveryLocation = {
          lat: prev.currentLocation.lat + (Math.random() - 0.5) * 0.001,
          lng: prev.currentLocation.lng + (Math.random() - 0.5) * 0.001,
          timestamp: new Date().toISOString(),
          speed: Math.random() * 20, // 0-20 km/h
          heading: Math.random() * 360, // 0-360 degrees
          accuracy: Math.random() * 10, // 0-10 meters
        };

        return {
          ...prev,
          currentLocation: newLocation,
          locationHistory: [newLocation, ...prev.locationHistory].slice(0, 50),
          routeProgress: Math.min(prev.routeProgress + 0.5, 100),
        };
      });
    };

    // Update location every 5 seconds when in transit
    const interval = setInterval(simulateLocationUpdate, 5000);
    locationWatchRef.current = interval as unknown as number;

    return () => clearInterval(interval);
  }, [orderId, trackingData.status]);

  const updateDriverLocation = async (location: DeliveryLocation) => {
    try {
      // Insert new location record
      const { error } = await supabase.from('delivery_locations').insert({
        order_id: orderId,
        lat: location.lat,
        lng: location.lng,
        timestamp: location.timestamp,
        speed: location.speed,
        heading: location.heading,
        accuracy: location.accuracy,
      });

      if (error) {
        throw new Error(`Failed to update location: ${error.message}`);
      }

      // Update tracking data
      setTrackingData(prev => ({
        ...prev,
        currentLocation: location,
        locationHistory: [location, ...prev.locationHistory].slice(0, 50),
      }));
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to update location'
      );
    }
  };

  return {
    trackingData,
    loading,
    error,
    updateDriverLocation,
  };
}

// Helper function to calculate route progress
function calculateRouteProgress(
  order: OrderData,
  locations: DeliveryLocation[]
): number {
  // In a real implementation, this would calculate actual progress
  // based on the route and current location
  if (!locations || locations.length === 0) return 0;

  // For now, we'll return a simulated progress
  // In a real app, you would compare current location to route waypoints
  return Math.min(100, locations.length * 2); // Simulate progress
}

// Helper function to calculate route progress from new location
function calculateRouteProgressFromLocation(
  prevData: DeliveryTrackingData,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  newLocation: DeliveryLocation
): number {
  // In a real implementation, this would calculate actual progress
  // based on the route and current location
  // For now, we're just simulating progress
  return Math.min(100, prevData.routeProgress + 0.5); // Simulate progress
}
