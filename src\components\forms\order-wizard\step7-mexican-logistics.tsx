'use client';

import React from 'react';
import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { VehicleSelector } from '@/components/forms/vehicle-selection/VehicleSelector';
import { OrderFormData } from '@/types/order-form';

// Define types for vehicle and cargo
interface VehicleType {
  id: string;
  name: string;
  category: string;
  max_weight_kg: number;
  max_volume_m3: number;
  base_rate_per_km: number;
  special_capabilities: string[] | null;
  description: string | null;
  created_at: string | null;
  updated_at: string | null;
}

interface CargoType {
  id: string;
  name: string;
}

// Define API response types
interface VehicleTypeApiResponse {
  success: boolean;
  data: VehicleType[];
  count: number;
}

interface Step8Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
}

export function OrderWizardStep8({ formData, updateFormData }: Step8Props) {
  const [vehicleTypes, setVehicleTypes] = useState<VehicleType[]>([]);
  const [cargoTypes, setCargoTypes] = useState<CargoType[]>([]);

  useEffect(() => {
    const fetchVehicleAndCargoTypes = async () => {
      try {
        // Fetch vehicle types
        const vehicleResponse = await fetch('/api/vehicle-types');
        const vehicleResult: VehicleTypeApiResponse =
          await vehicleResponse.json();

        if (vehicleResult.success) {
          setVehicleTypes(vehicleResult.data);
        }

        // Fetch cargo types (assuming similar API exists)
        // For now, we'll keep the mock data
        setCargoTypes([
          { id: '1', name: 'Frágil' },
          { id: '2', name: 'Perecedera' },
          { id: '3', name: 'Valiosa' },
          { id: '4', name: 'Peligrosa' },
          { id: '5', name: 'Normal' },
        ]);
      } catch (error) {
        console.error('Error fetching vehicle/cargo types:', error);

        // Fallback to mock data
        setVehicleTypes([
          {
            id: '1',
            name: 'Motocicleta',
            category: 'motorcycle',
            max_weight_kg: 150,
            max_volume_m3: 0.3,
            base_rate_per_km: 8.5,
            special_capabilities: null,
            description: null,
            created_at: null,
            updated_at: null,
          },
          {
            id: '2',
            name: 'Automóvil',
            category: 'small',
            max_weight_kg: 500,
            max_volume_m3: 2,
            base_rate_per_km: 12,
            special_capabilities: null,
            description: null,
            created_at: null,
            updated_at: null,
          },
          {
            id: '3',
            name: 'Camioneta',
            category: 'medium',
            max_weight_kg: 1500,
            max_volume_m3: 8,
            base_rate_per_km: 18,
            special_capabilities: null,
            description: null,
            created_at: null,
            updated_at: null,
          },
          {
            id: '4',
            name: 'Camión Pequeño',
            category: 'truck',
            max_weight_kg: 3000,
            max_volume_m3: 15,
            base_rate_per_km: 25,
            special_capabilities: null,
            description: null,
            created_at: null,
            updated_at: null,
          },
          {
            id: '5',
            name: 'Camión Grande',
            category: 'truck',
            max_weight_kg: 10000,
            max_volume_m3: 40,
            base_rate_per_km: 40,
            special_capabilities: null,
            description: null,
            created_at: null,
            updated_at: null,
          },
        ]);

        setCargoTypes([
          { id: '1', name: 'Frágil' },
          { id: '2', name: 'Perecedera' },
          { id: '3', name: 'Valiosa' },
          { id: '4', name: 'Peligrosa' },
          { id: '5', name: 'Normal' },
        ]);
      }
    };

    fetchVehicleAndCargoTypes();
  }, []);

  // Add a useEffect to use vehicleTypes to prevent the unused variable warning
  useEffect(() => {
    // This is just to prevent the unused variable warning
    // In a real application, you might use this for filtering or other logic
    if (vehicleTypes.length > 0) {
      // Do nothing, just to prevent the warning
    }
  }, [vehicleTypes]);

  return (
    <div className='space-y-6'>
      {/* Enhanced Vehicle Selection */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            🚚 Selección de Vehículo
          </CardTitle>
          <CardDescription>
            Selecciona el vehículo más adecuado para tu carga
          </CardDescription>
        </CardHeader>
        <CardContent>
          <VehicleSelector
            products={formData.products || []}
            selectedVehicleId={formData.vehicle_type_id || ''}
            onVehicleSelect={vehicleId =>
              updateFormData({ vehicle_type_id: vehicleId })
            }
          />
        </CardContent>
      </Card>

      {/* Additional Logistics Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            ⚙️ Configuración Adicional
          </CardTitle>
          <CardDescription>
            Detalles específicos de la logística mexicana
          </CardDescription>
        </CardHeader>
        <CardContent className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          {/* Cargo Type */}
          <div className='space-y-2'>
            <Label htmlFor='cargo_type_id'>Clasificación de Carga</Label>
            <select
              id='cargo_type_id'
              value={formData.cargo_type_id || ''}
              onChange={e =>
                updateFormData({
                  cargo_type_id: e.target.value,
                })
              }
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            >
              <option value=''>Seleccionar clasificación</option>
              {cargoTypes.map(cargo => (
                <option key={cargo.id} value={cargo.id}>
                  {cargo.name}
                </option>
              ))}
            </select>
          </div>

          {/* Scheduled Pickup Time */}
          <div className='space-y-2'>
            <Label htmlFor='scheduled_pickup_time'>
              Fecha y Hora de Recolección Programada
            </Label>
            <Input
              id='scheduled_pickup_time'
              type='datetime-local'
              value={formData.scheduled_pickup_time || ''}
              onChange={e =>
                updateFormData({
                  scheduled_pickup_time: e.target.value,
                })
              }
            />
          </div>

          {/* Scheduled Delivery Time */}
          <div className='space-y-2'>
            <Label htmlFor='scheduled_delivery_time'>
              Fecha y Hora de Entrega Programada
            </Label>
            <Input
              id='scheduled_delivery_time'
              type='datetime-local'
              value={formData.scheduled_delivery_time || ''}
              onChange={e =>
                updateFormData({
                  scheduled_delivery_time: e.target.value,
                })
              }
            />
          </div>

          {/* Delivery Instructions */}
          <div className='space-y-2 md:col-span-2'>
            <Label htmlFor='delivery_instructions'>
              Instrucciones de Entrega Especiales
            </Label>
            <Textarea
              id='delivery_instructions'
              value={formData.delivery_instructions || ''}
              onChange={e =>
                updateFormData({
                  delivery_instructions: e.target.value,
                })
              }
              placeholder='Instrucciones especiales para la entrega'
              rows={3}
            />
          </div>

          {/* Special Handling Notes */}
          <div className='space-y-2 md:col-span-2'>
            <Label htmlFor='special_handling_notes'>
              Notas de Manejo Especial
            </Label>
            <Textarea
              id='special_handling_notes'
              value={formData.special_handling_notes || ''}
              onChange={e =>
                updateFormData({
                  special_handling_notes: e.target.value,
                })
              }
              placeholder='Notas sobre el manejo especial de la carga'
              rows={3}
            />
          </div>

          {/* Fiscal Data */}
          <div className='space-y-2'>
            <Label htmlFor='fiscal_rfc'>RFC (Datos Fiscales)</Label>
            <Input
              id='fiscal_rfc'
              value={formData.fiscal_data?.rfc || ''}
              onChange={e =>
                updateFormData({
                  fiscal_data: {
                    ...formData.fiscal_data,
                    rfc: e.target.value,
                  },
                })
              }
              placeholder='RFC'
            />
          </div>

          <div className='space-y-2'>
            <Label htmlFor='fiscal_business_name'>Razón Social</Label>
            <Input
              id='fiscal_business_name'
              value={formData.fiscal_data?.business_name || ''}
              onChange={e =>
                updateFormData({
                  fiscal_data: {
                    ...formData.fiscal_data,
                    business_name: e.target.value,
                  },
                })
              }
              placeholder='Razón Social'
            />
          </div>

          <div className='space-y-2'>
            <Label htmlFor='fiscal_tax_regime'>Régimen Fiscal</Label>
            <Input
              id='fiscal_tax_regime'
              value={formData.fiscal_data?.tax_regime || ''}
              onChange={e =>
                updateFormData({
                  fiscal_data: {
                    ...formData.fiscal_data,
                    tax_regime: e.target.value,
                  },
                })
              }
              placeholder='Régimen Fiscal'
            />
          </div>

          {/* Route Optimization */}
          <div className='space-y-2'>
            <Label htmlFor='route_optimization'>Optimización de Ruta</Label>
            <select
              id='route_optimization'
              value={formData.route_optimization || 'balanced'}
              onChange={e =>
                updateFormData({
                  route_optimization: e.target.value as
                    | 'balanced'
                    | 'fastest'
                    | 'shortest'
                    | 'eco',
                })
              }
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            >
              <option value='balanced'>Balanceado</option>
              <option value='fastest'>Más Rápido</option>
              <option value='shortest'>Más Corto</option>
              <option value='eco'>Ecológico</option>
            </select>
          </div>

          {/* Delivery Region */}
          <div className='space-y-2'>
            <Label htmlFor='delivery_region'>Región de Entrega</Label>
            <select
              id='delivery_region'
              value={formData.delivery_region || 'local'}
              onChange={e =>
                updateFormData({
                  delivery_region: e.target.value as
                    | 'local'
                    | 'regional'
                    | 'national'
                    | 'international',
                })
              }
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            >
              <option value='local'>Local</option>
              <option value='regional'>Regional</option>
              <option value='national'>Nacional</option>
              <option value='international'>Internacional</option>
            </select>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
