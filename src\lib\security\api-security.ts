/**
 * API Security Wrapper
 *
 * Provides comprehensive security for API routes with Zero Trust principles
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  zeroTrustMiddleware,
  type SecurityContext,
  type ZeroTrustConfig,
} from './zero-trust';
import { getAPISecurityHeaders } from './security-headers';
import { auditLog } from './audit-logger';
import { endpointRateLimit } from './rate-limiter';

export interface APISecurityConfig extends ZeroTrustConfig {
  requireAuth?: boolean;
  allowedRoles?: ('admin' | 'customer' | 'delivery')[];
  requireMFA?: boolean;
  validateCSRF?: boolean;
  customValidation?: (
    context: SecurityContext
  ) => Promise<{ valid: boolean; error?: string }>;
}

export interface SecureAPIHandler {
  GET?: (
    request: NextRequest,
    context: SecurityContext
  ) => Promise<NextResponse>;
  POST?: (
    request: NextRequest,
    context: SecurityContext
  ) => Promise<NextResponse>;
  PUT?: (
    request: NextRequest,
    context: SecurityContext
  ) => Promise<NextResponse>;
  PATCH?: (
    request: NextRequest,
    context: SecurityContext
  ) => Promise<NextResponse>;
  DELETE?: (
    request: NextRequest,
    context: SecurityContext
  ) => Promise<NextResponse>;
}

/**
 * Secure API route wrapper with Zero Trust security
 */
export function secureAPI(
  handlers: SecureAPIHandler,
  config: APISecurityConfig = {}
) {
  return async function secureHandler(request: NextRequest) {
    const startTime = Date.now();

    try {
      // Apply Zero Trust middleware
      const { context, response: middlewareResponse } =
        await zeroTrustMiddleware(request, {
          requireMFA: config.requireMFA,
          maxRiskScore: config.maxRiskScore || 80,
          rateLimitRpm: config.rateLimitRpm,
          privilegedPaths: config.privilegedPaths,
          allowedOrigins: config.allowedOrigins,
          requireCSRF: config.validateCSRF,
        });

      // If middleware returned a response (error), return it
      if (middlewareResponse) {
        return applySecurityHeaders(middlewareResponse);
      }

      // Check authentication requirement
      if (config.requireAuth && !context.user) {
        await auditLog({
          event: 'UNAUTHORIZED_API_ACCESS',
          ip: context.request.ip,
          path: context.request.path,
          method: context.request.method,
          severity: 'HIGH',
          category: 'ACCESS',
        });

        return applySecurityHeaders(
          NextResponse.json(
            { error: 'Authentication required' },
            { status: 401 }
          )
        );
      }

      // Check role-based access
      if (config.allowedRoles && context.user) {
        if (!config.allowedRoles.includes(context.user.role)) {
          await auditLog({
            event: 'INSUFFICIENT_PRIVILEGES',
            userId: context.user.id,
            ip: context.request.ip,
            path: context.request.path,
            method: context.request.method,
            details: {
              userRole: context.user.role,
              requiredRoles: config.allowedRoles,
            },
            severity: 'HIGH',
            category: 'ACCESS',
          });

          return applySecurityHeaders(
            NextResponse.json(
              { error: 'Insufficient privileges' },
              { status: 403 }
            )
          );
        }
      }

      // Apply endpoint-specific rate limiting
      const rateLimitResult = await endpointRateLimit(
        context.request.ip,
        context.request.path,
        context.user?.role
      );

      if (!rateLimitResult.allowed) {
        await auditLog({
          event: 'ENDPOINT_RATE_LIMIT_EXCEEDED',
          userId: context.user?.id,
          ip: context.request.ip,
          path: context.request.path,
          details: {
            limit: rateLimitResult.totalRequests,
            resetTime: rateLimitResult.resetTime,
          },
          severity: 'MEDIUM',
          category: 'ACCESS',
        });

        return applySecurityHeaders(
          NextResponse.json(
            {
              error: 'Rate limit exceeded for this endpoint',
              retryAfter: Math.ceil(
                (rateLimitResult.resetTime - Date.now()) / 1000
              ),
            },
            {
              status: 429,
              headers: {
                'Retry-After': Math.ceil(
                  (rateLimitResult.resetTime - Date.now()) / 1000
                ).toString(),
              },
            }
          )
        );
      }

      // Custom validation
      if (config.customValidation) {
        const validationResult = await config.customValidation(context);
        if (!validationResult.valid) {
          await auditLog({
            event: 'CUSTOM_VALIDATION_FAILED',
            userId: context.user?.id,
            ip: context.request.ip,
            path: context.request.path,
            details: { error: validationResult.error },
            severity: 'MEDIUM',
            category: 'ACCESS',
          });

          return applySecurityHeaders(
            NextResponse.json(
              { error: validationResult.error || 'Validation failed' },
              { status: 400 }
            )
          );
        }
      }

      // Get the appropriate handler for the HTTP method
      const handler = handlers[request.method as keyof SecureAPIHandler];

      if (!handler) {
        return applySecurityHeaders(
          NextResponse.json(
            { error: 'Method not allowed' },
            {
              status: 405,
              headers: { Allow: Object.keys(handlers).join(', ') },
            }
          )
        );
      }

      // Execute the handler
      const response = await handler(request, context);

      // Log successful API call
      await auditLog({
        event: 'API_REQUEST_SUCCESS',
        userId: context.user?.id,
        ip: context.request.ip,
        path: context.request.path,
        method: context.request.method,
        details: {
          processingTime: Date.now() - startTime,
          riskScore: context.security.riskScore,
          rateLimitRemaining: rateLimitResult.remaining,
        },
        severity: 'LOW',
        category: 'ACCESS',
      });

      return applySecurityHeaders(response);
    } catch (error) {
      console.error('Secure API handler error:', error);

      await auditLog({
        event: 'API_HANDLER_ERROR',
        ip: getClientIP(request),
        path: request.nextUrl.pathname,
        method: request.method,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          processingTime: Date.now() - startTime,
        },
        severity: 'HIGH',
        category: 'SYSTEM',
      });

      return applySecurityHeaders(
        NextResponse.json({ error: 'Internal server error' }, { status: 500 })
      );
    }
  };
}

/**
 * Apply security headers to API response
 */
function applySecurityHeaders(response: NextResponse): NextResponse {
  const securityHeaders = getAPISecurityHeaders();

  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  return response;
}

/**
 * Get client IP address
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  return 'unknown';
}

/**
 * Validate request signature (for high-security operations)
 */
export async function validateRequestSignature(
  request: NextRequest,
  secret: string
): Promise<boolean> {
  const signature = request.headers.get('x-signature');
  if (!signature) return false;

  try {
    const body = await request.clone().text();
    const expectedSignature = await generateSignature(body, secret);
    return signature === expectedSignature;
  } catch (error) {
    console.error('Signature validation error:', error);
    return false;
  }
}

/**
 * Generate request signature
 */
async function generateSignature(
  payload: string,
  secret: string
): Promise<string> {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign(
    'HMAC',
    key,
    encoder.encode(payload)
  );
  return Array.from(new Uint8Array(signature))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * Input sanitization for API requests
 */
export function sanitizeInput(input: unknown): unknown {
  if (typeof input === 'string') {
    // Remove potentially dangerous characters
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }

  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }

  if (typeof input === 'object' && input !== null) {
    const sanitized: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(input)) {
      // Skip potentially dangerous keys
      if (!['__proto__', 'constructor', 'prototype'].includes(key)) {
        sanitized[key] = sanitizeInput(value);
      }
    }
    return sanitized;
  }

  return input;
}

/**
 * Validate UUID format
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
}

/**
 * Validate phone number format
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  return phoneRegex.test(phone);
}
