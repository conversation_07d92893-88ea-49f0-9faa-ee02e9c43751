/**
 * Security Audit Logger
 *
 * Comprehensive logging system for security events and compliance
 */

export interface AuditEvent {
  event: string;
  userId?: string;
  ip?: string;
  userAgent?: string;
  path?: string;
  method?: string;
  timestamp?: Date;
  details?: Record<string, unknown>;
  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  category?: 'AUTH' | 'ACCESS' | 'DATA' | 'SYSTEM' | 'SECURITY';
}

export interface AuditLogEntry extends AuditEvent {
  id: string;
  timestamp: Date;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  category: 'AUTH' | 'ACCESS' | 'DATA' | 'SYSTEM' | 'SECURITY';
}

// In-memory storage for development (use database/external service in production)
const auditLogs: AuditLogEntry[] = [];
const MAX_LOGS = 10000; // Prevent memory overflow

/**
 * Log security audit event
 */
export async function auditLog(event: AuditEvent): Promise<void> {
  try {
    const logEntry: AuditLogEntry = {
      id: generateId(),
      timestamp: event.timestamp || new Date(),
      severity: event.severity || determineSeverity(event.event),
      category: event.category || determineCategory(event.event),
      ...event,
    };

    // Add to in-memory storage
    auditLogs.push(logEntry);

    // Maintain max log size
    if (auditLogs.length > MAX_LOGS) {
      auditLogs.splice(0, auditLogs.length - MAX_LOGS);
    }

    // Console logging for development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[AUDIT] ${logEntry.severity} - ${logEntry.event}`, {
        userId: logEntry.userId,
        ip: logEntry.ip,
        path: logEntry.path,
        details: logEntry.details,
      });
    }

    // Send to external logging service in production
    if (process.env.NODE_ENV === 'production') {
      await sendToExternalLogger(logEntry);
    }

    // Alert on critical events
    if (logEntry.severity === 'CRITICAL') {
      await sendSecurityAlert(logEntry);
    }
  } catch (error) {
    console.error('Failed to log audit event:', error);
    // Don't throw - logging failures shouldn't break the application
  }
}

/**
 * Get audit logs with filtering
 */
export async function getAuditLogs(filters?: {
  userId?: string;
  ip?: string;
  event?: string;
  severity?: string;
  category?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
}): Promise<AuditLogEntry[]> {
  let filteredLogs = [...auditLogs];

  if (filters) {
    if (filters.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
    }

    if (filters.ip) {
      filteredLogs = filteredLogs.filter(log => log.ip === filters.ip);
    }

    if (filters.event) {
      filteredLogs = filteredLogs.filter(log =>
        log.event.toLowerCase().includes(filters.event!.toLowerCase())
      );
    }

    if (filters.severity) {
      filteredLogs = filteredLogs.filter(
        log => log.severity === filters.severity
      );
    }

    if (filters.category) {
      filteredLogs = filteredLogs.filter(
        log => log.category === filters.category
      );
    }

    if (filters.startDate) {
      filteredLogs = filteredLogs.filter(
        log => log.timestamp >= filters.startDate!
      );
    }

    if (filters.endDate) {
      filteredLogs = filteredLogs.filter(
        log => log.timestamp <= filters.endDate!
      );
    }
  }

  // Sort by timestamp (newest first)
  filteredLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

  // Apply limit
  if (filters?.limit) {
    filteredLogs = filteredLogs.slice(0, filters.limit);
  }

  return filteredLogs;
}

/**
 * Get security metrics
 */
export async function getSecurityMetrics(
  timeframe: 'hour' | 'day' | 'week' | 'month' = 'day'
) {
  const now = new Date();
  let startTime: Date;

  switch (timeframe) {
    case 'hour':
      startTime = new Date(now.getTime() - 60 * 60 * 1000);
      break;
    case 'day':
      startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case 'week':
      startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case 'month':
      startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
  }

  const recentLogs = auditLogs.filter(log => log.timestamp >= startTime);

  const metrics = {
    totalEvents: recentLogs.length,
    criticalEvents: recentLogs.filter(log => log.severity === 'CRITICAL')
      .length,
    highSeverityEvents: recentLogs.filter(log => log.severity === 'HIGH')
      .length,
    authEvents: recentLogs.filter(log => log.category === 'AUTH').length,
    securityEvents: recentLogs.filter(log => log.category === 'SECURITY')
      .length,
    failedLogins: recentLogs.filter(log => log.event === 'LOGIN_FAILED').length,
    rateLimitExceeded: recentLogs.filter(
      log => log.event === 'RATE_LIMIT_EXCEEDED'
    ).length,
    suspiciousActivity: recentLogs.filter(log =>
      ['SUSPICIOUS_REQUEST', 'ATTACK_DETECTED', 'INVALID_REQUEST'].includes(
        log.event
      )
    ).length,
    uniqueIPs: new Set(recentLogs.map(log => log.ip).filter(Boolean)).size,
    uniqueUsers: new Set(recentLogs.map(log => log.userId).filter(Boolean))
      .size,
    eventsByCategory: {
      AUTH: recentLogs.filter(log => log.category === 'AUTH').length,
      ACCESS: recentLogs.filter(log => log.category === 'ACCESS').length,
      DATA: recentLogs.filter(log => log.category === 'DATA').length,
      SYSTEM: recentLogs.filter(log => log.category === 'SYSTEM').length,
      SECURITY: recentLogs.filter(log => log.category === 'SECURITY').length,
    },
    eventsBySeverity: {
      LOW: recentLogs.filter(log => log.severity === 'LOW').length,
      MEDIUM: recentLogs.filter(log => log.severity === 'MEDIUM').length,
      HIGH: recentLogs.filter(log => log.severity === 'HIGH').length,
      CRITICAL: recentLogs.filter(log => log.severity === 'CRITICAL').length,
    },
  };

  return metrics;
}

/**
 * Determine event severity based on event type
 */
function determineSeverity(
  event: string
): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
  const severityMap: Record<string, 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'> = {
    // Critical events
    ADMIN_ACCESS_GRANTED: 'CRITICAL',
    PRIVILEGE_ESCALATION: 'CRITICAL',
    DATA_BREACH_DETECTED: 'CRITICAL',
    SYSTEM_COMPROMISE: 'CRITICAL',
    MULTIPLE_FAILED_LOGINS: 'CRITICAL',

    // High severity events
    LOGIN_FAILED: 'HIGH',
    UNAUTHORIZED_ACCESS: 'HIGH',
    RATE_LIMIT_EXCEEDED: 'HIGH',
    SUSPICIOUS_REQUEST: 'HIGH',
    ATTACK_DETECTED: 'HIGH',
    MFA_BYPASS_ATTEMPT: 'HIGH',
    SESSION_HIJACK_DETECTED: 'HIGH',

    // Medium severity events
    LOGIN_SUCCESS: 'MEDIUM',
    PASSWORD_CHANGED: 'MEDIUM',
    PROFILE_UPDATED: 'MEDIUM',
    ORDER_STATUS_CHANGED: 'MEDIUM',
    INVALID_REQUEST: 'MEDIUM',
    MFA_REQUIRED: 'MEDIUM',

    // Low severity events
    REQUEST_AUTHORIZED: 'LOW',
    SESSION_CREATED: 'LOW',
    SESSION_REFRESHED: 'LOW',
    USER_LOGOUT: 'LOW',
    DATA_ACCESSED: 'LOW',
  };

  return severityMap[event] || 'MEDIUM';
}

/**
 * Determine event category based on event type
 */
function determineCategory(
  event: string
): 'AUTH' | 'ACCESS' | 'DATA' | 'SYSTEM' | 'SECURITY' {
  const categoryMap: Record<
    string,
    'AUTH' | 'ACCESS' | 'DATA' | 'SYSTEM' | 'SECURITY'
  > = {
    // Authentication events
    LOGIN_SUCCESS: 'AUTH',
    LOGIN_FAILED: 'AUTH',
    LOGOUT: 'AUTH',
    PASSWORD_CHANGED: 'AUTH',
    MFA_ENABLED: 'AUTH',
    MFA_DISABLED: 'AUTH',
    MFA_REQUIRED: 'AUTH',
    SESSION_CREATED: 'AUTH',
    SESSION_EXPIRED: 'AUTH',

    // Access control events
    UNAUTHORIZED_ACCESS: 'ACCESS',
    PRIVILEGE_ESCALATION: 'ACCESS',
    ADMIN_ACCESS_GRANTED: 'ACCESS',
    REQUEST_AUTHORIZED: 'ACCESS',
    RATE_LIMIT_EXCEEDED: 'ACCESS',

    // Data events
    DATA_ACCESSED: 'DATA',
    DATA_MODIFIED: 'DATA',
    DATA_DELETED: 'DATA',
    ORDER_CREATED: 'DATA',
    ORDER_STATUS_CHANGED: 'DATA',
    PROFILE_UPDATED: 'DATA',

    // System events
    SYSTEM_ERROR: 'SYSTEM',
    SECURITY_MIDDLEWARE_ERROR: 'SYSTEM',
    DATABASE_ERROR: 'SYSTEM',
    API_ERROR: 'SYSTEM',

    // Security events
    ATTACK_DETECTED: 'SECURITY',
    SUSPICIOUS_REQUEST: 'SECURITY',
    INVALID_REQUEST: 'SECURITY',
    CSRF_VALIDATION_FAILED: 'SECURITY',
    SESSION_HIJACK_DETECTED: 'SECURITY',
    DATA_BREACH_DETECTED: 'SECURITY',
  };

  return categoryMap[event] || 'SYSTEM';
}

/**
 * Generate unique ID for log entries
 */
function generateId(): string {
  return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Send log to external logging service (placeholder)
 */
async function sendToExternalLogger(logEntry: AuditLogEntry): Promise<void> {
  // TODO: Implement integration with external logging services
  // Examples: Datadog, Splunk, ELK Stack, CloudWatch, etc.

  if (process.env.AUDIT_WEBHOOK_URL) {
    try {
      await fetch(process.env.AUDIT_WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.AUDIT_WEBHOOK_TOKEN}`,
        },
        body: JSON.stringify(logEntry),
      });
    } catch (error) {
      console.error('Failed to send audit log to external service:', error);
    }
  }
}

/**
 * Send security alert for critical events
 */
async function sendSecurityAlert(logEntry: AuditLogEntry): Promise<void> {
  // TODO: Implement security alerting
  // Examples: Email, Slack, PagerDuty, SMS, etc.

  console.error(`🚨 CRITICAL SECURITY EVENT: ${logEntry.event}`, {
    userId: logEntry.userId,
    ip: logEntry.ip,
    path: logEntry.path,
    details: logEntry.details,
    timestamp: logEntry.timestamp,
  });

  if (process.env.SECURITY_ALERT_WEBHOOK) {
    try {
      await fetch(process.env.SECURITY_ALERT_WEBHOOK, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: `🚨 Critical Security Event: ${logEntry.event}`,
          attachments: [
            {
              color: 'danger',
              fields: [
                { title: 'Event', value: logEntry.event, short: true },
                { title: 'Severity', value: logEntry.severity, short: true },
                {
                  title: 'User ID',
                  value: logEntry.userId || 'N/A',
                  short: true,
                },
                {
                  title: 'IP Address',
                  value: logEntry.ip || 'N/A',
                  short: true,
                },
                { title: 'Path', value: logEntry.path || 'N/A', short: true },
                {
                  title: 'Timestamp',
                  value: logEntry.timestamp.toISOString(),
                  short: true,
                },
              ],
            },
          ],
        }),
      });
    } catch (error) {
      console.error('Failed to send security alert:', error);
    }
  }
}
