/**
 * Security Cleanup Script: Fix Role Violations
 *
 * This script identifies and fixes users who have incorrect roles assigned,
 * specifically focusing on admin and customer users who may have been
 * incorrectly assigned delivery roles.
 *
 * CRITICAL SECURITY FIX for the delivery system authentication vulnerability.
 */

import { createClient } from '@supabase/supabase-js';

// Simple dotenv replacement for this script
function loadEnvVars() {
  // In a real implementation, you would load from .env file
  // For now, we'll rely on process.env being set externally
}

// Load environment variables
loadEnvVars();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

// Create Supabase client with service role key for admin operations
const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface Profile {
  id: string;
  email: string;
  full_name: string | null;
  phone: string | null;
  role: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

interface RoleViolation {
  userId: string;
  email: string;
  currentRole: string;
  suggestedRole: string;
  reason: string;
  severity: 'HIGH' | 'MEDIUM' | 'LOW';
}

async function auditLog(
  event: string,
  details: any,
  severity: string = 'INFO'
) {
  try {
    await supabase.from('audit_logs').insert({
      event,
      details,
      severity,
      category: 'SECURITY_CLEANUP',
      created_at: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to log audit event:', error);
  }
}

async function identifyRoleViolations(): Promise<RoleViolation[]> {
  console.log('🔍 Identifying role violations...');

  const violations: RoleViolation[] = [];

  try {
    // Get all profiles
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    if (!profiles || profiles.length === 0) {
      console.log('No profiles found');
      return violations;
    }

    console.log(`📊 Analyzing ${profiles.length} profiles...`);

    for (const profile of profiles as Profile[]) {
      // Check for suspicious patterns that indicate role violations

      // 1. Users with admin-like email patterns who have delivery role
      if (profile.role === 'delivery' && isAdminEmail(profile.email)) {
        violations.push({
          userId: profile.id,
          email: profile.email,
          currentRole: profile.role,
          suggestedRole: 'admin',
          reason: 'Admin email pattern with delivery role',
          severity: 'HIGH',
        });
      }

      // 2. Users created recently with delivery role (potential unauthorized registrations)
      const createdDate = new Date(profile.created_at);
      const daysSinceCreation =
        (Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24);

      if (
        profile.role === 'delivery' &&
        daysSinceCreation < 7 &&
        !profile.full_name
      ) {
        violations.push({
          userId: profile.id,
          email: profile.email,
          currentRole: profile.role,
          suggestedRole: 'customer',
          reason: 'Recent delivery registration without profile completion',
          severity: 'MEDIUM',
        });
      }

      // 3. Users with multiple role indicators (check user metadata if available)
      // This would require checking auth.users table metadata
    }

    console.log(`⚠️  Found ${violations.length} potential role violations`);
    return violations;
  } catch (error) {
    console.error('Error identifying role violations:', error);
    throw error;
  }
}

function isAdminEmail(email: string): boolean {
  const adminPatterns = [
    /admin@/i,
    /administrator@/i,
    /root@/i,
    /superuser@/i,
    /manager@/i,
    /owner@/i,
    /capitalcodecolmx@gmail\.com/i, // Specific admin email from the system
  ];

  return adminPatterns.some(pattern => pattern.test(email));
}

async function fixRoleViolation(
  violation: RoleViolation,
  dryRun: boolean = true
): Promise<boolean> {
  console.log(
    `${dryRun ? '🔍 [DRY RUN]' : '🔧'} Fixing violation for ${violation.email}`
  );
  console.log(`  Current role: ${violation.currentRole}`);
  console.log(`  Suggested role: ${violation.suggestedRole}`);
  console.log(`  Reason: ${violation.reason}`);

  if (dryRun) {
    console.log('  ✅ Would fix this violation (dry run mode)');
    return true;
  }

  try {
    // Update the user's role
    const { error } = await supabase
      .from('profiles')
      .update({
        role: violation.suggestedRole,
        updated_at: new Date().toISOString(),
      })
      .eq('id', violation.userId);

    if (error) {
      console.error(`  ❌ Failed to fix violation: ${error.message}`);
      return false;
    }

    // Log the fix
    await auditLog(
      'ROLE_VIOLATION_FIXED',
      {
        userId: violation.userId,
        email: violation.email,
        previousRole: violation.currentRole,
        newRole: violation.suggestedRole,
        reason: violation.reason,
        severity: violation.severity,
      },
      'HIGH'
    );

    console.log(`  ✅ Fixed violation for ${violation.email}`);
    return true;
  } catch (error) {
    console.error(`  ❌ Error fixing violation: ${error}`);
    return false;
  }
}

async function generateReport(violations: RoleViolation[]) {
  console.log('\n📋 SECURITY VIOLATION REPORT');
  console.log('='.repeat(50));

  const highSeverity = violations.filter(v => v.severity === 'HIGH');
  const mediumSeverity = violations.filter(v => v.severity === 'MEDIUM');
  const lowSeverity = violations.filter(v => v.severity === 'LOW');

  console.log(`🔴 HIGH severity violations: ${highSeverity.length}`);
  console.log(`🟡 MEDIUM severity violations: ${mediumSeverity.length}`);
  console.log(`🟢 LOW severity violations: ${lowSeverity.length}`);
  console.log(`📊 Total violations: ${violations.length}`);

  if (violations.length > 0) {
    console.log('\nDetailed violations:');
    violations.forEach((violation, index) => {
      console.log(`\n${index + 1}. ${violation.email}`);
      console.log(`   Current Role: ${violation.currentRole}`);
      console.log(`   Suggested Role: ${violation.suggestedRole}`);
      console.log(`   Severity: ${violation.severity}`);
      console.log(`   Reason: ${violation.reason}`);
    });
  }
}

async function main() {
  console.log('🚨 SECURITY CLEANUP: Role Violation Detection and Fix');
  console.log('='.repeat(60));

  const args = process.argv.slice(2);
  const dryRun = !args.includes('--fix');

  if (dryRun) {
    console.log('🔍 Running in DRY RUN mode. Use --fix to apply changes.');
  } else {
    console.log('⚠️  LIVE MODE: Changes will be applied to the database!');
  }

  try {
    // Step 1: Identify violations
    const violations = await identifyRoleViolations();

    // Step 2: Generate report
    await generateReport(violations);

    // Step 3: Fix violations if requested
    if (violations.length > 0) {
      console.log(`\n🔧 ${dryRun ? 'Simulating fixes' : 'Applying fixes'}...`);

      let fixedCount = 0;
      for (const violation of violations) {
        const success = await fixRoleViolation(violation, dryRun);
        if (success) fixedCount++;
      }

      console.log(
        `\n✅ ${dryRun ? 'Would fix' : 'Fixed'} ${fixedCount}/${violations.length} violations`
      );
    } else {
      console.log('\n🎉 No role violations found! System is secure.');
    }

    // Log the cleanup operation
    await auditLog(
      'SECURITY_CLEANUP_COMPLETED',
      {
        violationsFound: violations.length,
        violationsFixed: dryRun ? 0 : violations.length,
        dryRun,
        timestamp: new Date().toISOString(),
      },
      'HIGH'
    );
  } catch (error) {
    console.error('❌ Cleanup failed:', error);

    await auditLog(
      'SECURITY_CLEANUP_FAILED',
      {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      'HIGH'
    );

    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

export { identifyRoleViolations, fixRoleViolation, generateReport };
