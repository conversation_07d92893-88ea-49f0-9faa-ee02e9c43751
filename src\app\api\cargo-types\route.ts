import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { secureAPI, sanitizeInput } from '@/lib/security/api-security';
import { auditLog } from '@/lib/security/audit-logger';

// GET /api/cargo-types - Get all cargo types
export const GET = secureAPI(
  {
    GET: async (request, context) => {
      try {
        const supabase = await createClient();

        // Get all cargo types
        const { data, error } = await supabase
          .from('cargo_types')
          .select('*')
          .order('category', { ascending: true })
          .order('name', { ascending: true });

        if (error) {
          console.error('Database error:', error);
          await auditLog({
            event: 'CARGO_TYPES_FETCH_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { error: error.message },
            severity: 'MEDIUM',
            category: 'SYSTEM',
          });
          return NextResponse.json(
            { error: 'Error al obtener los tipos de carga' },
            { status: 500 }
          );
        }

        await auditLog({
          event: 'CARGO_TYPES_FETCHED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { count: data?.length || 0 },
          severity: 'LOW',
          category: 'DATA',
        });

        return NextResponse.json({
          success: true,
          data,
          count: data?.length || 0,
        });
      } catch (error) {
        await auditLog({
          event: 'CARGO_TYPES_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: false,
    rateLimitRpm: 60,
  }
);

// POST /api/cargo-types - Create a new cargo type (admin only)
export const POST = secureAPI(
  {
    POST: async (request, context) => {
      // Check if user is admin
      if (context.user?.role !== 'admin') {
        await auditLog({
          event: 'UNAUTHORIZED_CARGO_TYPE_CREATE_ATTEMPT',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { userRole: context.user?.role },
          severity: 'HIGH',
          category: 'SECURITY',
        });

        return NextResponse.json(
          { error: 'Solo los administradores pueden crear tipos de carga' },
          { status: 403 }
        );
      }

      try {
        // Parse and sanitize request body
        let requestBody;
        try {
          requestBody = sanitizeInput(await request.json());
        } catch {
          return NextResponse.json(
            { error: 'Formato de cuerpo de solicitud inválido' },
            { status: 400 }
          );
        }

        const {
          name,
          category,
          special_requirements,
          rate_multiplier,
          description,
        } = requestBody as {
          name?: string;
          category?: string;
          special_requirements?: Record<string, unknown>;
          rate_multiplier?: number;
          description?: string;
        };

        // Validate required fields
        if (!name || !category || !rate_multiplier) {
          return NextResponse.json(
            {
              error:
                'Los campos nombre, categoría y multiplicador de tarifa son requeridos',
            },
            { status: 400 }
          );
        }

        const supabase = await createClient();

        // Insert new cargo type
        const { data, error } = await supabase
          .from('cargo_types')
          .insert({
            name,
            category,
            special_requirements: special_requirements || {},
            rate_multiplier,
            description: description || null,
          })
          .select()
          .single();

        if (error) {
          console.error('Database error:', error);
          await auditLog({
            event: 'CARGO_TYPE_CREATE_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { error: error.message, requestData: requestBody },
            severity: 'MEDIUM',
            category: 'DATA',
          });
          return NextResponse.json(
            { error: 'Error al crear el tipo de carga' },
            { status: 500 }
          );
        }

        await auditLog({
          event: 'CARGO_TYPE_CREATED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { cargoTypeId: data.id, name: data.name },
          severity: 'MEDIUM',
          category: 'DATA',
        });

        return NextResponse.json(
          {
            success: true,
            message: 'Tipo de carga creado exitosamente',
            data,
          },
          { status: 201 }
        );
      } catch (error) {
        await auditLog({
          event: 'CARGO_TYPE_CREATE_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: true,
    allowedRoles: ['admin'],
    requireMFA: false,
    validateCSRF: true,
    rateLimitRpm: 30,
  }
);
