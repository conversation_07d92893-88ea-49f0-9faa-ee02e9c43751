/**
 * PKCE Authentication Helper
 *
 * Implements Supabase's recommended PKCE (Proof Key for Code Exchange) flow
 * for enhanced security in OAuth and authentication flows.
 *
 * Reference: https://supabase.com/docs/guides/auth/server-side/oauth-pkce
 */

import { createBrowserClient } from '@supabase/ssr';
import type {
  SupabaseClient,
  Session,
  AuthChangeEvent,
} from '@supabase/supabase-js';
import type { Database } from '@/lib/supabase';

// Type definitions for better type safety
interface UserMetadata {
  full_name?: string;
  role?: 'customer' | 'delivery' | 'admin';
  [key: string]: string | number | boolean | null | undefined;
}

/**
 * PKCE Authentication Manager
 * Handles secure authentication flows with PKCE
 * Note: This class is designed for browser-only usage
 */
export class PKCEAuth {
  private supabase: SupabaseClient<Database>;

  constructor() {
    if (typeof window === 'undefined') {
      throw new Error(
        'PKCEAuth can only be used in browser environment. Use server-side auth helpers for SSR.'
      );
    }

    this.supabase = createBrowserClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
  }

  /**
   * Initiate PKCE OAuth flow
   * @param provider OAuth provider (google, github, etc.)
   * @param redirectTo Optional redirect URL after authentication
   */
  async signInWithOAuth(
    provider:
      | 'google'
      | 'github'
      | 'apple'
      | 'azure'
      | 'bitbucket'
      | 'discord'
      | 'facebook'
      | 'figma'
      | 'gitlab'
      | 'kakao'
      | 'keycloak'
      | 'linkedin'
      | 'notion'
      | 'slack'
      | 'spotify'
      | 'twitch'
      | 'twitter'
      | 'workos'
      | 'zoom',
    redirectTo?: string
  ) {
    const { data, error } = await this.supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: redirectTo || `${window.location.origin}/auth/callback`,
        // PKCE is enabled by default in Supabase Auth
        // The library automatically handles code_challenge and code_verifier
      },
    });

    if (error) {
      console.error('OAuth sign-in error:', error);
      throw error;
    }

    return data;
  }

  /**
   * Sign in with email and password using PKCE
   * @param email User email
   * @param password User password
   */
  async signInWithPassword(email: string, password: string) {
    const { data, error } = await this.supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error('Password sign-in error:', error);
      throw error;
    }

    return data;
  }

  /**
   * Sign up with email and password
   * @param email User email
   * @param password User password
   * @param metadata Additional user metadata
   */
  async signUp(email: string, password: string, metadata?: UserMetadata) {
    const { data, error } = await this.supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    });

    if (error) {
      console.error('Sign-up error:', error);
      throw error;
    }

    return data;
  }

  /**
   * Handle OAuth callback (PKCE code exchange)
   * This should be called on the callback page
   */
  async handleOAuthCallback() {
    const { data, error } = await this.supabase.auth.getSession();

    if (error) {
      console.error('OAuth callback error:', error);
      throw error;
    }

    return data;
  }

  /**
   * Refresh session using refresh token
   * Implements proper token rotation as recommended by Supabase
   */
  async refreshSession() {
    const { data, error } = await this.supabase.auth.refreshSession();

    if (error) {
      console.error('Session refresh error:', error);
      throw error;
    }

    return data;
  }

  /**
   * Sign out user and clear session
   */
  async signOut() {
    const { error } = await this.supabase.auth.signOut();

    if (error) {
      console.error('Sign-out error:', error);
      throw error;
    }
  }

  /**
   * Get current session with automatic refresh
   */
  async getSession() {
    const { data, error } = await this.supabase.auth.getSession();

    if (error) {
      console.error('Get session error:', error);
      throw error;
    }

    // If session is expired, try to refresh
    if (data.session && this.isSessionExpired(data.session)) {
      return await this.refreshSession();
    }

    return data;
  }

  /**
   * Get current user with JWT validation
   */
  async getUser() {
    const { data, error } = await this.supabase.auth.getUser();

    if (error) {
      console.error('Get user error:', error);
      throw error;
    }

    return data;
  }

  /**
   * Listen to auth state changes
   * @param callback Function to call when auth state changes
   */
  onAuthStateChange(
    callback: (event: AuthChangeEvent, session: Session | null) => void
  ) {
    return this.supabase.auth.onAuthStateChange(callback);
  }

  /**
   * Check if session is expired
   * @param session Session object
   */
  private isSessionExpired(session: Session): boolean {
    if (!session.expires_at) return false;

    const expiresAt = new Date(session.expires_at * 1000);
    const now = new Date();
    const bufferTime = 5 * 60 * 1000; // 5 minutes buffer

    return expiresAt.getTime() - now.getTime() < bufferTime;
  }

  /**
   * Reset password
   * @param email User email
   */
  async resetPassword(email: string) {
    const { data, error } = await this.supabase.auth.resetPasswordForEmail(
      email,
      {
        redirectTo: `${window.location.origin}/auth/update-password`,
      }
    );

    if (error) {
      console.error('Password reset error:', error);
      throw error;
    }

    return data;
  }

  /**
   * Update password
   * @param password New password
   */
  async updatePassword(password: string) {
    const { data, error } = await this.supabase.auth.updateUser({
      password,
    });

    if (error) {
      console.error('Password update error:', error);
      throw error;
    }

    return data;
  }

  /**
   * Update user metadata
   * @param metadata User metadata to update
   */
  async updateUserMetadata(metadata: UserMetadata) {
    const { data, error } = await this.supabase.auth.updateUser({
      data: metadata,
    });

    if (error) {
      console.error('User metadata update error:', error);
      throw error;
    }

    return data;
  }

  /**
   * Verify OTP (One-Time Password)
   * @param email User email
   * @param token OTP token
   * @param type OTP type
   */
  async verifyOtp(
    email: string,
    token: string,
    type: 'signup' | 'invite' | 'magiclink' | 'recovery' | 'email_change'
  ) {
    const { data, error } = await this.supabase.auth.verifyOtp({
      email,
      token,
      type,
    });

    if (error) {
      console.error('OTP verification error:', error);
      throw error;
    }

    return data;
  }

  /**
   * Get Supabase client instance
   * Use this for direct database operations
   */
  getClient() {
    return this.supabase;
  }
}

// Export singleton instance
export const pkceAuth = new PKCEAuth();

/**
 * React hook for PKCE authentication
 * Use this in React components for authentication state management
 */
export function usePKCEAuth() {
  return pkceAuth;
}
