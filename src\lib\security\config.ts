/**
 * Security Configuration
 *
 * Centralized security configuration compliant with Next.js 14+ and Supabase standards
 */

export const SECURITY_CONFIG = {
  // JWT Configuration (Supabase Compliant)
  jwt: {
    // JWT validation settings
    audience: 'authenticated',
    issuer: process.env.NEXT_PUBLIC_SUPABASE_URL
      ? `${process.env.NEXT_PUBLIC_SUPABASE_URL}/auth/v1`
      : '',

    // JWT cache settings
    jwksCacheTTL: 24 * 60 * 60 * 1000, // 24 hours

    // JWT expiration buffer (5 minutes)
    expirationBuffer: 5 * 60,

    // Refresh threshold (refresh when less than 5 minutes remaining)
    refreshThreshold: 5 * 60 * 1000,
  },

  // Session Configuration (Supabase Compliant)
  session: {
    // Cookie settings
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      path: '/',
      httpOnly: false, // Supabase needs client access for token refresh
    },

    // Session duration based on risk score
    duration: {
      lowRisk: 24 * 60 * 60 * 1000, // 24 hours
      mediumRisk: 8 * 60 * 60 * 1000, // 8 hours
      highRisk: 2 * 60 * 60 * 1000, // 2 hours
      criticalRisk: 30 * 60 * 1000, // 30 minutes
    },

    // Verification intervals based on risk score
    verification: {
      lowRisk: 4 * 60 * 60 * 1000, // 4 hours
      mediumRisk: 60 * 60 * 1000, // 1 hour
      highRisk: 15 * 60 * 1000, // 15 minutes
      criticalRisk: 5 * 60 * 1000, // 5 minutes
    },
  },

  // Rate Limiting Configuration
  rateLimit: {
    // Global rate limits (requests per minute)
    global: {
      admin: 120,
      delivery: 90,
      customer: 60,
      anonymous: 30,
    },

    // Endpoint-specific limits
    endpoints: {
      '/api/auth/login': { rpm: 5, burst: 10 },
      '/api/auth/signup': { rpm: 3, burst: 5 },
      '/api/orders/create': { rpm: 10, burst: 15 },
      '/api/orders/update-status': { rpm: 30, burst: 45 },
      '/api/orders/assign-driver': { rpm: 20, burst: 30 },
      '/admin': { rpm: 60, burst: 90 },
      '/customer': { rpm: 40, burst: 60 },
      '/delivery': { rpm: 50, burst: 75 },
    },

    // Role-based multipliers
    roleMultipliers: {
      admin: 1.5,
      delivery: 1.2,
      customer: 1.0,
      anonymous: 0.5,
    },

    // Cache settings
    cacheTTL: 60 * 1000, // 1 minute
    cleanupInterval: 60 * 1000, // 1 minute
  },

  // Risk Scoring Configuration
  riskScoring: {
    // Base risk factors
    factors: {
      unauthenticated: 30,
      highRiskPath: 25,
      stateChangingMethod: 15,
      missingUserAgent: 20,
      unknownDevice: 30,
      suspiciousUserAgent: 40,
      invalidRequest: 35,
    },

    // Risk thresholds
    thresholds: {
      low: 30,
      medium: 50,
      high: 70,
      critical: 90,
    },

    // MFA requirements
    mfaThreshold: 70,

    // Privileged paths that increase risk
    privilegedPaths: [
      '/admin',
      '/api/admin',
      '/api/orders/assign-driver',
      '/api/orders/update-status',
      '/api/users',
    ],
  },

  // Security Headers Configuration (OWASP Compliant)
  headers: {
    contentSecurityPolicy: {
      enabled: true,
      reportOnly: process.env.NODE_ENV === 'development',
      directives: {
        'default-src': ["'self'"],
        'script-src': [
          "'self'",
          "'unsafe-inline'", // Required for Next.js
          "'unsafe-eval'", // Required for Next.js dev mode
          'https://js.stripe.com',
          'https://checkout.stripe.com',
          'https://maps.googleapis.com',
        ],
        'style-src': [
          "'self'",
          "'unsafe-inline'", // Required for styled-components
          'https://fonts.googleapis.com',
        ],
        'img-src': [
          "'self'",
          'data:',
          'blob:',
          'https:',
          'https://images.unsplash.com',
          'https://via.placeholder.com',
        ],
        'font-src': ["'self'", 'https://fonts.gstatic.com'],
        'connect-src': [
          "'self'",
          'https://api.stripe.com',
          'https://*.supabase.co',
          'wss://*.supabase.co',
          process.env.NEXT_PUBLIC_SUPABASE_URL || '',
        ].filter(Boolean),
        'frame-src': [
          "'self'",
          'https://js.stripe.com',
          'https://checkout.stripe.com',
        ],
        'object-src': ["'none'"],
        'base-uri': ["'self'"],
        'form-action': ["'self'"],
        'frame-ancestors': ["'none'"],
        'upgrade-insecure-requests': [],
      },
    },

    strictTransportSecurity: {
      enabled: process.env.NODE_ENV === 'production',
      maxAge: 31536000, // 1 year
      includeSubDomains: true,
      preload: true,
    },

    frameOptions: 'DENY',
    contentTypeOptions: true,
    referrerPolicy: 'strict-origin-when-cross-origin',

    permissionsPolicy: {
      camera: [],
      microphone: [],
      geolocation: ["'self'"],
      payment: ["'self'"],
      usb: [],
      magnetometer: [],
      gyroscope: [],
      accelerometer: [],
    },

    crossOriginEmbedderPolicy: 'credentialless',
    crossOriginOpenerPolicy: 'same-origin',
    crossOriginResourcePolicy: 'same-origin',
  },

  // Audit Logging Configuration
  audit: {
    // Event severity mapping
    severity: {
      LOGIN_SUCCESS: 'LOW',
      LOGIN_FAILED: 'HIGH',
      UNAUTHORIZED_ACCESS: 'HIGH',
      PRIVILEGE_ESCALATION: 'CRITICAL',
      RATE_LIMIT_EXCEEDED: 'MEDIUM',
      SUSPICIOUS_REQUEST: 'HIGH',
      MFA_REQUIRED: 'MEDIUM',
      SESSION_EXPIRED: 'MEDIUM',
      CSRF_VALIDATION_FAILED: 'HIGH',
      INVALID_REQUEST: 'MEDIUM',
    },

    // Event categories
    categories: {
      LOGIN_SUCCESS: 'AUTH',
      LOGIN_FAILED: 'AUTH',
      UNAUTHORIZED_ACCESS: 'ACCESS',
      PRIVILEGE_ESCALATION: 'ACCESS',
      RATE_LIMIT_EXCEEDED: 'ACCESS',
      SUSPICIOUS_REQUEST: 'SECURITY',
      ORDER_STATUS_UPDATED: 'DATA',
      PROFILE_UPDATED: 'DATA',
      MIDDLEWARE_ERROR: 'SYSTEM',
    },

    // Storage settings
    maxLogs: 10000,
    cleanupInterval: 24 * 60 * 60 * 1000, // 24 hours

    // Alert thresholds
    alertThresholds: {
      criticalEvents: 1,
      highSeverityEvents: 5,
      failedLogins: 5,
      rateLimitViolations: 10,
    },
  },

  // CSRF Protection Configuration
  csrf: {
    enabled: true,
    tokenLength: 32,
    headerName: 'x-csrf-token',
    cookieName: 'csrf-token',
    excludePaths: ['/api/auth/callback', '/api/webhooks'],
  },

  // MFA Configuration
  mfa: {
    enabled: false, // TODO: Enable when MFA is fully implemented
    issuer: 'Mouvers',
    serviceName: 'Mouvers App',
    totpWindow: 1, // Allow 1 time step tolerance
    challengeExpiry: 5 * 60 * 1000, // 5 minutes

    // MFA requirements by role
    roleRequirements: {
      admin: true,
      delivery: false,
      customer: false,
    },
  },

  // Middleware Configuration
  middleware: {
    // Paths that require Zero Trust checks
    sensitivePaths: ['/admin', '/api/admin', '/api/orders'],

    // Paths that are protected but don't need full Zero Trust
    protectedPaths: ['/admin', '/customer', '/dashboard', '/delivery'],

    // Auth paths for redirection logic
    authPaths: ['/auth/login', '/auth/sign-up'],

    // Password reset paths (exempt from auth redirect)
    passwordResetPaths: [
      '/auth/forgot-password',
      '/auth/confirm',
      '/auth/update-password',
    ],
  },
} as const;

// Type definitions for configuration
export type SecurityConfig = typeof SECURITY_CONFIG;
export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';
export type UserRole = 'admin' | 'customer' | 'delivery' | 'anonymous';
export type EventSeverity = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
export type EventCategory = 'AUTH' | 'ACCESS' | 'DATA' | 'SYSTEM' | 'SECURITY';

// Helper functions
export function getRiskLevel(score: number): RiskLevel {
  if (score >= SECURITY_CONFIG.riskScoring.thresholds.critical)
    return 'critical';
  if (score >= SECURITY_CONFIG.riskScoring.thresholds.high) return 'high';
  if (score >= SECURITY_CONFIG.riskScoring.thresholds.medium) return 'medium';
  return 'low';
}

export function getSessionDuration(riskLevel: RiskLevel): number {
  return SECURITY_CONFIG.session.duration[
    `${riskLevel}Risk` as keyof typeof SECURITY_CONFIG.session.duration
  ];
}

export function getVerificationInterval(riskLevel: RiskLevel): number {
  return SECURITY_CONFIG.session.verification[
    `${riskLevel}Risk` as keyof typeof SECURITY_CONFIG.session.verification
  ];
}

export function getRateLimit(role: UserRole): number {
  return SECURITY_CONFIG.rateLimit.global[role];
}

export function requiresMFA(riskScore: number): boolean {
  return riskScore >= SECURITY_CONFIG.riskScoring.mfaThreshold;
}

export function isPrivilegedPath(path: string): boolean {
  return SECURITY_CONFIG.riskScoring.privilegedPaths.some(privilegedPath =>
    path.startsWith(privilegedPath)
  );
}

export function isSensitivePath(path: string): boolean {
  return SECURITY_CONFIG.middleware.sensitivePaths.some(sensitivePath =>
    path.startsWith(sensitivePath)
  );
}

export function isProtectedPath(path: string): boolean {
  return SECURITY_CONFIG.middleware.protectedPaths.some(protectedPath =>
    path.startsWith(protectedPath)
  );
}
