'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { createClient } from '@/utils/supabase/client';
import { Trash2, Edit, Plus, Truck } from 'lucide-react';
import { VehicleImage } from '@/components/vehicles/VehicleImage';

interface VehicleType {
  id: string;
  name: string;
  category: string;
  max_weight_kg: number;
  max_volume_m3: number;
  base_rate_per_km: number;
  special_capabilities: string[];
  description: string | null;
  created_at: string;
  updated_at: string;
}

export function VehicleManagement() {
  const [vehicles, setVehicles] = useState<VehicleType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingVehicle, setEditingVehicle] = useState<VehicleType | null>(
    null
  );
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    max_weight_kg: 0,
    max_volume_m3: 0,
    base_rate_per_km: 0,
    special_capabilities: '',
    description: '',
  });

  const supabase = createClient();

  const fetchVehicles = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('vehicle_types')
        .select('*')
        .order('category', { ascending: true })
        .order('name', { ascending: true });

      if (error) throw error;

      setVehicles(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error fetching vehicles');
      console.error('Error fetching vehicles:', err);
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  useEffect(() => {
    fetchVehicles();
  }, [fetchVehicles]);

  const handleCreateVehicle = async () => {
    try {
      const { error } = await supabase.from('vehicle_types').insert({
        name: formData.name,
        category: formData.category,
        max_weight_kg: formData.max_weight_kg,
        max_volume_m3: formData.max_volume_m3,
        base_rate_per_km: formData.base_rate_per_km,
        special_capabilities: formData.special_capabilities
          .split(',')
          .map(cap => cap.trim())
          .filter(cap => cap),
        description: formData.description || null,
      });

      if (error) throw error;

      await fetchVehicles();
      resetForm();
      setIsDialogOpen(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error creating vehicle');
      console.error('Error creating vehicle:', err);
    }
  };

  const handleUpdateVehicle = async () => {
    if (!editingVehicle) return;

    try {
      const { error } = await supabase
        .from('vehicle_types')
        .update({
          name: formData.name,
          category: formData.category,
          max_weight_kg: formData.max_weight_kg,
          max_volume_m3: formData.max_volume_m3,
          base_rate_per_km: formData.base_rate_per_km,
          special_capabilities: formData.special_capabilities
            .split(',')
            .map(cap => cap.trim())
            .filter(cap => cap),
          description: formData.description || null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', editingVehicle.id);

      if (error) throw error;

      await fetchVehicles();
      resetForm();
      setIsDialogOpen(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error updating vehicle');
      console.error('Error updating vehicle:', err);
    }
  };

  const handleDeleteVehicle = async (id: string) => {
    if (
      !confirm('¿Estás seguro de que deseas eliminar este tipo de vehículo?')
    ) {
      return;
    }

    try {
      const { error } = await supabase
        .from('vehicle_types')
        .delete()
        .eq('id', id);

      if (error) throw error;

      await fetchVehicles();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error deleting vehicle');
      console.error('Error deleting vehicle:', err);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      category: '',
      max_weight_kg: 0,
      max_volume_m3: 0,
      base_rate_per_km: 0,
      special_capabilities: '',
      description: '',
    });
    setEditingVehicle(null);
  };

  const openEditDialog = (vehicle: VehicleType) => {
    setEditingVehicle(vehicle);
    setFormData({
      name: vehicle.name,
      category: vehicle.category,
      max_weight_kg: vehicle.max_weight_kg,
      max_volume_m3: vehicle.max_volume_m3,
      base_rate_per_km: vehicle.base_rate_per_km,
      special_capabilities: vehicle.special_capabilities.join(', '),
      description: vehicle.description || '',
    });
    setIsDialogOpen(true);
  };

  const openCreateDialog = () => {
    resetForm();
    setIsDialogOpen(true);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingVehicle) {
      handleUpdateVehicle();
    } else {
      handleCreateVehicle();
    }
  };

  if (loading) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500'></div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold text-gray-900'>
            Gestión de Vehículos
          </h2>
          <p className='text-gray-600'>
            Administra los tipos de vehículos disponibles para entregas
          </p>
        </div>
        <Button onClick={openCreateDialog}>
          <Plus className='w-4 h-4 mr-2' />
          Agregar Vehículo
        </Button>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className='max-w-2xl'>
            <DialogHeader>
              <DialogTitle>
                {editingVehicle ? 'Editar Vehículo' : 'Agregar Nuevo Vehículo'}
              </DialogTitle>
              <DialogDescription>
                {editingVehicle
                  ? 'Modifica la información del vehículo'
                  : 'Agrega un nuevo tipo de vehículo al sistema'}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label htmlFor='name'>Nombre del Vehículo</Label>
                  <Input
                    id='name'
                    value={formData.name}
                    onChange={e =>
                      setFormData({ ...formData, name: e.target.value })
                    }
                    required
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='category'>Categoría</Label>
                  <Input
                    id='category'
                    value={formData.category}
                    onChange={e =>
                      setFormData({ ...formData, category: e.target.value })
                    }
                    required
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='max_weight_kg'>Peso Máximo (kg)</Label>
                  <Input
                    id='max_weight_kg'
                    type='number'
                    value={formData.max_weight_kg}
                    onChange={e =>
                      setFormData({
                        ...formData,
                        max_weight_kg: parseFloat(e.target.value) || 0,
                      })
                    }
                    required
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='max_volume_m3'>Volumen Máximo (m³)</Label>
                  <Input
                    id='max_volume_m3'
                    type='number'
                    step='0.01'
                    value={formData.max_volume_m3}
                    onChange={e =>
                      setFormData({
                        ...formData,
                        max_volume_m3: parseFloat(e.target.value) || 0,
                      })
                    }
                    required
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='base_rate_per_km'>Tarifa Base por km</Label>
                  <Input
                    id='base_rate_per_km'
                    type='number'
                    step='0.01'
                    value={formData.base_rate_per_km}
                    onChange={e =>
                      setFormData({
                        ...formData,
                        base_rate_per_km: parseFloat(e.target.value) || 0,
                      })
                    }
                    required
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='special_capabilities'>
                    Capacidades Especiales
                  </Label>
                  <Input
                    id='special_capabilities'
                    value={formData.special_capabilities}
                    onChange={e =>
                      setFormData({
                        ...formData,
                        special_capabilities: e.target.value,
                      })
                    }
                    placeholder='frágil, refrigerado, peligroso'
                  />
                </div>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='description'>Descripción</Label>
                <Textarea
                  id='description'
                  value={formData.description}
                  onChange={e =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  placeholder='Descripción del tipo de vehículo'
                />
              </div>
              <div className='flex justify-end space-x-2'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancelar
                </Button>
                <Button type='submit'>
                  {editingVehicle ? 'Actualizar' : 'Crear'} Vehículo
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {error && (
        <Card>
          <CardContent className='p-4'>
            <div className='text-red-500'>{error}</div>
          </CardContent>
        </Card>
      )}

      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
        {vehicles.map(vehicle => (
          <Card key={vehicle.id} className='hover:shadow-md transition-shadow'>
            <CardHeader>
              <div className='flex items-start justify-between'>
                <div>
                  <CardTitle className='flex items-center gap-2'>
                    <VehicleImage
                      vehicleCategory={vehicle.category}
                      className='w-6 h-6 object-contain'
                      alt={vehicle.name}
                    />
                    {vehicle.name}
                  </CardTitle>
                  <CardDescription>{vehicle.category}</CardDescription>
                </div>
                <Badge variant='secondary'>
                  {vehicle.max_weight_kg}kg / {vehicle.max_volume_m3}m³
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className='space-y-3'>
                <div>
                  <p className='text-sm text-gray-600'>Tarifa Base</p>
                  <p className='font-medium'>
                    ${vehicle.base_rate_per_km.toFixed(2)}/km
                  </p>
                </div>
                {vehicle.special_capabilities.length > 0 && (
                  <div>
                    <p className='text-sm text-gray-600'>Capacidades</p>
                    <div className='flex flex-wrap gap-1 mt-1'>
                      {vehicle.special_capabilities.map((cap, index) => (
                        <Badge
                          key={index}
                          variant='outline'
                          className='text-xs'
                        >
                          {cap}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                {vehicle.description && (
                  <div>
                    <p className='text-sm text-gray-600'>Descripción</p>
                    <p className='text-sm'>{vehicle.description}</p>
                  </div>
                )}
                <div className='flex justify-end space-x-2 pt-2'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => openEditDialog(vehicle)}
                  >
                    <Edit className='w-4 h-4' />
                  </Button>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => handleDeleteVehicle(vehicle.id)}
                  >
                    <Trash2 className='w-4 h-4' />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {vehicles.length === 0 && !loading && (
        <Card>
          <CardContent className='text-center py-12'>
            <Truck className='w-12 h-12 mx-auto text-gray-400 mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>
              No hay vehículos registrados
            </h3>
            <p className='text-gray-600 mb-4'>
              Comienza agregando un nuevo tipo de vehículo
            </p>
            <Button onClick={openCreateDialog}>
              <Plus className='w-4 h-4 mr-2' />
              Agregar Vehículo
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
