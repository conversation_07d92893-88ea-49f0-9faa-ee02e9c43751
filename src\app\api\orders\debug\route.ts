import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Check environment variables
    if (
      !process.env.NEXT_PUBLIC_SUPABASE_URL ||
      !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    ) {
      return NextResponse.json(
        { error: 'Missing environment variables' },
        { status: 500 }
      );
    }

    let supabase;
    try {
      supabase = await createClient();
    } catch (error) {
      return NextResponse.json(
        {
          error: 'Failed to create Supabase client',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        { status: 500 }
      );
    }

    // Check for Authorization header
    const authHeader = request.headers.get('authorization');
    let user = null;
    let authError = null;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      // Use the access token from the Authorization header
      const token = authHeader.substring(7);
      const { data, error } = await supabase.auth.getUser(token);
      user = data.user;
      authError = error;
    } else {
      // Fallback to session-based authentication
      const { data, error } = await supabase.auth.getUser();
      user = data.user;
      authError = error;
    }

    if (authError) {
      return NextResponse.json(
        {
          error: 'Authentication failed',
          details: authError.message,
        },
        { status: 401 }
      );
    }

    // Test database connection by fetching a single order
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select('id, status, updated_at, created_at')
      .limit(1);

    if (ordersError) {
      return NextResponse.json(
        {
          error: 'Database query failed',
          details: ordersError.message,
          code: ordersError.code,
          hint: ordersError.hint,
        },
        { status: 500 }
      );
    }

    // Test if we can get user profile
    let profile = null;
    if (user) {
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id, role')
        .eq('id', user.id)
        .single();

      if (profileError) {
        return NextResponse.json(
          {
            error: 'Profile query failed',
            details: profileError.message,
            code: profileError.code,
            hint: profileError.hint,
          },
          { status: 500 }
        );
      }
      profile = profileData;
    }

    return NextResponse.json({
      success: true,
      environment: {
        hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasSupabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      },
      authentication: {
        isAuthenticated: !!user,
        userId: user?.id,
        userEmail: user?.email,
      },
      profile: profile,
      database: {
        ordersTableAccessible: true,
        sampleOrdersCount: orders?.length || 0,
        sampleOrder: orders?.[0] || null,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Unexpected error',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      },
      { status: 500 }
    );
  }
}
