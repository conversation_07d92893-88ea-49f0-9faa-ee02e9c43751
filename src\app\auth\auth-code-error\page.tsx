'use client';

import { AuthLayout } from '@/components/auth/auth-layout';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';
import Link from 'next/link';

export default function AuthCodeErrorPage() {
  return (
    <AuthLayout>
      <div className='w-full max-w-md'>
        <div className='text-center mb-8'>
          <h1 className='text-3xl font-bold text-white mb-2'>Mouvers</h1>
          <p className='text-gray-200'>Error de autenticación</p>
        </div>

        <div className='bg-gradient-to-br from-red-900/20 to-orange-900/20 backdrop-blur-sm border border-red-400/30 rounded-lg p-6 shadow-xl'>
          <div className='text-center'>
            <div className='mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4'>
              <AlertTriangle className='h-6 w-6 text-red-600' />
            </div>

            <h3 className='text-lg font-medium text-white mb-2'>
              Error de autenticación
            </h3>

            <p className='text-sm text-gray-300 mb-6'>
              El enlace de autenticación es inválido o ha expirado. Esto puede
              ocurrir si:
            </p>

            <ul className='text-sm text-gray-300 text-left mb-6 space-y-1'>
              <li>• El enlace ya fue utilizado</li>
              <li>• El enlace ha expirado</li>
              <li>• Hubo un error en el proceso de autenticación</li>
            </ul>

            <div className='space-y-3'>
              <Link href='/auth/login' className='block'>
                <Button className='w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white border-0'>
                  Ir al Login
                </Button>
              </Link>

              <Link href='/auth/forgot-password' className='block'>
                <Button
                  variant='outline'
                  className='w-full border-gray-300 text-gray-300 hover:bg-gray-800 hover:text-white'
                >
                  Recuperar contraseña
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </AuthLayout>
  );
}
