'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

import { Badge } from '@/components/ui/badge';
import { OrderFormData, Stop } from '@/types/order-form';
import { VehicleImage } from '@/components/vehicles/VehicleImage';

interface Step9Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
  errors?: Record<string, string>;
}

export function OrderWizardStep9({ formData, updateFormData }: Step9Props) {
  // Calculate totals
  const totalCost = formData.products.reduce(
    (sum, product) => sum + product.subtotal,
    0
  );

  const totalWeight = formData.products.reduce((sum, product) => {
    const weight = product.weight || 1;
    const weightInKg = product.weight_unit === 'g' ? weight / 1000 : weight;
    return sum + weightInKg * product.quantity;
  }, 0);

  // Calculate total volume for display (currently not shown in UI)
  // const totalVolume = formData.products.reduce((sum, product) => {
  //   if (!product.dimensions) return sum;
  //   const volume =
  //     (product.dimensions.length *
  //       product.dimensions.width *
  //       product.dimensions.height) /
  //     1000000;
  //   return sum + volume * product.quantity;
  // }, 0);

  // Calculate estimated delivery cost (mock calculation)
  const estimatedDeliveryCost = 150;

  const totalOrderCost = totalCost + estimatedDeliveryCost;

  return (
    <div className='space-y-6'>
      {/* Order Summary Header */}
      <Card className='bg-blue-50 border-blue-200'>
        <CardHeader>
          <CardTitle className='flex items-center gap-2 text-blue-800'>
            ✅ Resumen del Pedido
          </CardTitle>
          <CardDescription className='text-blue-700'>
            Revisa todos los detalles antes de confirmar tu pedido
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4 text-center'>
            <div>
              <div className='text-2xl font-bold text-blue-600'>
                ${totalOrderCost.toFixed(2)}
              </div>
              <div className='text-sm text-blue-700'>Total Final</div>
            </div>
            <div>
              <div className='text-2xl font-bold text-blue-600'>
                {formData.products.length}
              </div>
              <div className='text-sm text-blue-700'>Productos</div>
            </div>
            <div>
              <div className='text-2xl font-bold text-blue-600'>
                {totalWeight.toFixed(2)} kg
              </div>
              <div className='text-sm text-blue-700'>Peso Total</div>
            </div>
            <div>
              <div className='text-2xl font-bold text-blue-600'>
                {formData.stops && formData.stops.length > 0
                  ? formData.stops.length
                  : 1}
              </div>
              <div className='text-sm text-blue-700'>
                {formData.stops && formData.stops.length > 0
                  ? 'Paradas'
                  : 'Entrega'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              👤 Información del Cliente
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-3'>
            <div>
              <div className='font-medium'>{formData.customer_name}</div>
              <div className='text-sm text-gray-600'>
                {formData.customer_phone}
              </div>
              {formData.customer_email && (
                <div className='text-sm text-gray-600'>
                  {formData.customer_email}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Delivery Information */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              📍 Información de Entrega
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-3'>
            <div>
              <div className='font-medium'>
                {formData.delivery_address.street}{' '}
                {formData.delivery_address.number}
              </div>
              <div className='text-sm text-gray-600'>
                {formData.delivery_address.colony},{' '}
                {formData.delivery_address.city}
              </div>
              <div className='text-sm text-gray-600'>
                {formData.delivery_address.state}, C.P.{' '}
                {formData.delivery_address.zip}
              </div>
              {formData.delivery_address.references && (
                <div className='text-sm text-gray-500 italic'>
                  Ref: {formData.delivery_address.references}
                </div>
              )}
            </div>
            <div className='pt-2 border-t'>
              <div className='text-sm'>
                <strong>Fecha:</strong> {formData.delivery_date}
              </div>
              <div className='text-sm'>
                <strong>Horario:</strong> {formData.delivery_time_slot}
              </div>
              <div className='text-sm'>
                <strong>Modalidad:</strong>{' '}
                {formData.delivery_mode === 'home'
                  ? 'Entrega a domicilio'
                  : 'Recoger en punto'}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Products Summary */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            🛒 Productos del Pedido
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            {formData.products.map(product => (
              <div
                key={product.id}
                className='flex justify-between items-start p-3 bg-gray-50 rounded-lg'
              >
                <div className='flex-1'>
                  <div className='font-medium'>{product.name}</div>
                  <div className='text-sm text-gray-600'>
                    {product.quantity} {product.unit_measure} × $
                    {product.unit_price.toFixed(2)}
                  </div>
                  <div className='text-sm text-gray-600'>
                    Peso: {product.weight} {product.weight_unit}
                    {product.dimensions && (
                      <span className='ml-2'>
                        | Dim: {product.dimensions.length}×
                        {product.dimensions.width}×{product.dimensions.height}{' '}
                        {product.dimensions.unit}
                      </span>
                    )}
                  </div>
                  {/* Special Handling Badges */}
                  <div className='flex flex-wrap gap-1 mt-2'>
                    {product.special_handling?.fragile && (
                      <Badge variant='outline' className='text-xs'>
                        🔸 Frágil
                      </Badge>
                    )}
                    {product.special_handling?.perishable && (
                      <Badge variant='outline' className='text-xs'>
                        ❄️ Perecedero
                      </Badge>
                    )}
                    {product.special_handling?.valuable && (
                      <Badge variant='outline' className='text-xs'>
                        💎 Valioso
                      </Badge>
                    )}
                    {product.special_handling?.hazardous && (
                      <Badge variant='outline' className='text-xs'>
                        ⚠️ Peligroso
                      </Badge>
                    )}
                    {product.special_handling?.refrigerated && (
                      <Badge variant='outline' className='text-xs'>
                        🧊 Refrigerado
                      </Badge>
                    )}
                    {product.special_handling?.oversized && (
                      <Badge variant='outline' className='text-xs'>
                        📏 Sobredimensionado
                      </Badge>
                    )}
                  </div>
                </div>
                <div className='text-right'>
                  <div className='font-medium'>
                    ${product.subtotal.toFixed(2)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Multi-Stop Summary */}
      {formData.stops && formData.stops.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              🗺️ Ruta Multi-Parada
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-3'>
              {formData.stops.map((stop: Stop, index) => (
                <div
                  key={stop.id}
                  className='flex items-start space-x-3 p-3 bg-gray-50 rounded-lg'
                >
                  <Badge variant='outline'>#{index + 1}</Badge>
                  <div className='flex-1'>
                    <div className='font-medium'>{stop.address}</div>
                    <div className='text-sm text-gray-600'>
                      {stop.city}, {stop.state}
                    </div>
                    <div className='text-sm text-gray-600'>C.P. {stop.zip}</div>
                    {stop.instructions && (
                      <div className='text-sm text-gray-500 italic mt-1'>
                        {stop.instructions}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Vehicle and Logistics */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            🚚 Vehículo y Logística
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex items-start space-x-4'>
            <div className='w-24 h-16 bg-gray-100 rounded overflow-hidden'>
              <VehicleImage
                vehicleTypeId={formData.vehicle_type_id}
                className='w-full h-full object-cover'
              />
            </div>
            <div className='flex-1'>
              <div className='font-medium'>Vehículo Seleccionado</div>
              <div className='text-sm text-gray-600'>
                Tipo: {formData.vehicle_type_id || 'No especificado'}
              </div>
            </div>
            <div className='text-right'>
              <div className='font-medium'>
                ${estimatedDeliveryCost.toFixed(2)}
              </div>
              <div className='text-sm text-gray-600'>Costo estimado</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Information */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            💳 Información de Pago
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            <div>
              <div className='font-medium'>
                Método de pago:{' '}
                {formData.payment_method === 'card'
                  ? 'Tarjeta de crédito/débito'
                  : formData.payment_method === 'cash'
                    ? 'Efectivo contra entrega'
                    : formData.payment_method === 'digital_wallet'
                      ? 'Billetera digital'
                      : formData.payment_method === 'bank_transfer'
                        ? 'Transferencia bancaria'
                        : 'No especificado'}
              </div>
              {formData.invoice_required && (
                <div className='text-sm text-gray-600 mt-2'>
                  <strong>Factura requerida:</strong> Sí
                  {formData.fiscal_data?.rfc && (
                    <div>RFC: {formData.fiscal_data.rfc}</div>
                  )}
                  {formData.fiscal_data?.business_name && (
                    <div>
                      Razón Social: {formData.fiscal_data.business_name}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cost Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            💰 Desglose de Costos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-2'>
            <div className='flex justify-between'>
              <span>Subtotal productos:</span>
              <span>${totalCost.toFixed(2)}</span>
            </div>
            <div className='flex justify-between'>
              <span>Costo de entrega:</span>
              <span>${estimatedDeliveryCost.toFixed(2)}</span>
            </div>
            {formData.stops && formData.stops.length > 1 && (
              <div className='flex justify-between text-sm text-gray-600'>
                <span>Paradas adicionales ({formData.stops.length - 1}):</span>
                <span>${((formData.stops.length - 1) * 25).toFixed(2)}</span>
              </div>
            )}
            <div className='border-t pt-2 flex justify-between font-bold text-lg'>
              <span>Total:</span>
              <span className='text-green-600'>
                ${totalOrderCost.toFixed(2)} MXN
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Special Instructions */}
      {formData.special_instructions && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              📝 Instrucciones Especiales
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-3'>
              <p className='text-sm'>{formData.special_instructions}</p>
            </div>
            <div className='mt-3 text-sm text-gray-600'>
              <strong>Política de sustituciones:</strong>{' '}
              {formData.allow_substitutions
                ? 'Se permiten sustituciones de productos agotados'
                : 'No se permiten sustituciones, cancelar productos agotados'}
            </div>
          </CardContent>
        </Card>
      )}
      {/* Terms and Conditions Acceptance */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            📋 Términos y Condiciones
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            <div className='bg-gray-50 p-4 rounded-lg text-sm max-h-32 overflow-y-auto'>
              <p>
                Al marcar esta casilla, acepto los términos y condiciones del
                servicio de logística, incluyendo las políticas de entrega,
                cancelación y devolución. Entiendo que la información
                proporcionada es correcta y autorizo el procesamiento de mi
                pedido.
              </p>
            </div>
            <div className='flex items-center space-x-2'>
              <input
                type='checkbox'
                id='terms_accepted'
                checked={formData.terms_accepted || false}
                onChange={e =>
                  updateFormData({ terms_accepted: e.target.checked })
                }
                className='h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500'
              />
              <label
                htmlFor='terms_accepted'
                className='font-medium text-gray-700'
              >
                Acepto los términos y condiciones
              </label>
            </div>
            {formData.terms_accepted === false && (
              <p className='text-red-500 text-sm'>
                Debes aceptar los términos y condiciones para continuar
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
