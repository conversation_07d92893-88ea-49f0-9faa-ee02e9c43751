'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';

import { useAuthStore } from '@/stores/authStore';
import { OrdersManagement } from '@/components/admin/orders-management';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { createClient } from '@/utils/supabase/client';

interface Order {
  id: string;
  customer_id: string;
  customer_name: string;
  customer_email: string;
  status:
    | 'pending'
    | 'confirmed'
    | 'in-transit'
    | 'pending-admin-confirmation'
    | 'delivered'
    | 'closed'
    | 'cancelled';
  pickup_address: {
    street_address: string;
    city: string;
    state: string;
    contact_name: string;
    contact_phone: string;
  };
  delivery_addresses: {
    street_address: string;
    city: string;
    state: string;
    recipient_name: string;
    phone: string;
  };
  package_details: {
    description: string;
    weight: string;
    dimensions: string;
    value: string;
  };
  total_cost: number;
  payment_status: 'pending' | 'paid' | 'failed';
  created_at: string;
  updated_at: string;
}

function AdminOrdersContent() {
  const { user, isAdmin } = useAuthStore();
  const [orders, setOrders] = useState<Order[]>([]);
  const [ordersLoading, setOrdersLoading] = useState(true);

  // Memoize the Supabase client to prevent recreation on every render
  const supabase = useMemo(() => createClient(), []);

  const fetchOrders = useCallback(async () => {
    // Middleware guarantees user is authenticated and has admin role
    if (!user) {
      return;
    }

    try {
      // Fetch orders directly from the orders table
      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching orders:', error);
        return;
      }

      // Transform data to match the expected interface
      const transformedOrders =
        data?.map(order => ({
          id: order.id,
          customer_id: order.customer_id,
          customer_name: order.pickup_address?.contact_name || 'Unknown',
          customer_email: 'Customer Email', // We'll get this from profiles if needed
          status: order.status || 'pending',
          pickup_address: {
            street_address: order.pickup_address?.street || '',
            city: order.pickup_address?.city || '',
            state: order.pickup_address?.state || '',
            contact_name: order.pickup_address?.contact_name || '',
            contact_phone: order.pickup_address?.phone || '',
          },
          delivery_addresses: {
            street_address: order.delivery_addresses?.street || '',
            city: order.delivery_addresses?.city || '',
            state: order.delivery_addresses?.state || '',
            recipient_name: order.delivery_addresses?.contact_name || '',
            phone: order.delivery_addresses?.phone || '',
          },
          package_details: {
            description: order.package_details?.content_description || '',
            weight: order.package_details?.weight || '',
            dimensions: order.package_details?.dimensions || '',
            value: order.total_cost?.toString() || '0',
          },
          total_cost: parseFloat(order.total_cost) || 0,
          payment_status: order.payment_status || 'pending',
          created_at: order.created_at,
          updated_at: order.updated_at,
        })) || [];

      setOrders(transformedOrders);
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setOrdersLoading(false);
    }
  }, [supabase, user]);

  useEffect(() => {
    if (user && isAdmin) {
      fetchOrders();
    }
  }, [user, isAdmin, fetchOrders]);

  const handleUpdateOrderStatus = useCallback(
    async (orderId: string, newStatus: Order['status']) => {
      try {
        // Get the current session to include the access token
        const {
          data: { session },
        } = await supabase.auth.getSession();

        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
        };

        // Include authorization header if we have a session
        if (session?.access_token) {
          headers['Authorization'] = `Bearer ${session.access_token}`;
        }

        const response = await fetch('/api/orders/update-status', {
          method: 'PUT',
          headers,
          body: JSON.stringify({
            orderId,
            newStatus,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('API Error Response:', errorData);
          const errorMessage = errorData.details
            ? `${errorData.error}: ${errorData.details}`
            : errorData.error || 'Failed to update order status';
          throw new Error(errorMessage);
        }

        // Update local state
        setOrders(prev =>
          prev.map(order =>
            order.id === orderId ? { ...order, status: newStatus } : order
          )
        );
      } catch (error) {
        console.error('Error:', error);
        alert(
          error instanceof Error
            ? error.message
            : 'Failed to update order status'
        );
      }
    },
    [supabase.auth]
  );

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Header */}
      <header className='bg-white shadow-sm border-b'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center h-16'>
            <div className='flex items-center space-x-4'>
              <Button
                variant='ghost'
                onClick={() => window.history.back()}
                className='text-blue-600'
              >
                ← Volver al Panel
              </Button>
              <h1 className='text-xl font-bold text-black'>
                Gestión de Pedidos
              </h1>
            </div>
            <div className='flex items-center space-x-4'>
              <span className='text-sm text-gray-600'>
                {user?.email || 'Admin'}
              </span>
              <Badge variant='default'>Admin</Badge>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='max-w-7xl mx-auto py-6 sm:px-6 lg:px-8'>
        <div className='px-4 py-6 sm:px-0'>
          <OrdersManagement
            orders={orders}
            onUpdateOrderStatus={handleUpdateOrderStatus}
            isLoading={ordersLoading}
          />
        </div>
      </main>
    </div>
  );
}

export default function AdminOrdersPage() {
  // Middleware guarantees user is authenticated and has admin role
  return <AdminOrdersContent />;
}
