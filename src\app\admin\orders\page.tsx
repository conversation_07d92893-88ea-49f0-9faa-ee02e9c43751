'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';

import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { createClient } from '@/utils/supabase/client';
import { OrderTrackingService } from '@/lib/services/order-tracking';
import { VehicleFleetManagementService } from '@/lib/services/vehicle-fleet-management';
import { OrderTracking, ProductItem, Stop } from '@/types/order-form';

interface Order {
  id: string;
  customer_id: string;
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  tracking_number: string;
  status:
    | 'pending'
    | 'confirmed'
    | 'in-transit'
    | 'pending-admin-confirmation'
    | 'delivered'
    | 'closed'
    | 'cancelled';
  pickup_address: {
    street_address: string;
    city: string;
    state: string;
    contact_name: string;
    contact_phone: string;
  };
  delivery_addresses: {
    street_address: string;
    city: string;
    state: string;
    recipient_name: string;
    phone: string;
  };
  package_details: {
    description: string;
    weight: string;
    dimensions: string;
    value: string;
  };
  products: ProductItem[];
  stops: Stop[];
  delivery_date: string;
  delivery_time_slot: string;
  vehicle_id?: string;
  driver_id?: string;
  vehicle_type_id?: string;
  cargo_type_id?: string;
  route_optimization: string;
  delivery_region: string;
  special_instructions?: string;
  total_cost: number;
  payment_status: 'pending' | 'paid' | 'failed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  created_at: string;
  updated_at: string;
}

interface FleetStatus {
  total_vehicles: number;
  available: number;
  assigned: number;
  in_transit: number;
  maintenance: number;
  offline: number;
  by_category: Record<string, number>;
}

interface VehicleFleet {
  id: string;
  vehicle_types: {
    name: string;
  };
}

interface Driver {
  full_name: string;
  phone: string;
}

interface OrderFromDB {
  id: string;
  customer_id: string;
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  tracking_number: string;
  status: string;
  pickup_address: {
    street: string;
    city: string;
    state: string;
    contact_name: string;
    phone: string;
  };
  delivery_addresses: {
    street: string;
    city: string;
    state: string;
    contact_name: string;
    phone: string;
  };
  package_details: {
    content_description: string;
    weight: string;
    dimensions: string;
  };
  products: ProductItem[];
  stops: Stop[];
  delivery_date: string;
  delivery_time_slot: string;
  vehicle_id: string;
  driver_id: string;
  vehicle_type_id: string;
  cargo_type_id: string;
  route_optimization: string;
  delivery_region: string;
  special_instructions: string;
  total_cost: string;
  payment_status: string;
  created_at: string;
  updated_at: string;
  vehicle_fleet: VehicleFleet;
  drivers: Driver;
}

function AdminOrdersContent() {
  const router = useRouter();
  const { user, isAdmin } = useAuthStore();
  const [orders, setOrders] = useState<Order[]>([]);
  const [ordersLoading, setOrdersLoading] = useState(true);
  const [fleetStatus, setFleetStatus] = useState<FleetStatus | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);

  // Memoize the Supabase client to prevent recreation on every render
  const supabase = useMemo(() => createClient(), []);

  // Calculate order priority based on various factors
  const calculateOrderPriority = useCallback(
    (order: OrderFromDB): 'low' | 'medium' | 'high' | 'urgent' => {
      const deliveryDate = new Date(order.delivery_date || order.created_at);
      const now = new Date();
      const hoursUntilDelivery =
        (deliveryDate.getTime() - now.getTime()) / (1000 * 60 * 60);

      // Urgent: Same day or overdue
      if (hoursUntilDelivery <= 24) return 'urgent';

      // High: Next day or has special handling requirements
      if (
        hoursUntilDelivery <= 48 ||
        order.products?.some(
          (p: ProductItem) =>
            p.special_handling?.perishable ||
            p.special_handling?.hazardous ||
            p.special_handling?.valuable
        )
      ) {
        return 'high';
      }

      // Medium: Within 3 days or high value
      if (hoursUntilDelivery <= 72 || parseFloat(order.total_cost) > 10000)
        return 'medium';

      return 'low';
    },
    []
  );

  const fetchOrders = useCallback(async () => {
    // Middleware guarantees user is authenticated and has admin role
    if (!user) {
      return;
    }

    try {
      // Fetch orders with enhanced data
      let query = supabase
        .from('orders')
        .select(
          `
          *,
          vehicle_fleet (
            id,
            vehicle_types (name)
          ),
          drivers (
            full_name,
            phone
          )
        `
        )
        .order('created_at', { ascending: false });

      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching orders:', error);
        return;
      }

      // Load fleet status
      const fleetResult = await VehicleFleetManagementService.getFleetStatus();
      if (fleetResult.success && fleetResult.data) {
        setFleetStatus(fleetResult.data);
      }

      // Transform data to match the enhanced interface
      const transformedOrders =
        data?.map(order => ({
          id: order.id,
          customer_id: order.customer_id,
          customer_name:
            order.customer_name ||
            order.pickup_address?.contact_name ||
            'Unknown',
          customer_email: order.customer_email || 'No email provided',
          customer_phone:
            order.customer_phone || order.pickup_address?.contact_phone || '',
          tracking_number:
            order.tracking_number || `MX${order.id.slice(-8).toUpperCase()}`,
          status: order.status || 'pending',
          pickup_address: {
            street_address: order.pickup_address?.street || '',
            city: order.pickup_address?.city || '',
            state: order.pickup_address?.state || '',
            contact_name: order.pickup_address?.contact_name || '',
            contact_phone: order.pickup_address?.phone || '',
          },
          delivery_addresses: {
            street_address: order.delivery_addresses?.street || '',
            city: order.delivery_addresses?.city || '',
            state: order.delivery_addresses?.state || '',
            recipient_name: order.delivery_addresses?.contact_name || '',
            phone: order.delivery_addresses?.phone || '',
          },
          package_details: {
            description: order.package_details?.content_description || '',
            weight: order.package_details?.weight || '',
            dimensions: order.package_details?.dimensions || '',
            value: order.total_cost?.toString() || '0',
          },
          products: order.products || [],
          stops: order.stops || [],
          delivery_date: order.delivery_date || order.created_at,
          delivery_time_slot: order.delivery_time_slot || '08:00-11:00',
          vehicle_id: order.vehicle_id,
          driver_id: order.driver_id,
          vehicle_type_id: order.vehicle_type_id,
          cargo_type_id: order.cargo_type_id,
          route_optimization: order.route_optimization || 'balanced',
          delivery_region: order.delivery_region || 'local',
          special_instructions: order.special_instructions,
          priority: calculateOrderPriority(order),
          total_cost: parseFloat(order.total_cost) || 0,
          payment_status: order.payment_status || 'pending',
          created_at: order.created_at,
          updated_at: order.updated_at,
        })) || [];

      setOrders(transformedOrders);
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setOrdersLoading(false);
    }
  }, [supabase, user, statusFilter, calculateOrderPriority]);

  // Enhanced order management functions
  const handleVehicleAssignment = useCallback(
    async (orderId: string) => {
      router.push(`/admin/orders/${orderId}/assign-vehicle`);
    },
    [router]
  );

  const handleStatusUpdate = useCallback(
    async (orderId: string, newStatus: string) => {
      try {
        // Map the status values to the expected enum values
        const statusMap: Record<string, OrderTracking['status']> = {
          pending: 'created',
          confirmed: 'confirmed',
          'in-transit': 'in_transit',
          'pending-admin-confirmation': 'confirmed',
          delivered: 'delivered',
          closed: 'delivered',
          cancelled: 'cancelled',
        };

        const mappedStatus = statusMap[newStatus] || 'created';

        await OrderTrackingService.updateOrderStatus(
          orderId,
          mappedStatus,
          undefined,
          `Estado actualizado por administrador a: ${newStatus}`
        );

        // Reload data
        fetchOrders();
      } catch (error) {
        console.error('Error updating status:', error);
        alert('Error al actualizar el estado');
      }
    },
    [fetchOrders]
  );

  const handleBulkStatusUpdate = useCallback(
    async (newStatus: string) => {
      if (selectedOrders.length === 0) return;

      try {
        // Map the status values to the expected enum values
        const statusMap: Record<string, OrderTracking['status']> = {
          pending: 'created',
          confirmed: 'confirmed',
          'in-transit': 'in_transit',
          'pending-admin-confirmation': 'confirmed',
          delivered: 'delivered',
          closed: 'delivered',
          cancelled: 'cancelled',
        };

        const mappedStatus = statusMap[newStatus] || 'created';

        await Promise.all(
          selectedOrders.map(orderId =>
            OrderTrackingService.updateOrderStatus(
              orderId,
              mappedStatus,
              undefined,
              `Estado actualizado en lote por administrador`
            )
          )
        );

        setSelectedOrders([]);
        fetchOrders();
      } catch (error) {
        console.error('Error in bulk update:', error);
        alert('Error en la actualización en lote');
      }
    },
    [selectedOrders, fetchOrders]
  );

  // Filter orders based on search and filters
  const filteredOrders = useMemo(() => {
    return orders.filter(order => {
      const matchesSearch =
        !searchQuery ||
        order.tracking_number
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        order.customer_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.customer_phone.includes(searchQuery);

      const matchesPriority =
        priorityFilter === 'all' || order.priority === priorityFilter;

      return matchesSearch && matchesPriority;
    });
  }, [orders, searchQuery, priorityFilter]);

  const getPriorityColor = useCallback((priority: string) => {
    const colors = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800',
    };
    return (
      colors[priority as keyof typeof colors] || 'bg-gray-100 text-gray-800'
    );
  }, []);

  const getStatusColor = useCallback((status: string) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      confirmed: 'bg-green-100 text-green-800',
      'in-transit': 'bg-blue-100 text-blue-800',
      delivered: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  }, []);

  useEffect(() => {
    if (user && isAdmin) {
      fetchOrders();
    }
  }, [user, isAdmin, fetchOrders]);

  if (ordersLoading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600'></div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='space-y-6'>
        {/* Header */}
        <div className='flex justify-between items-center'>
          <div>
            <h1 className='text-3xl font-bold text-gray-900'>
              🚚 Gestión de Pedidos
            </h1>
            <p className='text-gray-600'>
              Panel de administración para pedidos y asignación de vehículos
            </p>
          </div>
          <Button onClick={() => router.push('/admin/dashboard')}>
            ← Panel Principal
          </Button>
        </div>

        {/* Fleet Status Overview */}
        {fleetStatus && (
          <Card>
            <CardHeader>
              <CardTitle>📊 Estado de la Flota</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='grid grid-cols-2 md:grid-cols-6 gap-4'>
                <div className='text-center'>
                  <div className='text-2xl font-bold text-blue-600'>
                    {fleetStatus.total_vehicles}
                  </div>
                  <div className='text-sm text-gray-600'>Total</div>
                </div>
                <div className='text-center'>
                  <div className='text-2xl font-bold text-green-600'>
                    {fleetStatus.available}
                  </div>
                  <div className='text-sm text-gray-600'>Disponibles</div>
                </div>
                <div className='text-center'>
                  <div className='text-2xl font-bold text-orange-600'>
                    {fleetStatus.assigned}
                  </div>
                  <div className='text-sm text-gray-600'>Asignados</div>
                </div>
                <div className='text-center'>
                  <div className='text-2xl font-bold text-purple-600'>
                    {fleetStatus.in_transit}
                  </div>
                  <div className='text-sm text-gray-600'>En Tránsito</div>
                </div>
                <div className='text-center'>
                  <div className='text-2xl font-bold text-yellow-600'>
                    {fleetStatus.maintenance}
                  </div>
                  <div className='text-sm text-gray-600'>Mantenimiento</div>
                </div>
                <div className='text-center'>
                  <div className='text-2xl font-bold text-gray-600'>
                    {fleetStatus.offline}
                  </div>
                  <div className='text-sm text-gray-600'>Fuera de Línea</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Filters and Search */}
        <Card>
          <CardContent className='p-4'>
            <div className='flex flex-wrap gap-4 items-center'>
              <Input
                placeholder='Buscar por número de seguimiento, cliente o teléfono...'
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className='flex-1 min-w-64'
              />

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className='w-48'>
                  <SelectValue placeholder='Filtrar por estado' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>Todos los estados</SelectItem>
                  <SelectItem value='pending'>Pendiente</SelectItem>
                  <SelectItem value='confirmed'>Confirmado</SelectItem>
                  <SelectItem value='in-transit'>En Tránsito</SelectItem>
                  <SelectItem value='delivered'>Entregado</SelectItem>
                  <SelectItem value='cancelled'>Cancelado</SelectItem>
                </SelectContent>
              </Select>

              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className='w-48'>
                  <SelectValue placeholder='Filtrar por prioridad' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>Todas las prioridades</SelectItem>
                  <SelectItem value='urgent'>Urgente</SelectItem>
                  <SelectItem value='high'>Alta</SelectItem>
                  <SelectItem value='medium'>Media</SelectItem>
                  <SelectItem value='low'>Baja</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Bulk Actions */}
        {selectedOrders.length > 0 && (
          <Card>
            <CardContent className='p-4'>
              <div className='flex items-center gap-4'>
                <span className='text-sm text-gray-600'>
                  {selectedOrders.length} pedidos seleccionados
                </span>
                <Button
                  size='sm'
                  onClick={() => handleBulkStatusUpdate('confirmed')}
                >
                  Confirmar Seleccionados
                </Button>
                <Button
                  size='sm'
                  variant='outline'
                  onClick={() => setSelectedOrders([])}
                >
                  Limpiar Selección
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Enhanced Orders List */}
        <Card>
          <CardHeader>
            <CardTitle>📋 Lista de Pedidos ({filteredOrders.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {filteredOrders.map(order => (
                <div
                  key={order.id}
                  className='border rounded-lg p-4 hover:bg-gray-50 transition-colors'
                >
                  <div className='flex items-start justify-between mb-3'>
                    <div className='flex items-center space-x-3'>
                      <input
                        type='checkbox'
                        checked={selectedOrders.includes(order.id)}
                        onChange={e => {
                          if (e.target.checked) {
                            setSelectedOrders([...selectedOrders, order.id]);
                          } else {
                            setSelectedOrders(
                              selectedOrders.filter(id => id !== order.id)
                            );
                          }
                        }}
                        className='rounded'
                      />
                      <div>
                        <h3 className='font-semibold'>{order.customer_name}</h3>
                        <p className='text-sm text-gray-600 font-mono'>
                          {order.tracking_number}
                        </p>
                      </div>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <Badge className={getPriorityColor(order.priority)}>
                        {order.priority.toUpperCase()}
                      </Badge>
                      <Badge className={getStatusColor(order.status)}>
                        {order.status}
                      </Badge>
                    </div>
                  </div>

                  <div className='grid grid-cols-1 md:grid-cols-4 gap-4 text-sm'>
                    <div>
                      <strong>Teléfono:</strong> {order.customer_phone}
                    </div>
                    <div>
                      <strong>Entrega:</strong> {order.delivery_date} (
                      {order.delivery_time_slot})
                    </div>
                    <div>
                      <strong>Total:</strong> $
                      {order.total_cost?.toFixed(2) || '0.00'} MXN
                    </div>
                    <div>
                      <strong>Productos:</strong> {order.products?.length || 0}{' '}
                      artículos
                    </div>
                  </div>

                  {order.stops && order.stops.length > 0 && (
                    <div className='mt-2 text-sm text-blue-600'>
                      🗺️ Multi-parada: {order.stops.length} destinos
                    </div>
                  )}

                  {order.special_instructions && (
                    <div className='mt-2 text-sm text-orange-600'>
                      📝 Instrucciones: {order.special_instructions}
                    </div>
                  )}

                  <div className='flex justify-between items-center mt-4'>
                    <div className='text-sm text-gray-600'>
                      Creado:{' '}
                      {new Date(order.created_at).toLocaleString('es-MX')}
                    </div>
                    <div className='flex space-x-2'>
                      <Button
                        size='sm'
                        variant='outline'
                        onClick={() =>
                          router.push(
                            `/admin/orders/${order.id}/assign-vehicle`
                          )
                        }
                      >
                        Ver Detalles
                      </Button>
                      {!order.vehicle_id && order.status === 'pending' && (
                        <Button
                          size='sm'
                          onClick={() => handleVehicleAssignment(order.id)}
                        >
                          Asignar Vehículo
                        </Button>
                      )}
                      {order.status === 'pending' && (
                        <Button
                          size='sm'
                          onClick={() =>
                            handleStatusUpdate(order.id, 'confirmed')
                          }
                        >
                          Confirmar
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {filteredOrders.length === 0 && (
                <div className='text-center py-8 text-gray-600'>
                  No se encontraron pedidos que coincidan con los filtros
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function AdminOrdersPage() {
  // Middleware guarantees user is authenticated and has admin role
  return <AdminOrdersContent />;
}
