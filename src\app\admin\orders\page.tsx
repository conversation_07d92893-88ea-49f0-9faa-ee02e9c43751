'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';

import { useAuthStore } from '@/stores/authStore';
import { OrdersManagement } from '@/components/admin/orders-management';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { createClient } from '@/utils/supabase/client';
import { OrderTrackingService } from '@/lib/services/order-tracking';
import { VehicleFleetManagementService } from '@/lib/services/vehicle-fleet-management';

interface Order {
  id: string;
  customer_id: string;
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  tracking_number: string;
  status:
    | 'pending'
    | 'confirmed'
    | 'in-transit'
    | 'pending-admin-confirmation'
    | 'delivered'
    | 'closed'
    | 'cancelled';
  pickup_address: {
    street_address: string;
    city: string;
    state: string;
    contact_name: string;
    contact_phone: string;
  };
  delivery_addresses: {
    street_address: string;
    city: string;
    state: string;
    recipient_name: string;
    phone: string;
  };
  package_details: {
    description: string;
    weight: string;
    dimensions: string;
    value: string;
  };
  products: any[];
  stops: any[];
  delivery_date: string;
  delivery_time_slot: string;
  vehicle_id?: string;
  driver_id?: string;
  vehicle_type_id?: string;
  cargo_type_id?: string;
  route_optimization: string;
  delivery_region: string;
  special_instructions?: string;
  total_cost: number;
  payment_status: 'pending' | 'paid' | 'failed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  created_at: string;
  updated_at: string;
}

interface FleetStatus {
  total_vehicles: number;
  available: number;
  assigned: number;
  in_transit: number;
  maintenance: number;
  offline: number;
  by_category: Record<string, number>;
}

function AdminOrdersContent() {
  const router = useRouter();
  const { user, isAdmin } = useAuthStore();
  const [orders, setOrders] = useState<Order[]>([]);
  const [ordersLoading, setOrdersLoading] = useState(true);
  const [fleetStatus, setFleetStatus] = useState<FleetStatus | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);

  // Memoize the Supabase client to prevent recreation on every render
  const supabase = useMemo(() => createClient(), []);

  // Calculate order priority based on various factors
  const calculateOrderPriority = useCallback(
    (order: any): 'low' | 'medium' | 'high' | 'urgent' => {
      const deliveryDate = new Date(order.delivery_date || order.created_at);
      const now = new Date();
      const hoursUntilDelivery =
        (deliveryDate.getTime() - now.getTime()) / (1000 * 60 * 60);

      // Urgent: Same day or overdue
      if (hoursUntilDelivery <= 24) return 'urgent';

      // High: Next day or has special handling requirements
      if (
        hoursUntilDelivery <= 48 ||
        order.products?.some(
          (p: any) =>
            p.special_handling?.perishable ||
            p.special_handling?.hazardous ||
            p.special_handling?.valuable
        )
      ) {
        return 'high';
      }

      // Medium: Within 3 days or high value
      if (hoursUntilDelivery <= 72 || order.total_cost > 10000) return 'medium';

      return 'low';
    },
    []
  );

  const fetchOrders = useCallback(async () => {
    // Middleware guarantees user is authenticated and has admin role
    if (!user) {
      return;
    }

    try {
      // Fetch orders with enhanced data
      let query = supabase
        .from('orders')
        .select(
          `
          *,
          vehicle_fleet (
            id,
            vehicle_types (name)
          ),
          drivers (
            full_name,
            phone
          )
        `
        )
        .order('created_at', { ascending: false });

      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching orders:', error);
        return;
      }

      // Load fleet status
      const fleetResult = await VehicleFleetManagementService.getFleetStatus();
      if (fleetResult.success && fleetResult.data) {
        setFleetStatus(fleetResult.data);
      }

      // Transform data to match the enhanced interface
      const transformedOrders =
        data?.map(order => ({
          id: order.id,
          customer_id: order.customer_id,
          customer_name:
            order.customer_name ||
            order.pickup_address?.contact_name ||
            'Unknown',
          customer_email: order.customer_email || 'No email provided',
          customer_phone:
            order.customer_phone || order.pickup_address?.contact_phone || '',
          tracking_number:
            order.tracking_number || `MX${order.id.slice(-8).toUpperCase()}`,
          status: order.status || 'pending',
          pickup_address: {
            street_address: order.pickup_address?.street || '',
            city: order.pickup_address?.city || '',
            state: order.pickup_address?.state || '',
            contact_name: order.pickup_address?.contact_name || '',
            contact_phone: order.pickup_address?.phone || '',
          },
          delivery_addresses: {
            street_address: order.delivery_addresses?.street || '',
            city: order.delivery_addresses?.city || '',
            state: order.delivery_addresses?.state || '',
            recipient_name: order.delivery_addresses?.contact_name || '',
            phone: order.delivery_addresses?.phone || '',
          },
          package_details: {
            description: order.package_details?.content_description || '',
            weight: order.package_details?.weight || '',
            dimensions: order.package_details?.dimensions || '',
            value: order.total_cost?.toString() || '0',
          },
          products: order.products || [],
          stops: order.stops || [],
          delivery_date: order.delivery_date || order.created_at,
          delivery_time_slot: order.delivery_time_slot || '08:00-11:00',
          vehicle_id: order.vehicle_id,
          driver_id: order.driver_id,
          vehicle_type_id: order.vehicle_type_id,
          cargo_type_id: order.cargo_type_id,
          route_optimization: order.route_optimization || 'balanced',
          delivery_region: order.delivery_region || 'local',
          special_instructions: order.special_instructions,
          priority: calculateOrderPriority(order),
          total_cost: parseFloat(order.total_cost) || 0,
          payment_status: order.payment_status || 'pending',
          created_at: order.created_at,
          updated_at: order.updated_at,
        })) || [];

      setOrders(transformedOrders);
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setOrdersLoading(false);
    }
  }, [supabase, user]);

  // Enhanced order management functions
  const handleVehicleAssignment = useCallback(
    async (orderId: string) => {
      router.push(`/admin/orders/${orderId}/assign-vehicle`);
    },
    [router]
  );

  const handleStatusUpdate = useCallback(
    async (orderId: string, newStatus: string) => {
      try {
        await OrderTrackingService.updateOrderStatus(
          orderId,
          newStatus as any,
          undefined,
          `Estado actualizado por administrador a: ${newStatus}`
        );

        // Reload data
        fetchOrders();
      } catch (error) {
        console.error('Error updating status:', error);
        alert('Error al actualizar el estado');
      }
    },
    [fetchOrders]
  );

  const handleBulkStatusUpdate = useCallback(
    async (newStatus: string) => {
      if (selectedOrders.length === 0) return;

      try {
        await Promise.all(
          selectedOrders.map(orderId =>
            OrderTrackingService.updateOrderStatus(
              orderId,
              newStatus as any,
              undefined,
              `Estado actualizado en lote por administrador`
            )
          )
        );

        setSelectedOrders([]);
        fetchOrders();
      } catch (error) {
        console.error('Error in bulk update:', error);
        alert('Error en la actualización en lote');
      }
    },
    [selectedOrders, fetchOrders]
  );

  // Filter orders based on search and filters
  const filteredOrders = useMemo(() => {
    return orders.filter(order => {
      const matchesSearch =
        !searchQuery ||
        order.tracking_number
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        order.customer_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.customer_phone.includes(searchQuery);

      const matchesPriority =
        priorityFilter === 'all' || order.priority === priorityFilter;

      return matchesSearch && matchesPriority;
    });
  }, [orders, searchQuery, priorityFilter]);

  const getPriorityColor = useCallback((priority: string) => {
    const colors = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800',
    };
    return (
      colors[priority as keyof typeof colors] || 'bg-gray-100 text-gray-800'
    );
  }, []);

  const getStatusColor = useCallback((status: string) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      confirmed: 'bg-green-100 text-green-800',
      'in-transit': 'bg-blue-100 text-blue-800',
      delivered: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  }, []);

  useEffect(() => {
    if (user && isAdmin) {
      fetchOrders();
    }
  }, [user, isAdmin, fetchOrders]);

  const handleUpdateOrderStatus = useCallback(
    async (orderId: string, newStatus: Order['status']) => {
      try {
        // Get the current session to include the access token
        const {
          data: { session },
        } = await supabase.auth.getSession();

        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
        };

        // Include authorization header if we have a session
        if (session?.access_token) {
          headers['Authorization'] = `Bearer ${session.access_token}`;
        }

        const response = await fetch('/api/orders/update-status', {
          method: 'PUT',
          headers,
          body: JSON.stringify({
            orderId,
            newStatus,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('API Error Response:', errorData);
          const errorMessage = errorData.details
            ? `${errorData.error}: ${errorData.details}`
            : errorData.error || 'Failed to update order status';
          throw new Error(errorMessage);
        }

        // Update local state
        setOrders(prev =>
          prev.map(order =>
            order.id === orderId ? { ...order, status: newStatus } : order
          )
        );
      } catch (error) {
        console.error('Error:', error);
        alert(
          error instanceof Error
            ? error.message
            : 'Failed to update order status'
        );
      }
    },
    [supabase.auth]
  );

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Header */}
      <header className='bg-white shadow-sm border-b'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center h-16'>
            <div className='flex items-center space-x-4'>
              <Button
                variant='ghost'
                onClick={() => window.history.back()}
                className='text-blue-600'
              >
                ← Volver al Panel
              </Button>
              <h1 className='text-xl font-bold text-black'>
                Gestión de Pedidos
              </h1>
            </div>
            <div className='flex items-center space-x-4'>
              <span className='text-sm text-gray-600'>
                {user?.email || 'Admin'}
              </span>
              <Badge variant='default'>Admin</Badge>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='max-w-7xl mx-auto py-6 sm:px-6 lg:px-8'>
        <div className='px-4 py-6 sm:px-0'>
          <OrdersManagement
            orders={orders}
            onUpdateOrderStatus={handleUpdateOrderStatus}
            isLoading={ordersLoading}
          />
        </div>
      </main>
    </div>
  );
}

export default function AdminOrdersPage() {
  // Middleware guarantees user is authenticated and has admin role
  return <AdminOrdersContent />;
}
