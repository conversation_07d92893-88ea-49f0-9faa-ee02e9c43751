'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { MultiStopManager } from '@/components/forms/multi-stop/MultiStopManager';
import { OrderFormData } from '@/types/order-form';
import { Stop } from '@/lib/validation/order-schemas';

interface Step5Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
  errors?: Record<string, string>;
}

export function OrderWizardStep5({ formData, updateFormData }: Step5Props) {
  // Convert from OrderFormData Stop to validation Stop
  const convertToValidationStops = (stops: any[]): Stop[] => {
    return stops.map(stop => ({
      id: stop.id,
      order: stop.order || 1,
      recipient_name: stop.recipient_name || '',
      recipient_phone: stop.recipient_phone || '',
      address: stop.address || {
        id: Date.now().toString(),
        street: '',
        number: '',
        colony: '',
        city: '',
        state: 'Ciudad de México',
        zip: '',
        references: '',
      },
      scheduled_time: stop.scheduled_time || '',
      delivery_instructions: stop.delivery_instructions || '',
      products: stop.products || [],
    }));
  };

  // Convert from validation Stop to OrderFormData Stop
  const convertFromValidationStops = (stops: Stop[]): any[] => {
    return stops.map(stop => ({
      id: stop.id,
      order: stop.order,
      recipient_name: stop.recipient_name,
      recipient_phone: stop.recipient_phone,
      address: stop.address,
      scheduled_time: stop.scheduled_time,
      delivery_instructions: stop.delivery_instructions,
      products: stop.products,
    }));
  };

  const deliveryMode =
    formData.stops && formData.stops.length > 0 ? 'multi' : 'single';

  const handleDeliveryModeChange = (mode: 'single' | 'multi') => {
    if (mode === 'single') {
      updateFormData({ stops: [] });
    } else if (
      mode === 'multi' &&
      (!formData.stops || formData.stops.length === 0)
    ) {
      // Initialize with one stop if switching to multi-stop mode
      const initialStop: any = {
        id: Date.now().toString(),
        order: 1,
        recipient_name: formData.customer_name || '',
        recipient_phone: formData.customer_phone || '',
        address: formData.delivery_address,
        scheduled_time: '',
        delivery_instructions: '',
        products: [],
      };
      updateFormData({ stops: [initialStop] });
    }
  };

  const handleStopsChange = (stops: Stop[]) => {
    const convertedStops = convertFromValidationStops(stops);
    updateFormData({ stops: convertedStops });
  };

  const validationStops = formData.stops
    ? convertToValidationStops(formData.stops)
    : [];

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          🗺️ Gestión de Entregas
        </CardTitle>
        <CardDescription>
          Configura entregas simples o múltiples paradas con optimización de
          ruta
        </CardDescription>
      </CardHeader>
      <CardContent>
        <MultiStopManager
          stops={validationStops}
          onStopsChange={handleStopsChange}
          deliveryMode={deliveryMode}
          onDeliveryModeChange={handleDeliveryModeChange}
        />
      </CardContent>
    </Card>
  );
}
