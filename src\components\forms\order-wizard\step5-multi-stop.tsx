'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { MultiStopManager } from '@/components/forms/multi-stop/MultiStopManager';
import { OrderFormData } from '@/types/order-form';

interface Step5Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
}

export function OrderWizardStep5({ formData, updateFormData }: Step5Props) {
  const deliveryMode = formData.stops && formData.stops.length > 0 ? 'multi' : 'single';

  const handleDeliveryModeChange = (mode: 'single' | 'multi') => {
    if (mode === 'single') {
      updateFormData({ stops: [] });
    } else if (mode === 'multi' && (!formData.stops || formData.stops.length === 0)) {
      // Initialize with one stop if switching to multi-stop mode
      const initialStop = {
        id: Date.now().toString(),
        order: 1,
        recipient_name: formData.customer_name || '',
        recipient_phone: formData.customer_phone || '',
        address: formData.delivery_address,
        scheduled_time: '',
        delivery_instructions: '',
        products: [],
      };
      updateFormData({ stops: [initialStop] });
    }
  };

  const handleStopsChange = (stops: any[]) => {
    updateFormData({ stops });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          🗺️ Gestión de Entregas
        </CardTitle>
        <CardDescription>
          Configura entregas simples o múltiples paradas con optimización de ruta
        </CardDescription>
      </CardHeader>
      <CardContent>
        <MultiStopManager
          stops={formData.stops || []}
          onStopsChange={handleStopsChange}
          deliveryMode={deliveryMode}
          onDeliveryModeChange={handleDeliveryModeChange}
        />
      </CardContent>
    </Card>
  );
}
