'use client';

import { useState, useEffect } from 'react';

interface VehicleImageProps {
  vehicleTypeId?: string;
  vehicleCategory?: string;
  className?: string;
  alt?: string;
}

export function VehicleImage({
  vehicleTypeId,
  vehicleCategory,
  className = '',
  alt = 'Vehículo',
}: VehicleImageProps) {
  const [imageSrc, setImageSrc] = useState('/vehicules/car.jpg'); // Default fallback
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  // Map vehicle categories to image names
  const getCategoryImageName = (category: string) => {
    const categoryMap: Record<string, string> = {
      motorcycle: 'moto',
      motocicleta: 'moto',
      small: 'car',
      pequeño: 'car',
      medium: 'van',
      mediano: 'van',
      large: 'truck',
      grande: 'truck',
      truck: 'truck',
      camion: 'truck',
      van: 'van',
      camioneta: 'van',
      rabon: 'rabon',
      torton: 'truck',
    };

    return categoryMap[category?.toLowerCase()] || 'car';
  };

  // Map vehicle type IDs to image names (this would be enhanced with real data)
  const getTypeImageName = (typeId: string) => {
    const typeMap: Record<string, string> = {
      '1': 'moto',
      '2': 'car',
      '3': 'van',
      '4': 'rabon',
      '5': 'truck',
    };

    return typeMap[typeId] || 'car';
  };

  useEffect(() => {
    let imageName = 'car'; // Default

    if (vehicleTypeId) {
      imageName = getTypeImageName(vehicleTypeId);
    } else if (vehicleCategory) {
      imageName = getCategoryImageName(vehicleCategory);
    }

    const src = `/vehicules/${imageName}.jpg`;
    setImageSrc(src);
    setLoading(true);
    setError(false);
  }, [vehicleTypeId, vehicleCategory]);

  const handleImageLoad = () => {
    setLoading(false);
  };

  const handleImageError = () => {
    setLoading(false);
    setError(true);
    // Set fallback image
    setImageSrc('/vehicules/car.jpg');
  };

  // Show loading state
  if (loading) {
    return (
      <div
        className={`bg-gray-200 animate-pulse flex items-center justify-center ${className}`}
      >
        <div className='text-gray-500 text-xs'>Cargando...</div>
      </div>
    );
  }

  // Show error state with fallback
  if (error) {
    return (
      <div
        className={`bg-gray-100 flex items-center justify-center border ${className}`}
      >
        <div className='text-gray-500 text-xs'>Imagen no disponible</div>
      </div>
    );
  }

  return (
    <img
      src={imageSrc}
      alt={alt}
      className={className}
      onLoad={handleImageLoad}
      onError={handleImageError}
    />
  );
}
