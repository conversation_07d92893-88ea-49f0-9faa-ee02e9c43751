/**
 * JWT Validation Utilities
 *
 * Implements Supabase's recommended JWT validation and signing key rotation
 * Reference: https://supabase.com/docs/guides/auth/signing-keys
 */

import { createServerClient } from '@supabase/ssr';
import { jwtVerify, importJWK } from 'jose';
import type { Database } from '@/lib/supabase';
import { NextRequest } from 'next/server';

interface JWTClaims {
  sub: string;
  email?: string;
  role?: string;
  app_metadata?: {
    role?: string;
    [key: string]: unknown;
  };
  user_metadata?: {
    [key: string]: unknown;
  };
  aud: string;
  exp: number;
  iat: number;
  iss: string;
}

interface SupabaseUser {
  id: string;
  email?: string;
  app_metadata?: {
    role?: string;
    [key: string]: unknown;
  };
  user_metadata?: {
    [key: string]: unknown;
  };
}

interface SupabaseJWK {
  kty: string;
  use: string;
  kid: string;
  x5t: string;
  n: string;
  e: string;
  x5c: string[];
}

/**
 * JWT Validation Manager
 * Handles JWT validation with proper signing key verification
 */
export class JWTValidator {
  private jwksCache = new Map<string, SupabaseJWK>();
  private jwksCacheExpiry = new Map<string, number>();
  private readonly CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours

  /**
   * Validate JWT token using Supabase's signing keys
   * @param token JWT token to validate
   * @param audience Expected audience (usually 'authenticated')
   */
  async validateJWT(
    token: string,
    audience: string = 'authenticated'
  ): Promise<{
    valid: boolean;
    claims?: JWTClaims;
    error?: string;
  }> {
    try {
      // Get JWT header to extract key ID
      const [headerB64] = token.split('.');
      const header = JSON.parse(Buffer.from(headerB64, 'base64url').toString());
      const kid = header.kid;

      if (!kid) {
        return { valid: false, error: 'Missing key ID in JWT header' };
      }

      // Get signing key
      const jwk = await this.getSigningKey(kid);
      if (!jwk) {
        return { valid: false, error: 'Unable to find matching signing key' };
      }

      // Import JWK for verification
      const key = await importJWK(jwk);

      // Verify JWT
      const { payload } = await jwtVerify(token, key, {
        issuer: `https://${process.env.NEXT_PUBLIC_SUPABASE_URL?.replace('https://', '')}/auth/v1`,
        audience,
      });

      return {
        valid: true,
        claims: payload as JWTClaims,
      };
    } catch (error) {
      console.error('JWT validation error:', error);
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'JWT validation failed',
      };
    }
  }

  /**
   * Get signing key from Supabase JWKS endpoint
   * @param kid Key ID
   */
  private async getSigningKey(kid: string): Promise<SupabaseJWK | null> {
    // Check cache first
    const cached = this.jwksCache.get(kid);
    const cacheExpiry = this.jwksCacheExpiry.get(kid);

    if (cached && cacheExpiry && Date.now() < cacheExpiry) {
      return cached;
    }

    try {
      // Fetch JWKS from Supabase
      const jwksUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/auth/v1/jwks`;
      const response = await fetch(jwksUrl);

      if (!response.ok) {
        throw new Error(`Failed to fetch JWKS: ${response.status}`);
      }

      const jwks = await response.json();
      const key = jwks.keys.find((k: SupabaseJWK) => k.kid === kid);

      if (key) {
        // Cache the key
        this.jwksCache.set(kid, key);
        this.jwksCacheExpiry.set(kid, Date.now() + this.CACHE_TTL);
        return key;
      }

      return null;
    } catch (error) {
      console.error('Error fetching signing key:', error);
      return null;
    }
  }

  /**
   * Validate JWT using Supabase client (recommended approach)
   * @param request Next.js request object
   */
  async validateWithSupabase(request: NextRequest): Promise<{
    valid: boolean;
    user?: SupabaseUser;
    session?: unknown;
    error?: string;
  }> {
    try {
      const supabase = createServerClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            getAll() {
              return request.cookies.getAll();
            },
            setAll() {
              // Read-only in middleware
            },
          },
        }
      );

      // Use getClaims() for JWT validation (recommended by Supabase)
      const { data: claims, error: claimsError } =
        await supabase.auth.getClaims();

      if (claimsError || !claims) {
        return {
          valid: false,
          error: claimsError?.message || 'No valid JWT claims found',
        };
      }

      // Get user data if needed
      const { data: userData, error: userError } =
        await supabase.auth.getUser();

      if (userError) {
        return {
          valid: false,
          error: userError.message,
        };
      }

      // Get session data
      const { data: sessionData } = await supabase.auth.getSession();

      return {
        valid: true,
        user: userData.user,
        session: sessionData.session,
      };
    } catch (error) {
      console.error('Supabase JWT validation error:', error);
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'JWT validation failed',
      };
    }
  }

  /**
   * Extract JWT from Authorization header
   * @param authHeader Authorization header value
   */
  extractJWTFromHeader(authHeader: string | null): string | null {
    if (!authHeader) return null;

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1];
  }

  /**
   * Check if JWT is expired
   * @param claims JWT claims
   * @param bufferSeconds Buffer time in seconds (default: 300 = 5 minutes)
   */
  isJWTExpired(claims: JWTClaims, bufferSeconds: number = 300): boolean {
    const now = Math.floor(Date.now() / 1000);
    return claims.exp <= now + bufferSeconds;
  }

  /**
   * Get user role from JWT claims
   * @param claims JWT claims
   */
  getUserRole(claims: JWTClaims): string | null {
    return claims.role || claims.app_metadata?.role || null;
  }

  /**
   * Check if user has required role
   * @param claims JWT claims
   * @param requiredRole Required role
   */
  hasRole(claims: JWTClaims, requiredRole: string): boolean {
    const userRole = this.getUserRole(claims);
    return userRole === requiredRole;
  }

  /**
   * Check if user has any of the required roles
   * @param claims JWT claims
   * @param requiredRoles Array of required roles
   */
  hasAnyRole(claims: JWTClaims, requiredRoles: string[]): boolean {
    const userRole = this.getUserRole(claims);
    return userRole ? requiredRoles.includes(userRole) : false;
  }

  /**
   * Clear JWKS cache (useful for key rotation)
   */
  clearJWKSCache(): void {
    this.jwksCache.clear();
    this.jwksCacheExpiry.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    totalKeys: number;
    expiredKeys: number;
    validKeys: number;
  } {
    const now = Date.now();
    let expiredKeys = 0;
    let validKeys = 0;

    for (const [, expiry] of this.jwksCacheExpiry.entries()) {
      if (expiry < now) {
        expiredKeys++;
      } else {
        validKeys++;
      }
    }

    return {
      totalKeys: this.jwksCache.size,
      expiredKeys,
      validKeys,
    };
  }
}

// Export singleton instance
export const jwtValidator = new JWTValidator();

/**
 * Utility function for quick JWT validation in API routes
 * @param request Next.js request object
 */
export async function validateRequestJWT(request: NextRequest): Promise<{
  valid: boolean;
  user?: SupabaseUser;
  claims?: Partial<JWTClaims>;
  error?: string;
}> {
  return await jwtValidator.validateWithSupabase(request);
}

/**
 * Middleware helper for JWT validation
 * @param request Next.js request object
 */
export async function validateMiddlewareJWT(request: NextRequest): Promise<{
  valid: boolean;
  claims?: Partial<JWTClaims>;
  user?: SupabaseUser;
  error?: string;
}> {
  const result = await jwtValidator.validateWithSupabase(request);
  return {
    valid: result.valid,
    claims: result.user
      ? {
          sub: result.user.id,
          email: result.user.email,
          role: result.user.app_metadata?.role,
          app_metadata: result.user.app_metadata,
          user_metadata: result.user.user_metadata,
        }
      : undefined,
    user: result.user,
    error: result.error,
  };
}
