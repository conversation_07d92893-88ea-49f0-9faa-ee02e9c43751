/**
 * Mock Data Seeder for Mexican Logistics System
 *
 * Provides comprehensive test data for validating the complete order flow
 */

import { MEXICAN_VEHICLE_TYPES, MEXICAN_CARGO_TYPES } from './mexican-vehicles';
import { ProductItem, Address, Stop } from '../validation/order-schemas';

export interface MockOrder {
  id: string;
  customer_name: string;
  customer_phone: string;
  customer_email?: string;
  delivery_address: Address;
  products: ProductItem[];
  vehicle_type_id?: string;
  cargo_type_id?: string;
  delivery_date: string;
  delivery_time_slot: string;
  payment_method: 'card' | 'cash' | 'digital_wallet' | 'bank_transfer';
  special_instructions?: string;
  stops?: Stop[];
  total_cost: number;
  status: 'pending' | 'confirmed' | 'in_transit' | 'delivered' | 'cancelled';
}

export class MockDataSeeder {
  // Sample Mexican addresses across different states
  static readonly SAMPLE_ADDRESSES: Address[] = [
    {
      id: '1',
      street: 'Av. Insurgentes Sur',
      number: '1234',
      colony: 'Roma Norte',
      city: 'Ciudad de México',
      state: 'Ciudad de México',
      zip: '06700',
      references: '<PERSON><PERSON> Durango',
      coordinates: { lat: 19.4326, lng: -99.1332 },
    },
    {
      id: '2',
      street: 'Av. Chapultepec',
      number: '890',
      colony: 'Americana',
      city: 'Guadalajara',
      state: 'Jalisco',
      zip: '44160',
      references: 'Frente al parque',
      coordinates: { lat: 20.6597, lng: -103.3496 },
    },
    {
      id: '3',
      street: 'Av. Constitución',
      number: '234',
      colony: 'Centro',
      city: 'Monterrey',
      state: 'Nuevo León',
      zip: '64000',
      references: 'Edificio azul, planta baja',
      coordinates: { lat: 25.6866, lng: -100.3161 },
    },
    {
      id: '4',
      street: 'Calle 5 de Mayo',
      number: '345',
      colony: 'Centro Histórico',
      city: 'Puebla',
      state: 'Puebla',
      zip: '72000',
      references: 'Cerca de la catedral',
      coordinates: { lat: 19.0414, lng: -98.2063 },
    },
    {
      id: '5',
      street: 'Av. Juárez',
      number: '567',
      colony: 'Zona Centro',
      city: 'Tijuana',
      state: 'Baja California',
      zip: '22000',
      references: 'Plaza comercial',
      coordinates: { lat: 32.5149, lng: -117.0382 },
    },
  ];

  // Sample products with realistic Mexican items
  static readonly SAMPLE_PRODUCTS: Omit<ProductItem, 'id' | 'subtotal'>[] = [
    {
      name: 'Televisión LED 55" Samsung',
      quantity: 1,
      unit_measure: 'pieza',
      unit_price: 12500.0,
      weight: 18.5,
      weight_unit: 'kg',
      dimensions: { length: 123, width: 71, height: 8, unit: 'cm' },
      special_handling: {
        fragile: true,
        valuable: true,
        perishable: false,
        hazardous: false,
        refrigerated: false,
        oversized: false,
      },
      notes: 'Manejar con extremo cuidado, embalaje original',
    },
    {
      name: 'Refrigerador LG 400L',
      quantity: 1,
      unit_measure: 'pieza',
      unit_price: 18900.0,
      weight: 85.0,
      weight_unit: 'kg',
      dimensions: { length: 60, width: 65, height: 170, unit: 'cm' },
      special_handling: {
        fragile: true,
        valuable: true,
        perishable: false,
        hazardous: false,
        refrigerated: false,
        oversized: true,
      },
      notes: 'Requiere montacargas, instalación incluida',
    },
    {
      name: 'Caja de Mangos Ataulfo',
      quantity: 10,
      unit_measure: 'caja',
      unit_price: 180.0,
      weight: 4.5,
      weight_unit: 'kg',
      dimensions: { length: 40, width: 30, height: 15, unit: 'cm' },
      special_handling: {
        fragile: false,
        valuable: false,
        perishable: true,
        hazardous: false,
        refrigerated: true,
        oversized: false,
      },
      notes: 'Mantener refrigerado, entrega máximo 24 horas',
    },
    {
      name: 'Laptop Dell Inspiron 15',
      quantity: 2,
      unit_measure: 'pieza',
      unit_price: 15800.0,
      weight: 2.1,
      weight_unit: 'kg',
      dimensions: { length: 36, width: 25, height: 2, unit: 'cm' },
      special_handling: {
        fragile: true,
        valuable: true,
        perishable: false,
        hazardous: false,
        refrigerated: false,
        oversized: false,
      },
      notes: 'Embalaje anti-estático, seguro contra robo',
    },
    {
      name: 'Cemento Portland 50kg',
      quantity: 20,
      unit_measure: 'bulto',
      unit_price: 185.0,
      weight: 50.0,
      weight_unit: 'kg',
      dimensions: { length: 60, width: 40, height: 10, unit: 'cm' },
      special_handling: {
        fragile: false,
        valuable: false,
        perishable: false,
        hazardous: false,
        refrigerated: false,
        oversized: false,
      },
      notes: 'Mantener seco, entrega en obra',
    },
    {
      name: 'Medicamentos Controlados',
      quantity: 5,
      unit_measure: 'caja',
      unit_price: 450.0,
      weight: 0.5,
      weight_unit: 'kg',
      dimensions: { length: 15, width: 10, height: 5, unit: 'cm' },
      special_handling: {
        fragile: true,
        valuable: true,
        perishable: true,
        hazardous: false,
        refrigerated: true,
        oversized: false,
      },
      notes: 'Cadena de frío, requiere firma del destinatario',
    },
    {
      name: 'Tanque de Gas LP 30kg',
      quantity: 3,
      unit_measure: 'pieza',
      unit_price: 680.0,
      weight: 30.0,
      weight_unit: 'kg',
      dimensions: { length: 35, width: 35, height: 60, unit: 'cm' },
      special_handling: {
        fragile: false,
        valuable: false,
        perishable: false,
        hazardous: true,
        refrigerated: false,
        oversized: false,
      },
      notes: 'Material peligroso, requiere permisos especiales',
    },
    {
      name: 'Muebles de Sala (Juego completo)',
      quantity: 1,
      unit_measure: 'unidad',
      unit_price: 25000.0,
      weight: 150.0,
      weight_unit: 'kg',
      dimensions: { length: 300, width: 200, height: 100, unit: 'cm' },
      special_handling: {
        fragile: true,
        valuable: true,
        perishable: false,
        hazardous: false,
        refrigerated: false,
        oversized: true,
      },
      notes: 'Requiere desensamble y armado, incluye instalación',
    },
  ];

  // Generate realistic mock orders
  static generateMockOrders(count: number = 10): MockOrder[] {
    const orders: MockOrder[] = [];
    const customerNames = [
      'María González López',
      'Juan Carlos Rodríguez',
      'Ana Patricia Martínez',
      'Luis Fernando Hernández',
      'Carmen Rosa Jiménez',
      'Roberto Carlos Pérez',
      'Sofía Elena Ramírez',
      'Miguel Ángel Torres',
      'Gabriela Morales Castro',
      'Diego Alejandro Vargas',
    ];

    const phoneNumbers = [
      '+52 55 1234 5678',
      '+52 33 2345 6789',
      '+52 81 3456 7890',
      '+52 ************',
      '+52 ************',
      '+52 55 6789 1234',
      '+52 33 7891 2345',
      '+52 81 8912 3456',
      '+52 ************',
      '+52 ************',
    ];

    for (let i = 0; i < count; i++) {
      const customerIndex = i % customerNames.length;
      const addressIndex = i % this.SAMPLE_ADDRESSES.length;
      const productCount = Math.floor(Math.random() * 3) + 1; // 1-3 products per order

      const selectedProducts: ProductItem[] = [];
      for (let j = 0; j < productCount; j++) {
        const productTemplate =
          this.SAMPLE_PRODUCTS[
            Math.floor(Math.random() * this.SAMPLE_PRODUCTS.length)
          ];
        const product: ProductItem = {
          ...productTemplate,
          id: `${i}-${j}`,
          subtotal: productTemplate.unit_price * productTemplate.quantity,
        };
        selectedProducts.push(product);
      }

      const totalCost = selectedProducts.reduce(
        (sum, product) => sum + product.subtotal,
        0
      );

      // Generate delivery date (next 7 days)
      const deliveryDate = new Date();
      deliveryDate.setDate(
        deliveryDate.getDate() + Math.floor(Math.random() * 7) + 1
      );

      const timeSlots = [
        '08:00-11:00',
        '11:00-14:00',
        '14:00-17:00',
        '17:00-20:00',
      ];
      const paymentMethods: (
        | 'card'
        | 'cash'
        | 'digital_wallet'
        | 'bank_transfer'
      )[] = ['card', 'cash', 'digital_wallet', 'bank_transfer'];
      const statuses: (
        | 'pending'
        | 'confirmed'
        | 'in_transit'
        | 'delivered'
        | 'cancelled'
      )[] = ['pending', 'confirmed', 'in_transit', 'delivered'];

      const order: MockOrder = {
        id: `order-${i + 1}`,
        customer_name: customerNames[customerIndex],
        customer_phone: phoneNumbers[customerIndex],
        customer_email: `${customerNames[customerIndex].toLowerCase().replace(/\s+/g, '.')}@email.com`,
        delivery_address: this.SAMPLE_ADDRESSES[addressIndex],
        products: selectedProducts,
        vehicle_type_id:
          MEXICAN_VEHICLE_TYPES[
            Math.floor(Math.random() * MEXICAN_VEHICLE_TYPES.length)
          ].id,
        cargo_type_id:
          MEXICAN_CARGO_TYPES[
            Math.floor(Math.random() * MEXICAN_CARGO_TYPES.length)
          ].id,
        delivery_date: deliveryDate.toISOString().split('T')[0],
        delivery_time_slot:
          timeSlots[Math.floor(Math.random() * timeSlots.length)],
        payment_method:
          paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
        special_instructions:
          Math.random() > 0.5
            ? 'Llamar antes de llegar, no tocar timbre después de las 8pm'
            : undefined,
        total_cost: totalCost,
        status: statuses[Math.floor(Math.random() * statuses.length)],
      };

      // Add multi-stop for some orders
      if (Math.random() > 0.7) {
        order.stops = this.generateMultiStops(
          2 + Math.floor(Math.random() * 2)
        ); // 2-3 stops
      }

      orders.push(order);
    }

    return orders;
  }

  // Generate multi-stop data
  static generateMultiStops(count: number): Stop[] {
    const stops: Stop[] = [];
    const recipientNames = [
      'Pedro Sánchez',
      'Laura Mendoza',
      'Carlos Ruiz',
      'Elena Vásquez',
      'Javier Moreno',
    ];

    for (let i = 0; i < count; i++) {
      const addressIndex = i % this.SAMPLE_ADDRESSES.length;
      const recipientIndex = i % recipientNames.length;

      stops.push({
        id: `stop-${i + 1}`,
        order: i + 1,
        recipient_name: recipientNames[recipientIndex],
        recipient_phone: `+52 55 ${Math.floor(Math.random() * 9000) + 1000} ${Math.floor(Math.random() * 9000) + 1000}`,
        address: this.SAMPLE_ADDRESSES[addressIndex],
        delivery_instructions: `Parada ${i + 1}: ${Math.random() > 0.5 ? 'Llamar al llegar' : 'Dejar con portería'}`,
        products: [],
      });
    }

    return stops;
  }

  // Get sample data for testing specific scenarios
  static getTestScenarios() {
    return {
      // Light delivery scenario
      lightDelivery: {
        products: [this.SAMPLE_PRODUCTS[3]], // Laptop
        expectedVehicle: 'moto-delivery',
        expectedCost: 'low',
      },

      // Heavy delivery scenario
      heavyDelivery: {
        products: [this.SAMPLE_PRODUCTS[1]], // Refrigerator
        expectedVehicle: 'torton-standard',
        expectedCost: 'high',
      },

      // Perishable delivery scenario
      perishableDelivery: {
        products: [this.SAMPLE_PRODUCTS[2]], // Mangos
        expectedVehicle: 'refrigerated-truck',
        expectedCost: 'medium',
      },

      // Hazardous delivery scenario
      hazardousDelivery: {
        products: [this.SAMPLE_PRODUCTS[6]], // Gas tank
        expectedVehicle: 'rabon-single',
        expectedCost: 'high',
      },

      // Multi-stop scenario
      multiStopDelivery: {
        products: [this.SAMPLE_PRODUCTS[0], this.SAMPLE_PRODUCTS[3]], // TV + Laptop
        stops: this.generateMultiStops(3),
        expectedVehicle: 'van-panel',
        expectedCost: 'high',
      },
    };
  }

  // Validate order data completeness
  static validateOrderData(order: MockOrder): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!order.customer_name) errors.push('Customer name is required');
    if (!order.customer_phone) errors.push('Customer phone is required');
    if (!order.delivery_address.street)
      errors.push('Delivery street is required');
    if (!order.delivery_address.city) errors.push('Delivery city is required');
    if (!order.delivery_address.state)
      errors.push('Delivery state is required');
    if (!order.delivery_address.zip)
      errors.push('Delivery zip code is required');
    if (order.products.length === 0)
      errors.push('At least one product is required');
    if (!order.delivery_date) errors.push('Delivery date is required');
    if (!order.payment_method) errors.push('Payment method is required');

    // Validate products
    order.products.forEach((product, index) => {
      if (!product.name) errors.push(`Product ${index + 1}: name is required`);
      if (product.quantity <= 0)
        errors.push(`Product ${index + 1}: quantity must be greater than 0`);
      if (product.unit_price < 0)
        errors.push(`Product ${index + 1}: unit price cannot be negative`);
      if (product.weight <= 0)
        errors.push(`Product ${index + 1}: weight must be greater than 0`);
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
