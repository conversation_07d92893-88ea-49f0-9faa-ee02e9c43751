'use client';

import { useState } from 'react';
import LoginForm from './LoginForm';
import RegisterForm from './RegisterForm';

export default function AuthContainer() {
  const [isLogin, setIsLogin] = useState(true);

  return (
    <div
      className='min-h-screen flex items-center justify-center p-2 sm:p-4 relative overflow-hidden'
      style={{
        backgroundImage: 'url(/back.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }}
    >
      {/* Overlay para mejorar legibilidad */}
      <div className='absolute inset-0 bg-black/40'></div>

      <div className='w-full max-w-sm sm:max-w-md px-4 sm:px-0 space-y-6 sm:space-y-8 relative z-10'>
        <div className='text-center'>
          <h1 className='text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-2 sm:mb-3 drop-shadow-lg'>
            Mouvers
          </h1>
          <h2 className='text-lg sm:text-xl md:text-2xl font-semibold text-blue-300 mb-2 drop-shadow-lg'>
            Repartidor
          </h2>
          <p className='text-gray-200 text-sm sm:text-base md:text-lg px-2'>
            Accede a tu cuenta para gestionar entregas
          </p>
        </div>

        <div className='bg-white/10 backdrop-blur-md rounded-xl sm:rounded-2xl p-4 sm:p-6 md:p-8 border border-white/20 shadow-2xl'>
          {isLogin ? <LoginForm /> : <RegisterForm />}

          <div className='mt-4 sm:mt-6 text-center'>
            <button
              onClick={() => setIsLogin(!isLogin)}
              className='text-blue-300 hover:text-blue-200 transition-colors font-medium text-sm sm:text-base'
            >
              {isLogin
                ? '¿No tienes cuenta? Regístrate aquí'
                : '¿Ya tienes cuenta? Inicia sesión aquí'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
