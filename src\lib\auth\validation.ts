/**
 * Authentication Validation Schemas using Zod
 *
 * Provides type-safe validation for authentication flows with role-based access control
 */

import { z } from 'zod';

// User roles enum
export const UserRole = z.enum(['admin', 'customer', 'delivery']);
export type UserRole = z.infer<typeof UserRole>;

// Login request validation
export const LoginRequestSchema = z.object({
  email: z
    .string()
    .email('Formato de email inválido')
    .min(1, 'El email es requerido')
    .max(255, 'El email es demasiado largo'),
  password: z
    .string()
    .min(6, 'La contraseña debe tener al menos 6 caracteres')
    .max(128, 'La contraseña es demasiado larga'),
  requiredRole: UserRole.optional(),
});

export type LoginRequest = z.infer<typeof LoginRequestSchema>;

// User profile validation
export const UserProfileSchema = z.object({
  id: z.string().uuid('ID de usuario inválido'),
  email: z.string().email('Email inválido'),
  role: UserRole,
  full_name: z.string().nullable(),
  phone: z.string().nullable(),
  is_active: z.boolean().default(true),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

export type UserProfile = z.infer<typeof UserProfileSchema>;

// Simple role validation schema for signIn method
export const RoleOnlyProfileSchema = z.object({
  role: UserRole,
});

export type RoleOnlyProfile = z.infer<typeof RoleOnlyProfileSchema>;

// Role validation schemas
export const DeliveryLoginSchema = LoginRequestSchema.extend({
  requiredRole: z.literal('delivery'),
});

export const MainAppLoginSchema = LoginRequestSchema.extend({
  requiredRole: z.union([z.literal('admin'), z.literal('customer')]).optional(),
});

// Registration validation
export const RegistrationRequestSchema = z
  .object({
    email: z
      .string()
      .email('Formato de email inválido')
      .min(1, 'El email es requerido')
      .max(255, 'El email es demasiado largo'),
    password: z
      .string()
      .min(6, 'La contraseña debe tener al menos 6 caracteres')
      .max(128, 'La contraseña es demasiado larga')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'La contraseña debe contener al menos una minúscula, una mayúscula y un número'
      ),
    confirmPassword: z.string(),
    full_name: z
      .string()
      .min(2, 'El nombre debe tener al menos 2 caracteres')
      .max(100, 'El nombre es demasiado largo')
      .optional(),
    phone: z
      .string()
      .regex(/^\+?[\d\s\-\(\)]+$/, 'Formato de teléfono inválido')
      .min(10, 'El teléfono debe tener al menos 10 dígitos')
      .max(20, 'El teléfono es demasiado largo')
      .optional(),
    metadata: z.record(z.string(), z.string()).optional(),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: 'Las contraseñas no coinciden',
    path: ['confirmPassword'],
  });

export type RegistrationRequest = z.infer<typeof RegistrationRequestSchema>;

// Profile completion validation
export const ProfileCompletionSchema = z.object({
  full_name: z
    .string()
    .min(2, 'El nombre debe tener al menos 2 caracteres')
    .max(100, 'El nombre es demasiado largo'),
  phone: z
    .string()
    .regex(/^\+?[\d\s\-\(\)]+$/, 'Formato de teléfono inválido')
    .min(10, 'El teléfono debe tener al menos 10 dígitos')
    .max(20, 'El teléfono es demasiado largo'),
  currentRole: UserRole.optional(),
  targetRole: UserRole.optional(),
});

export type ProfileCompletion = z.infer<typeof ProfileCompletionSchema>;

// Role access validation
export const RoleAccessSchema = z.object({
  userRole: UserRole,
  requiredRole: UserRole.optional(),
  path: z.string(),
});

export type RoleAccess = z.infer<typeof RoleAccessSchema>;

// Authentication response validation
export const AuthResponseSchema = z.object({
  user: z
    .object({
      id: z.string().uuid(),
      email: z.string().email(),
      created_at: z.string().datetime(),
    })
    .nullable(),
  session: z
    .object({
      access_token: z.string(),
      refresh_token: z.string(),
      expires_at: z.number(),
    })
    .nullable(),
  error: z
    .object({
      message: z.string(),
      status: z.number().optional(),
    })
    .nullable(),
});

export type AuthResponse = z.infer<typeof AuthResponseSchema>;

// Validation helper functions
export function validateLogin(data: unknown): LoginRequest {
  return LoginRequestSchema.parse(data);
}

export function validateDeliveryLogin(
  data: unknown
): z.infer<typeof DeliveryLoginSchema> {
  return DeliveryLoginSchema.parse(data);
}

export function validateMainAppLogin(
  data: unknown
): z.infer<typeof MainAppLoginSchema> {
  return MainAppLoginSchema.parse(data);
}

export function validateRegistration(data: unknown): RegistrationRequest {
  return RegistrationRequestSchema.parse(data);
}

export function validateProfileCompletion(data: unknown): ProfileCompletion {
  return ProfileCompletionSchema.parse(data);
}

export function validateUserProfile(data: unknown): UserProfile {
  return UserProfileSchema.parse(data);
}

export function validateRoleAccess(data: unknown): RoleAccess {
  return RoleAccessSchema.parse(data);
}

// Role-based access control validation
export function canAccessDeliverySystem(userRole: UserRole): boolean {
  return userRole === 'delivery';
}

export function canAccessMainApp(userRole: UserRole): boolean {
  return userRole === 'admin' || userRole === 'customer';
}

export function canAccessAdminPanel(userRole: UserRole): boolean {
  return userRole === 'admin';
}

export function canChangeRoleToDelivery(
  currentRole: UserRole | null | undefined
): boolean {
  // Only allow role change to delivery if:
  // 1. User doesn't have a role yet (new user)
  // 2. User's current role is 'customer' (customer upgrading to delivery)
  // 3. Never allow admin users to be changed to delivery role
  return !currentRole || currentRole === 'customer';
}

// Error messages for role validation
export const RoleValidationErrors = {
  DELIVERY_ONLY:
    'Este login es solo para repartidores. Los administradores y clientes deben usar el login principal.',
  MAIN_APP_ONLY:
    'Los repartidores deben usar el sistema de login de repartidores.',
  ADMIN_ONLY: 'Solo los administradores pueden acceder a esta sección.',
  INVALID_ROLE_CHANGE:
    'Los administradores no pueden registrarse como repartidores.',
  ROLE_VERIFICATION_FAILED: 'No se pudo verificar el rol del usuario.',
  UNAUTHORIZED_ACCESS: 'No tienes permisos para acceder a esta sección.',
} as const;

// Validation result types
export type ValidationResult<T> =
  | {
      success: true;
      data: T;
    }
  | {
      success: false;
      error: {
        message: string;
        field?: string;
        code?: string;
      };
    };

// Safe validation wrapper
export function safeValidate<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): ValidationResult<T> {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.issues[0];
      return {
        success: false,
        error: {
          message: firstError.message,
          field: firstError.path.join('.'),
          code: firstError.code,
        },
      };
    }
    return {
      success: false,
      error: {
        message: 'Error de validación desconocido',
      },
    };
  }
}

// Note: Schemas are already exported above, no need to re-export
