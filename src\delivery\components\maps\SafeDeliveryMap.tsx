'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { geocodeAddress, GeocodingResult } from '@/delivery/lib/geocoding';

// Importar DeliveryMap dinámicamente solo en el cliente
const DeliveryMap = dynamic(() => import('./DeliveryMap'), {
  ssr: false,
  loading: () => (
    <div className='w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center'>
      <div className='text-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2'></div>
        <p className='text-sm text-gray-600'>Cargando mapa...</p>
      </div>
    </div>
  ),
});

interface SafeDeliveryMapProps {
  pickupAddress?:
    | {
        street?: string;
        number?: string;
        city?: string;
        state?: string;
        zip?: string;
        lat?: number;
        lng?: number;
        name?: string;
        coordinates?: { lat: number; lng: number } | [number, number];
      }
    | string;
  deliveryAddress?:
    | {
        street?: string;
        number?: string;
        city?: string;
        state?: string;
        zip?: string;
        lat?: number;
        lng?: number;
        name?: string;
        coordinates?: { lat: number; lng: number } | [number, number];
      }
    | string;
  className?: string;
}

export default function SafeDeliveryMap({
  pickupAddress,
  deliveryAddress,
  className,
}: SafeDeliveryMapProps) {
  const [canRender, setCanRender] = useState(false);
  const [geocodedPickup, setGeocodedPickup] = useState<GeocodingResult | null>(
    null
  );
  const [geocodedDelivery, setGeocodedDelivery] =
    useState<GeocodingResult | null>(null);
  const [isGeocoding, setIsGeocoding] = useState(false);
  const [geocodingTimeout, setGeocodingTimeout] = useState(false);

  useEffect(() => {
    const geocodeAddresses = async () => {
      if (!pickupAddress || !deliveryAddress) return;

      setIsGeocoding(true);
      setGeocodingTimeout(false);
      console.log('🔍 SafeDeliveryMap - Iniciando geocodificación...');

      // Set a timeout to render map with fallback coordinates after 5 seconds
      const timeoutId = setTimeout(() => {
        console.log(
          '⏰ Geocoding timeout - rendering map with fallback coordinates'
        );
        setGeocodingTimeout(true);
        setCanRender(true);
        setIsGeocoding(false);
      }, 5000);

      try {
        // Extraer direcciones como texto
        const pickupText = formatAddressText(pickupAddress);
        const deliveryText = formatAddressText(deliveryAddress);

        console.log('📍 Direcciones a geocodificar:', {
          pickupText,
          deliveryText,
        });

        // Geocodificar ambas direcciones
        const [pickupResult, deliveryResult] = await Promise.all([
          geocodeAddress(pickupText),
          geocodeAddress(deliveryText),
        ]);

        clearTimeout(timeoutId); // Clear timeout if geocoding succeeds

        setGeocodedPickup(pickupResult);
        setGeocodedDelivery(deliveryResult);

        // Verificar si ambas geocodificaciones fueron exitosas
        const bothSuccessful = pickupResult.success && deliveryResult.success;
        setCanRender(bothSuccessful);

        console.log('✅ Geocodificación completada:', {
          pickup: pickupResult,
          delivery: deliveryResult,
          canRender: bothSuccessful,
        });
      } catch (error) {
        clearTimeout(timeoutId); // Clear timeout on error
        console.error('❌ Error en geocodificación:', error);
        setCanRender(true); // Still render map with fallback coordinates
        setGeocodingTimeout(true);
      } finally {
        setIsGeocoding(false);
      }
    };

    geocodeAddresses();
  }, [pickupAddress, deliveryAddress]);

  // Función para formatear dirección como texto
  const formatAddressText = (address: unknown): string => {
    if (!address) return '';

    // Si ya es un string, usarlo directamente
    if (typeof address === 'string') return address;

    // Si es un objeto, construir el texto
    if (typeof address === 'object' && address !== null) {
      const addr = address as Record<string, unknown>;
      const parts = [
        addr.street,
        addr.number,
        addr.city,
        addr.state,
        addr.zip,
      ].filter(Boolean);

      return parts.join(', ');
    }

    return '';
  };

  if (isGeocoding) {
    return (
      <div className={`w-full ${className}`} style={{ minHeight: '256px' }}>
        <div className='bg-blue-50 border border-blue-200 rounded-lg p-4 h-full flex items-center justify-center'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-3'></div>
            <h3 className='text-sm font-medium text-blue-800 mb-1'>
              Obteniendo coordenadas...
            </h3>
            <p className='text-xs text-blue-700'>
              Convirtiendo direcciones en coordenadas geográficas.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!canRender) {
    return (
      <div className={`w-full ${className}`} style={{ minHeight: '256px' }}>
        <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-4 h-full flex items-center justify-center'>
          <div className='text-center'>
            <span className='text-yellow-600 text-2xl mb-2 block'>⚠️</span>
            <h3 className='text-sm font-medium text-yellow-800 mb-2'>
              Mapa no disponible
            </h3>
            <p className='text-xs text-yellow-700 mb-3'>
              {geocodedPickup?.error ||
                geocodedDelivery?.error ||
                'No se pudieron obtener las coordenadas de las direcciones.'}
            </p>
            <button
              onClick={() => window.location.reload()}
              className='text-xs text-blue-600 hover:text-blue-800 underline'
            >
              Reintentar
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Debug: Log SafeDeliveryMap state
  console.log('🔍 SafeDeliveryMap state:', {
    isGeocoding,
    canRender,
    geocodingTimeout,
    geocodedPickup,
    geocodedDelivery,
    pickupAddress,
    deliveryAddress,
  });

  return (
    <div className={className} style={{ minHeight: '256px' }}>
      {geocodingTimeout && (
        <div className='mb-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-700'>
          ⚠️ Usando coordenadas de ejemplo (geocodificación no disponible)
        </div>
      )}
      <DeliveryMap
        pickupAddress={{
          lat: geocodedPickup?.lat || 25.6866,
          lng: geocodedPickup?.lng || -100.3161,
          name: geocodedPickup?.display_name || 'Punto de Recogida',
        }}
        deliveryAddress={{
          lat: geocodedDelivery?.lat || 25.6595,
          lng: geocodedDelivery?.lng || -100.3624,
          name: geocodedDelivery?.display_name || 'Punto de Entrega',
        }}
        className='w-full h-full'
      />
    </div>
  );
}
