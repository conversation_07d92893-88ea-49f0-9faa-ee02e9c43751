/**
 * Production Vehicle Fleet Management Service
 *
 * Real-time vehicle availability, capacity validation, and assignment
 */

import { createClient } from '@/utils/supabase/client';
import {
  MEXICAN_VEHICLE_TYPES,
  VehicleType,
} from '@/lib/data/mexican-vehicles';
import { ProductItem } from '@/types/order-form';

export interface VehicleAvailability {
  vehicle_id: string;
  vehicle_type_id: string;
  driver_id?: string;
  status: 'available' | 'assigned' | 'in_transit' | 'maintenance' | 'offline';
  current_location?: {
    lat: number;
    lng: number;
    address: string;
  };
  capacity_used: {
    weight_kg: number;
    volume_m3: number;
  };
  next_available_time?: string;
  maintenance_due?: string;
}

export interface VehicleAssignment {
  vehicle_id: string;
  driver_id: string;
  order_id: string;
  assigned_at: string;
  estimated_pickup_time: string;
  estimated_delivery_time: string;
  route_optimization: 'balanced' | 'fastest' | 'shortest' | 'eco';
}

export class VehicleFleetManagementService {
  private static supabase = createClient();

  /**
   * Get available vehicles for specific requirements
   */
  static async getAvailableVehicles(requirements: {
    weight_kg: number;
    volume_m3: number;
    special_handling: {
      fragile?: boolean;
      perishable?: boolean;
      valuable?: boolean;
      hazardous?: boolean;
      refrigerated?: boolean;
      oversized?: boolean;
    };
    pickup_location: { lat: number; lng: number };
    delivery_date: string;
    delivery_time_slot: string;
  }): Promise<{
    success: boolean;
    data?: {
      vehicle: VehicleType;
      availability: VehicleAvailability;
      estimated_cost: number;
      distance_to_pickup: number;
    }[];
    error?: string;
  }> {
    try {
      // Get all vehicles from database
      const { data: vehicles, error: vehicleError } = await this.supabase
        .from('vehicle_fleet')
        .select(
          `
          *,
          vehicle_types (*),
          drivers (*)
        `
        )
        .eq('status', 'available');

      if (vehicleError) {
        return { success: false, error: vehicleError.message };
      }

      // Filter vehicles based on capacity and special requirements
      const suitableVehicles =
        vehicles?.filter(vehicle => {
          const vehicleType = MEXICAN_VEHICLE_TYPES.find(
            vt => vt.id === vehicle.vehicle_type_id
          );
          if (!vehicleType) return false;

          // Check capacity
          if (requirements.weight_kg > vehicleType.max_weight_kg) return false;
          if (requirements.volume_m3 > vehicleType.max_volume_m3) return false;

          // Check special handling requirements
          if (
            requirements.special_handling.refrigerated &&
            !vehicleType.special_capabilities.includes('temperature_control')
          ) {
            return false;
          }

          if (
            requirements.special_handling.hazardous &&
            vehicleType.category === 'motorcycle'
          ) {
            return false;
          }

          if (
            requirements.special_handling.oversized &&
            vehicleType.category !== 'specialized'
          ) {
            return false;
          }

          return true;
        }) || [];

      // Calculate distance and cost for each suitable vehicle
      const vehicleOptions = await Promise.all(
        suitableVehicles.map(async vehicle => {
          const vehicleType = MEXICAN_VEHICLE_TYPES.find(
            vt => vt.id === vehicle.vehicle_type_id
          )!;

          // Calculate distance to pickup location
          const distance = vehicle.current_location
            ? this.calculateDistance(
                vehicle.current_location.lat,
                vehicle.current_location.lng,
                requirements.pickup_location.lat,
                requirements.pickup_location.lng
              )
            : 10; // Default 10km if no location

          // Estimate cost based on distance and vehicle type
          const specialHandling = {
            fragile: requirements.special_handling?.fragile ?? false,
            perishable: requirements.special_handling?.perishable ?? false,
            valuable: requirements.special_handling?.valuable ?? false,
            hazardous: requirements.special_handling?.hazardous ?? false,
            refrigerated: requirements.special_handling?.refrigerated ?? false,
            oversized: requirements.special_handling?.oversized ?? false,
          };
          const estimatedCost = this.calculateEstimatedCost(
            vehicleType,
            distance,
            requirements.weight_kg,
            specialHandling
          );

          return {
            vehicle: vehicleType,
            availability: {
              vehicle_id: vehicle.id,
              vehicle_type_id: vehicle.vehicle_type_id,
              driver_id: vehicle.driver_id,
              status: vehicle.status,
              current_location: vehicle.current_location,
              capacity_used: vehicle.capacity_used || {
                weight_kg: 0,
                volume_m3: 0,
              },
              next_available_time: vehicle.next_available_time,
            } as VehicleAvailability,
            estimated_cost: estimatedCost,
            distance_to_pickup: distance,
          };
        })
      );

      // Sort by cost-effectiveness (cost per km)
      vehicleOptions.sort((a, b) => {
        const efficiencyA =
          a.estimated_cost / Math.max(a.distance_to_pickup, 1);
        const efficiencyB =
          b.estimated_cost / Math.max(b.distance_to_pickup, 1);
        return efficiencyA - efficiencyB;
      });

      return { success: true, data: vehicleOptions };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Assign vehicle to order
   */
  static async assignVehicleToOrder(
    orderId: string,
    vehicleId: string,
    driverId: string,
    routeOptimization: 'balanced' | 'fastest' | 'shortest' | 'eco' = 'balanced'
  ): Promise<{
    success: boolean;
    data?: VehicleAssignment;
    error?: string;
  }> {
    try {
      // Check if vehicle is still available
      const { data: vehicle, error: vehicleError } = await this.supabase
        .from('vehicle_fleet')
        .select('*')
        .eq('id', vehicleId)
        .eq('status', 'available')
        .single();

      if (vehicleError || !vehicle) {
        return { success: false, error: 'Vehicle not available' };
      }

      // Create assignment record
      const assignment: Omit<
        VehicleAssignment,
        'estimated_pickup_time' | 'estimated_delivery_time'
      > = {
        vehicle_id: vehicleId,
        driver_id: driverId,
        order_id: orderId,
        assigned_at: new Date().toISOString(),
        route_optimization: routeOptimization,
      };

      // Get order details for time estimation
      const { data: order, error: orderError } = await this.supabase
        .from('orders')
        .select('*')
        .eq('id', orderId)
        .single();

      if (orderError || !order) {
        return { success: false, error: 'Order not found' };
      }

      // Estimate pickup and delivery times
      const now = new Date();
      const estimatedPickupTime = new Date(now.getTime() + 30 * 60000); // 30 minutes from now
      const estimatedDeliveryTime = new Date(
        estimatedPickupTime.getTime() + 60 * 60000
      ); // 1 hour after pickup

      const fullAssignment: VehicleAssignment = {
        ...assignment,
        estimated_pickup_time: estimatedPickupTime.toISOString(),
        estimated_delivery_time: estimatedDeliveryTime.toISOString(),
      };

      // Insert assignment
      const { error: assignmentError } = await this.supabase
        .from('vehicle_assignments')
        .insert(fullAssignment);

      if (assignmentError) {
        return { success: false, error: assignmentError.message };
      }

      // Update vehicle status
      await this.supabase
        .from('vehicle_fleet')
        .update({
          status: 'assigned',
          updated_at: new Date().toISOString(),
        })
        .eq('id', vehicleId);

      // Update order with vehicle assignment
      await this.supabase
        .from('orders')
        .update({
          vehicle_id: vehicleId,
          driver_id: driverId,
          status: 'confirmed',
          updated_at: new Date().toISOString(),
        })
        .eq('id', orderId);

      return { success: true, data: fullAssignment };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get vehicle fleet status overview
   */
  static async getFleetStatus(): Promise<{
    success: boolean;
    data?: {
      total_vehicles: number;
      available: number;
      assigned: number;
      in_transit: number;
      maintenance: number;
      offline: number;
      by_category: Record<string, number>;
    };
    error?: string;
  }> {
    try {
      const { data: vehicles, error } = await this.supabase.from(
        'vehicle_fleet'
      ).select(`
          status,
          vehicle_types (category)
        `);

      if (error) {
        return { success: false, error: error.message };
      }

      const statusCounts = {
        total_vehicles: vehicles?.length || 0,
        available: 0,
        assigned: 0,
        in_transit: 0,
        maintenance: 0,
        offline: 0,
        by_category: {} as Record<string, number>,
      };

      vehicles?.forEach(vehicle => {
        // Count by status
        statusCounts[vehicle.status as keyof typeof statusCounts]++;

        // Count by category
        const vehicleType = Array.isArray(vehicle.vehicle_types)
          ? vehicle.vehicle_types[0]
          : vehicle.vehicle_types;
        const category = vehicleType?.category || 'unknown';
        statusCounts.by_category[category] =
          (statusCounts.by_category[category] || 0) + 1;
      });

      return { success: true, data: statusCounts };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Update vehicle location (for GPS tracking)
   */
  static async updateVehicleLocation(
    vehicleId: string,
    location: { lat: number; lng: number; address?: string }
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const { error } = await this.supabase
        .from('vehicle_fleet')
        .update({
          current_location: location,
          updated_at: new Date().toISOString(),
        })
        .eq('id', vehicleId);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Schedule vehicle maintenance
   */
  static async scheduleVehicleMaintenance(
    vehicleId: string,
    maintenanceDate: string,
    maintenanceType: string,
    notes?: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // Insert maintenance record
      const { error: maintenanceError } = await this.supabase
        .from('vehicle_maintenance')
        .insert({
          vehicle_id: vehicleId,
          scheduled_date: maintenanceDate,
          maintenance_type: maintenanceType,
          notes,
          status: 'scheduled',
          created_at: new Date().toISOString(),
        });

      if (maintenanceError) {
        return { success: false, error: maintenanceError.message };
      }

      // Update vehicle with maintenance due date
      await this.supabase
        .from('vehicle_fleet')
        .update({
          maintenance_due: maintenanceDate,
          updated_at: new Date().toISOString(),
        })
        .eq('id', vehicleId);

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get optimal vehicle suggestions based on products
   */
  static getVehicleSuggestions(products: ProductItem[]): {
    suggestions: VehicleType[];
    requirements: {
      total_weight: number;
      total_volume: number;
      special_handling: {
        fragile: boolean;
        perishable: boolean;
        valuable: boolean;
        hazardous: boolean;
        refrigerated: boolean;
        oversized: boolean;
      };
    };
  } {
    // Calculate total requirements
    const totalWeight = products.reduce((sum, product) => {
      const weight = product.weight || 1;
      const weightInKg = product.weight_unit === 'g' ? weight / 1000 : weight;
      return sum + weightInKg * product.quantity;
    }, 0);

    const totalVolume = products.reduce((sum, product) => {
      if (!product.dimensions) return sum;
      const volume =
        (product.dimensions.length *
          product.dimensions.width *
          product.dimensions.height) /
        1000000;
      return sum + volume * product.quantity;
    }, 0);

    const specialHandling = {
      fragile: products.some(p => p.special_handling?.fragile),
      perishable: products.some(p => p.special_handling?.perishable),
      valuable: products.some(p => p.special_handling?.valuable),
      hazardous: products.some(p => p.special_handling?.hazardous),
      refrigerated: products.some(p => p.special_handling?.refrigerated),
      oversized: products.some(p => p.special_handling?.oversized),
    };

    // Filter suitable vehicles
    const suitableVehicles = MEXICAN_VEHICLE_TYPES.filter(vehicle => {
      // Check capacity
      if (
        totalWeight > vehicle.max_weight_kg ||
        totalVolume > vehicle.max_volume_m3
      ) {
        return false;
      }

      // Check special handling requirements
      if (
        specialHandling.refrigerated &&
        !vehicle.special_capabilities.includes('temperature_control')
      ) {
        return false;
      }

      if (specialHandling.hazardous && vehicle.category === 'motorcycle') {
        return false;
      }

      if (specialHandling.oversized && vehicle.category !== 'specialized') {
        return false;
      }

      return true;
    });

    // Sort by efficiency (cost per kg capacity)
    const suggestions = suitableVehicles.sort((a, b) => {
      const efficiencyA = a.base_rate_per_km / a.max_weight_kg;
      const efficiencyB = b.base_rate_per_km / b.max_weight_kg;
      return efficiencyA - efficiencyB;
    });

    return {
      suggestions: suggestions.slice(0, 3), // Top 3 suggestions
      requirements: {
        total_weight: totalWeight,
        total_volume: totalVolume,
        special_handling: specialHandling,
      },
    };
  }

  /**
   * Calculate estimated cost for vehicle assignment
   */
  private static calculateEstimatedCost(
    vehicleType: VehicleType,
    distance: number,
    weight: number,
    specialHandling: {
      fragile: boolean;
      perishable: boolean;
      valuable: boolean;
      hazardous: boolean;
      refrigerated: boolean;
      oversized: boolean;
    }
  ): number {
    let baseCost = vehicleType.base_rate_per_km * distance;

    // Add weight-based surcharge
    const utilizationFactor = Math.min(weight / vehicleType.max_weight_kg, 1);
    baseCost *= 0.8 + utilizationFactor * 0.4; // 80% base + up to 40% utilization

    // Add special handling surcharges
    if (specialHandling.fragile) baseCost *= 1.1;
    if (specialHandling.perishable) baseCost *= 1.2;
    if (specialHandling.valuable) baseCost *= 1.15;
    if (specialHandling.hazardous) baseCost *= 1.3;
    if (specialHandling.refrigerated) baseCost *= 1.25;
    if (specialHandling.oversized) baseCost *= 1.4;

    return Math.round(baseCost * 100) / 100;
  }

  /**
   * Calculate distance between two coordinates
   */
  private static calculateDistance(
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLng / 2) *
        Math.sin(dLng / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private static toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}
