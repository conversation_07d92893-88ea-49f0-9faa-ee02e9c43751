/**
 * Comprehensive Order Flow Validation Tests
 * 
 * Tests the complete Mexican logistics order flow implementation
 * against the 8-step specification requirements
 */

import { OrderFormDataSchema, ProductItem } from '../validation/order-schemas';
import { MEXICAN_VEHICLE_TYPES, suggestVehicleTypes } from '../data/mexican-vehicles';
import { CostCalculationService } from '../services/cost-calculation';
import { AddressAutocompleteService } from '../services/address-autocomplete';
import { MockDataSeeder } from '../data/mock-data-seeder';

export interface ValidationResult {
  step: string;
  passed: boolean;
  errors: string[];
  warnings: string[];
  details?: any;
}

export class OrderFlowValidator {
  
  /**
   * Run comprehensive validation of the entire order flow
   */
  static async validateCompleteOrderFlow(): Promise<{
    overallPassed: boolean;
    results: ValidationResult[];
    summary: {
      totalTests: number;
      passed: number;
      failed: number;
      warnings: number;
    };
  }> {
    const results: ValidationResult[] = [];

    // Step 1: Customer Information Validation
    results.push(await this.validateStep1CustomerInfo());

    // Step 2: Address Management Validation
    results.push(await this.validateStep2AddressManagement());

    // Step 3: Product Management Validation
    results.push(await this.validateStep3ProductManagement());

    // Step 4: Delivery Options Validation
    results.push(await this.validateStep4DeliveryOptions());

    // Step 5: Multi-Stop Management Validation
    results.push(await this.validateStep5MultiStopManagement());

    // Step 6: Payment and Fiscal Validation
    results.push(await this.validateStep6PaymentFiscal());

    // Step 7: Additional Notes Validation
    results.push(await this.validateStep7AdditionalNotes());

    // Step 8: Mexican Logistics Validation
    results.push(await this.validateStep8MexicanLogistics());

    // Step 9: Order Review Validation
    results.push(await this.validateStep9OrderReview());

    // Integration Tests
    results.push(await this.validateIntegrationTests());

    const summary = {
      totalTests: results.length,
      passed: results.filter(r => r.passed).length,
      failed: results.filter(r => !r.passed).length,
      warnings: results.reduce((sum, r) => sum + r.warnings.length, 0)
    };

    return {
      overallPassed: summary.failed === 0,
      results,
      summary
    };
  }

  /**
   * Step 1: Customer Information Validation
   */
  static async validateStep1CustomerInfo(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Test valid customer data
      const validCustomer = {
        customer_name: 'María González López',
        customer_phone: '+52 55 1234 5678',
        customer_email: '<EMAIL>'
      };

      // Test phone validation
      const invalidPhones = ['123', '55-1234-5678', '1234567890123'];
      invalidPhones.forEach(phone => {
        try {
          OrderFormDataSchema.pick({ customer_phone: true }).parse({ customer_phone: phone });
          errors.push(`Invalid phone ${phone} was accepted`);
        } catch {
          // Expected to fail
        }
      });

      // Test name validation
      const invalidNames = ['A', '', 'X'.repeat(101)];
      invalidNames.forEach(name => {
        try {
          OrderFormDataSchema.pick({ customer_name: true }).parse({ customer_name: name });
          errors.push(`Invalid name "${name}" was accepted`);
        } catch {
          // Expected to fail
        }
      });

    } catch (error) {
      errors.push(`Step 1 validation error: ${error}`);
    }

    return {
      step: 'Step 1: Customer Information',
      passed: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Step 2: Address Management Validation
   */
  static async validateStep2AddressManagement(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Test address autocomplete service
      const suggestions = await AddressAutocompleteService.searchAddresses('Insurgentes', 5);
      if (suggestions.length === 0) {
        warnings.push('Address autocomplete returned no results for common street name');
      }

      // Test postal code validation
      const validZips = ['06700', '44160', '64000'];
      const invalidZips = ['123', '123456', 'ABCDE'];

      validZips.forEach(zip => {
        if (!AddressAutocompleteService.validatePostalCode(zip)) {
          errors.push(`Valid postal code ${zip} was rejected`);
        }
      });

      invalidZips.forEach(zip => {
        if (AddressAutocompleteService.validatePostalCode(zip)) {
          errors.push(`Invalid postal code ${zip} was accepted`);
        }
      });

      // Test Mexican states validation
      const validStates = ['Ciudad de México', 'Jalisco', 'Nuevo León'];
      const invalidStates = ['California', 'Texas', 'Invalid State'];

      validStates.forEach(state => {
        if (!AddressAutocompleteService.isValidMexicanState(state)) {
          errors.push(`Valid Mexican state ${state} was rejected`);
        }
      });

      invalidStates.forEach(state => {
        if (AddressAutocompleteService.isValidMexicanState(state)) {
          errors.push(`Invalid state ${state} was accepted`);
        }
      });

    } catch (error) {
      errors.push(`Step 2 validation error: ${error}`);
    }

    return {
      step: 'Step 2: Address Management',
      passed: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Step 3: Product Management Validation
   */
  static async validateStep3ProductManagement(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      const sampleProducts = MockDataSeeder.SAMPLE_PRODUCTS;

      // Test product validation
      sampleProducts.forEach((productTemplate, index) => {
        const product: ProductItem = {
          ...productTemplate,
          id: `test-${index}`,
          subtotal: productTemplate.unit_price * productTemplate.quantity
        };

        try {
          OrderFormDataSchema.pick({ products: true }).parse({ products: [product] });
        } catch (error) {
          errors.push(`Sample product ${index} failed validation: ${error}`);
        }
      });

      // Test weight and volume calculations
      const testProduct: ProductItem = {
        id: 'test-calc',
        name: 'Test Product',
        quantity: 2,
        unit_measure: 'pieza',
        unit_price: 100,
        subtotal: 200,
        weight: 5,
        weight_unit: 'kg',
        dimensions: { length: 30, width: 20, height: 10, unit: 'cm' },
        special_handling: { fragile: true, perishable: false, valuable: false, hazardous: false, refrigerated: false, oversized: false }
      };

      const expectedVolume = (30 * 20 * 10) / 1000000; // Convert cm³ to m³
      const calculatedVolume = testProduct.dimensions ? 
        (testProduct.dimensions.length * testProduct.dimensions.width * testProduct.dimensions.height) / 1000000 : 0;

      if (Math.abs(calculatedVolume - expectedVolume) > 0.001) {
        errors.push(`Volume calculation incorrect: expected ${expectedVolume}, got ${calculatedVolume}`);
      }

      // Test special handling flags
      const specialHandlingTypes = ['fragile', 'perishable', 'valuable', 'hazardous', 'refrigerated', 'oversized'];
      specialHandlingTypes.forEach(type => {
        const testHandling = { [type]: true };
        try {
          OrderFormDataSchema.pick({ products: true }).parse({
            products: [{
              ...testProduct,
              special_handling: testHandling
            }]
          });
        } catch (error) {
          errors.push(`Special handling ${type} validation failed: ${error}`);
        }
      });

    } catch (error) {
      errors.push(`Step 3 validation error: ${error}`);
    }

    return {
      step: 'Step 3: Product Management',
      passed: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Step 4: Delivery Options Validation
   */
  static async validateStep4DeliveryOptions(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Test time slot validation
      const validTimeSlots = ['08:00-11:00', '11:00-14:00', '14:00-17:00', '17:00-20:00'];
      const invalidTimeSlots = ['25:00-26:00', '08:00-25:00', 'invalid'];

      validTimeSlots.forEach(slot => {
        try {
          OrderFormDataSchema.pick({ delivery_time_slot: true }).parse({ delivery_time_slot: slot });
        } catch (error) {
          errors.push(`Valid time slot ${slot} was rejected: ${error}`);
        }
      });

      invalidTimeSlots.forEach(slot => {
        try {
          OrderFormDataSchema.pick({ delivery_time_slot: true }).parse({ delivery_time_slot: slot });
          errors.push(`Invalid time slot ${slot} was accepted`);
        } catch {
          // Expected to fail
        }
      });

      // Test delivery mode validation
      const validModes = ['home', 'pickup_point'];
      validModes.forEach(mode => {
        try {
          OrderFormDataSchema.pick({ delivery_mode: true }).parse({ delivery_mode: mode });
        } catch (error) {
          errors.push(`Valid delivery mode ${mode} was rejected: ${error}`);
        }
      });

    } catch (error) {
      errors.push(`Step 4 validation error: ${error}`);
    }

    return {
      step: 'Step 4: Delivery Options',
      passed: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Step 5: Multi-Stop Management Validation
   */
  static async validateStep5MultiStopManagement(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Test multi-stop generation
      const stops = MockDataSeeder.generateMultiStops(3);
      
      if (stops.length !== 3) {
        errors.push(`Expected 3 stops, got ${stops.length}`);
      }

      // Validate stop order
      stops.forEach((stop, index) => {
        if (stop.order !== index + 1) {
          errors.push(`Stop ${index} has incorrect order: expected ${index + 1}, got ${stop.order}`);
        }
      });

      // Test stop validation
      stops.forEach((stop, index) => {
        try {
          OrderFormDataSchema.pick({ stops: true }).parse({ stops: [stop] });
        } catch (error) {
          errors.push(`Stop ${index} validation failed: ${error}`);
        }
      });

    } catch (error) {
      errors.push(`Step 5 validation error: ${error}`);
    }

    return {
      step: 'Step 5: Multi-Stop Management',
      passed: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Step 6: Payment and Fiscal Validation
   */
  static async validateStep6PaymentFiscal(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Test payment methods
      const validPaymentMethods = ['card', 'cash', 'digital_wallet', 'bank_transfer'];
      validPaymentMethods.forEach(method => {
        try {
          OrderFormDataSchema.pick({ payment_method: true }).parse({ payment_method: method });
        } catch (error) {
          errors.push(`Valid payment method ${method} was rejected: ${error}`);
        }
      });

      // Test RFC validation (basic format check)
      const validRFCs = ['XAXX010101000', 'GODE561231GR8'];
      const invalidRFCs = ['123', 'INVALID', 'TOOLONGTOBEVALID'];

      validRFCs.forEach(rfc => {
        // RFC validation would be implemented in the schema
        if (!/^[A-ZÑ&]{3,4}\d{6}[A-Z0-9]{3}$/.test(rfc)) {
          warnings.push(`RFC format validation might need improvement for: ${rfc}`);
        }
      });

    } catch (error) {
      errors.push(`Step 6 validation error: ${error}`);
    }

    return {
      step: 'Step 6: Payment and Fiscal',
      passed: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Step 7: Additional Notes Validation
   */
  static async validateStep7AdditionalNotes(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Test instruction length limits
      const longInstructions = 'X'.repeat(1001);
      try {
        OrderFormDataSchema.pick({ special_instructions: true }).parse({ special_instructions: longInstructions });
        errors.push('Instructions exceeding 1000 characters were accepted');
      } catch {
        // Expected to fail
      }

      // Test substitution policy
      const validSubstitutionValues = [true, false];
      validSubstitutionValues.forEach(value => {
        try {
          OrderFormDataSchema.pick({ allow_substitutions: true }).parse({ allow_substitutions: value });
        } catch (error) {
          errors.push(`Valid substitution value ${value} was rejected: ${error}`);
        }
      });

    } catch (error) {
      errors.push(`Step 7 validation error: ${error}`);
    }

    return {
      step: 'Step 7: Additional Notes',
      passed: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Step 8: Mexican Logistics Validation
   */
  static async validateStep8MexicanLogistics(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Test vehicle type catalog completeness
      const expectedVehicleCategories = ['motorcycle', 'light', 'medium', 'heavy', 'specialized'];
      const actualCategories = [...new Set(MEXICAN_VEHICLE_TYPES.map(v => v.category))];
      
      expectedVehicleCategories.forEach(category => {
        if (!actualCategories.includes(category)) {
          errors.push(`Missing vehicle category: ${category}`);
        }
      });

      // Test vehicle suggestion algorithm
      const testProducts: ProductItem[] = [{
        id: 'test-1',
        name: 'Light Package',
        quantity: 1,
        unit_measure: 'pieza',
        unit_price: 100,
        subtotal: 100,
        weight: 2,
        weight_unit: 'kg',
        dimensions: { length: 20, width: 15, height: 10, unit: 'cm' },
        special_handling: { fragile: false, perishable: false, valuable: false, hazardous: false, refrigerated: false, oversized: false }
      }];

      const suggestions = suggestVehicleTypes(2, 0.003, { fragile: false, perishable: false, valuable: false, hazardous: false, refrigerated: false, oversized: false });
      
      if (suggestions.length === 0) {
        errors.push('Vehicle suggestion algorithm returned no results for light package');
      }

      // Test that motorcycle is suggested for light packages
      const motorcycleSuggested = suggestions.some(v => v.category === 'motorcycle');
      if (!motorcycleSuggested) {
        warnings.push('Motorcycle not suggested for light package - algorithm might need tuning');
      }

    } catch (error) {
      errors.push(`Step 8 validation error: ${error}`);
    }

    return {
      step: 'Step 8: Mexican Logistics',
      passed: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Step 9: Order Review Validation
   */
  static async validateStep9OrderReview(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Test complete order validation
      const mockOrders = MockDataSeeder.generateMockOrders(5);
      
      mockOrders.forEach((order, index) => {
        const validation = MockDataSeeder.validateOrderData(order);
        if (!validation.isValid) {
          errors.push(`Mock order ${index} validation failed: ${validation.errors.join(', ')}`);
        }
      });

      // Test cost calculation
      const testOrder = mockOrders[0];
      if (testOrder) {
        try {
          const costResult = CostCalculationService.calculateCost({
            products: testOrder.products,
            vehicleTypeId: testOrder.vehicle_type_id,
            deliveryMode: testOrder.stops && testOrder.stops.length > 0 ? 'multi' : 'single',
            stops: testOrder.stops,
            deliveryDate: testOrder.delivery_date,
            deliveryTimeSlot: testOrder.delivery_time_slot,
            deliveryRegion: 'local',
            specialHandlingRequired: testOrder.products.some(p => 
              Object.values(p.special_handling || {}).some(Boolean)
            )
          });

          if (costResult.total <= 0) {
            errors.push('Cost calculation returned invalid total');
          }

          if (costResult.breakdown.length === 0) {
            warnings.push('Cost breakdown is empty');
          }
        } catch (error) {
          errors.push(`Cost calculation failed: ${error}`);
        }
      }

    } catch (error) {
      errors.push(`Step 9 validation error: ${error}`);
    }

    return {
      step: 'Step 9: Order Review',
      passed: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Integration Tests
   */
  static async validateIntegrationTests(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Test complete order flow with mock data
      const testScenarios = MockDataSeeder.getTestScenarios();
      
      // Test light delivery scenario
      const lightScenario = testScenarios.lightDelivery;
      const lightSuggestions = suggestVehicleTypes(
        lightScenario.products[0].weight,
        0.001, // Small volume
        lightScenario.products[0].special_handling || {}
      );
      
      if (lightSuggestions.length === 0) {
        errors.push('No vehicle suggestions for light delivery scenario');
      }

      // Test heavy delivery scenario
      const heavyScenario = testScenarios.heavyDelivery;
      const heavySuggestions = suggestVehicleTypes(
        heavyScenario.products[0].weight,
        0.1, // Large volume
        heavyScenario.products[0].special_handling || {}
      );
      
      if (heavySuggestions.length === 0) {
        errors.push('No vehicle suggestions for heavy delivery scenario');
      }

      // Test that heavy items don't suggest motorcycles
      const motorcycleForHeavy = heavySuggestions.some(v => v.category === 'motorcycle');
      if (motorcycleForHeavy) {
        errors.push('Motorcycle suggested for heavy delivery - algorithm error');
      }

    } catch (error) {
      errors.push(`Integration test error: ${error}`);
    }

    return {
      step: 'Integration Tests',
      passed: errors.length === 0,
      errors,
      warnings
    };
  }
}
