/**
 * Rate Limiter Implementation
 *
 * Implements sliding window rate limiting with Redis-like behavior
 * using in-memory storage for development and Redis for production
 */

interface RateLimitEntry {
  count: number;
  resetTime: number;
  requests: number[];
}

interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  totalRequests: number;
}

// In-memory storage for development
const rateLimitStore = new Map<string, RateLimitEntry>();

// Cleanup interval to prevent memory leaks
setInterval(() => {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (entry.resetTime < now) {
      rateLimitStore.delete(key);
    }
  }
}, 60000); // Cleanup every minute

/**
 * Sliding window rate limiter
 * @param identifier - Unique identifier (IP, user ID, etc.)
 * @param limit - Maximum requests per window
 * @param windowMs - Window size in milliseconds (default: 1 minute)
 * @param burst - Allow burst requests (default: limit * 1.5)
 */
export async function rateLimit(
  identifier: string,
  limit: number,
  windowMs: number = 60000,
  burst?: number
): Promise<RateLimitResult> {
  const now = Date.now();
  const windowStart = now - windowMs;
  const burstLimit = burst || Math.floor(limit * 1.5);

  // Get or create rate limit entry
  let entry = rateLimitStore.get(identifier);

  if (!entry) {
    entry = {
      count: 0,
      resetTime: now + windowMs,
      requests: [],
    };
    rateLimitStore.set(identifier, entry);
  }

  // Clean old requests outside the sliding window
  entry.requests = entry.requests.filter(timestamp => timestamp > windowStart);
  entry.count = entry.requests.length;

  // Check if request is allowed
  const allowed = entry.count < burstLimit;

  if (allowed) {
    entry.requests.push(now);
    entry.count++;
  }

  // Update reset time for next window
  if (now >= entry.resetTime) {
    entry.resetTime = now + windowMs;
  }

  return {
    allowed,
    remaining: Math.max(0, limit - entry.count),
    resetTime: entry.resetTime,
    totalRequests: entry.count,
  };
}

/**
 * Advanced rate limiting with different tiers
 */
export async function tieredRateLimit(
  identifier: string,
  userRole?: 'admin' | 'customer' | 'delivery'
): Promise<RateLimitResult> {
  // Different limits based on user role
  const limits = {
    admin: { rpm: 120, burst: 180 },
    delivery: { rpm: 90, burst: 135 },
    customer: { rpm: 60, burst: 90 },
    anonymous: { rpm: 30, burst: 45 },
  };

  const tier = userRole || 'anonymous';
  const config = limits[tier];

  return rateLimit(identifier, config.rpm, 60000, config.burst);
}

/**
 * Rate limit by endpoint
 */
export async function endpointRateLimit(
  identifier: string,
  endpoint: string,
  userRole?: 'admin' | 'customer' | 'delivery'
): Promise<RateLimitResult> {
  // Endpoint-specific limits
  const endpointLimits: Record<string, { rpm: number; burst?: number }> = {
    '/api/auth/login': { rpm: 5, burst: 10 },
    '/api/auth/signup': { rpm: 3, burst: 5 },
    '/api/orders/create': { rpm: 10, burst: 15 },
    '/api/orders/update-status': { rpm: 30, burst: 45 },
    '/api/orders/assign-driver': { rpm: 20, burst: 30 },
    '/admin': { rpm: 60, burst: 90 },
    '/customer': { rpm: 40, burst: 60 },
    '/delivery': { rpm: 50, burst: 75 },
  };

  // Find matching endpoint pattern
  let endpointConfig = endpointLimits[endpoint];

  if (!endpointConfig) {
    // Check for pattern matches
    for (const [pattern, config] of Object.entries(endpointLimits)) {
      if (endpoint.startsWith(pattern)) {
        endpointConfig = config;
        break;
      }
    }
  }

  // Fallback to tiered rate limiting
  if (!endpointConfig) {
    return tieredRateLimit(identifier, userRole);
  }

  // Apply role-based multipliers
  const roleMultipliers = {
    admin: 1.5,
    delivery: 1.2,
    customer: 1.0,
    anonymous: 0.5,
  };

  const multiplier = roleMultipliers[userRole || 'anonymous'];
  const adjustedLimit = Math.floor(endpointConfig.rpm * multiplier);
  const adjustedBurst = endpointConfig.burst
    ? Math.floor(endpointConfig.burst * multiplier)
    : undefined;

  return rateLimit(
    `${identifier}:${endpoint}`,
    adjustedLimit,
    60000,
    adjustedBurst
  );
}

/**
 * Check if IP is in whitelist (for admin IPs, etc.)
 */
export function isWhitelisted(ip: string): boolean {
  const whitelist = process.env.RATE_LIMIT_WHITELIST?.split(',') || [];
  return whitelist.includes(ip);
}

/**
 * Check if IP is blacklisted
 */
export function isBlacklisted(ip: string): boolean {
  const blacklist = process.env.RATE_LIMIT_BLACKLIST?.split(',') || [];
  return blacklist.includes(ip);
}

/**
 * Adaptive rate limiting based on system load
 */
export async function adaptiveRateLimit(
  identifier: string,
  baseLimit: number,
  userRole?: 'admin' | 'customer' | 'delivery'
): Promise<RateLimitResult> {
  // TODO: Implement system load monitoring
  // For now, use static multipliers based on time of day

  const hour = new Date().getHours();
  let loadMultiplier = 1.0;

  // Reduce limits during peak hours (9 AM - 6 PM)
  if (hour >= 9 && hour <= 18) {
    loadMultiplier = 0.8;
  }
  // Increase limits during off-peak hours
  else if (hour >= 22 || hour <= 6) {
    loadMultiplier = 1.3;
  }

  // Apply role-based adjustment
  const roleMultiplier =
    userRole === 'admin' ? 1.2 : userRole === 'delivery' ? 1.1 : 1.0;
  const adjustedLimit = Math.floor(baseLimit * loadMultiplier * roleMultiplier);

  return rateLimit(identifier, adjustedLimit);
}

/**
 * Get rate limit status without incrementing counter
 */
export async function getRateLimitStatus(
  identifier: string
): Promise<RateLimitResult | null> {
  const entry = rateLimitStore.get(identifier);

  if (!entry) {
    return null;
  }

  const now = Date.now();
  const windowStart = now - 60000; // 1 minute window

  // Clean old requests
  const activeRequests = entry.requests.filter(
    timestamp => timestamp > windowStart
  );

  return {
    allowed: activeRequests.length < 60, // Default limit
    remaining: Math.max(0, 60 - activeRequests.length),
    resetTime: entry.resetTime,
    totalRequests: activeRequests.length,
  };
}

/**
 * Clear rate limit for identifier (admin function)
 */
export async function clearRateLimit(identifier: string): Promise<void> {
  rateLimitStore.delete(identifier);
}

/**
 * Get all rate limit entries (admin function)
 */
export async function getAllRateLimits(): Promise<
  Record<string, RateLimitEntry>
> {
  const result: Record<string, RateLimitEntry> = {};

  for (const [key, value] of rateLimitStore.entries()) {
    result[key] = { ...value };
  }

  return result;
}
