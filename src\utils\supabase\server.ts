import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/lib/supabase';

/**
 * Returns a Supabase client configured for **server** usage (Server Components,
 * Route Handlers, Server Actions).
 * Cookie handling follows the pattern documented in Supabase SSR guide.
 * Updated for ECC P-256 JWT key support.
 */
export async function createClient() {
  const cookieStore = await cookies();

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set({ name, value, ...options })
            );
          } catch {
            // ignore – cannot set cookies from Server Components
          }
        },
      },
    }
  );
}
