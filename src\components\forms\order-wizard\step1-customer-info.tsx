'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { OrderFormData } from '@/types/order-form';

interface Step1Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
  errors?: Record<string, string>;
}

export function OrderWizardStep1({
  formData,
  updateFormData,
  errors = {},
}: Step1Props) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          👤 Datos del Cliente
        </CardTitle>
        <CardDescription>
          Información de contacto para el pedido
        </CardDescription>
      </CardHeader>
      <CardContent className='grid grid-cols-1 md:grid-cols-2 gap-4'>
        <div className='space-y-2'>
          <Label htmlFor='customer_name'>Nombre Completo</Label>
          <Input
            id='customer_name'
            value={formData.customer_name}
            onChange={e =>
              updateFormData({
                customer_name: e.target.value,
              })
            }
            required
            className={errors.customer_name ? 'border-red-500' : ''}
          />
          {errors.customer_name && (
            <p className='text-sm text-red-600'>{errors.customer_name}</p>
          )}
        </div>
        <div className='space-y-2'>
          <Label htmlFor='customer_phone'>Teléfono de Contacto</Label>
          <Input
            id='customer_phone'
            type='tel'
            value={formData.customer_phone}
            onChange={e =>
              updateFormData({
                customer_phone: e.target.value,
              })
            }
            required
            className={errors.customer_phone ? 'border-red-500' : ''}
          />
          {errors.customer_phone && (
            <p className='text-sm text-red-600'>{errors.customer_phone}</p>
          )}
        </div>
        <div className='space-y-2'>
          <Label htmlFor='customer_email'>Correo Electrónico (Opcional)</Label>
          <Input
            id='customer_email'
            type='email'
            value={formData.customer_email || ''}
            onChange={e =>
              updateFormData({
                customer_email: e.target.value,
              })
            }
            className={errors.customer_email ? 'border-red-500' : ''}
          />
          {errors.customer_email && (
            <p className='text-sm text-red-600'>{errors.customer_email}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
