'use client';

import { useEffect } from 'react';
import { useAuthStore } from '@/stores/authStore';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { VehicleManagement } from '@/components/admin/vehicle-management';

export default function AdminVehiclesPage() {
  const { user, profile, loading, isAdmin } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/');
      return;
    }

    if (!loading && profile && !isAdmin) {
      router.push('/dashboard');
      return;
    }
  }, [user, profile, loading, isAdmin, router]);

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-black'></div>
      </div>
    );
  }

  if (!user || !profile || !isAdmin) {
    return null;
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Header */}
      <header className='bg-white shadow-sm border-b'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center h-16'>
            <div className='flex items-center space-x-4'>
              <Button
                variant='ghost'
                onClick={() => router.back()}
                className='text-blue-600'
              >
                ← Volver al Panel
              </Button>
              <h1 className='text-xl font-bold text-black'>
                Gestión de Vehículos
              </h1>
            </div>
            <div className='flex items-center space-x-4'>
              <span className='text-sm text-gray-600'>
                {profile.full_name || user.email}
              </span>
              <Badge variant='default'>Administrador</Badge>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='max-w-7xl mx-auto py-6 sm:px-6 lg:px-8'>
        <div className='px-4 py-6 sm:px-0'>
          <VehicleManagement />
        </div>
      </main>
    </div>
  );
}
