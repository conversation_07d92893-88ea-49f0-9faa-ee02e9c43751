'use client';

import { useState, useEffect, useCallback } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { VehicleFleetManagementService } from '@/lib/services/vehicle-fleet-management';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { createClient } from '@/utils/supabase/client';
import { VehicleType } from '@/lib/data/mexican-vehicles';

interface ProductItem {
  id: string;
  name: string;
  quantity: number;
  unit_measure: string;
  unit_price: number;
  subtotal: number;
  weight: number;
  weight_unit: string;
  dimensions: {
    length: number;
    width: number;
    height: number;
    unit: string;
  };
  special_handling: {
    fragile: boolean;
    perishable: boolean;
    valuable: boolean;
    hazardous: boolean;
    refrigerated: boolean;
    oversized: boolean;
  };
  notes?: string;
}

interface Stop {
  id: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  scheduled_time?: string;
  instructions?: string;
}

interface OrderDetails {
  id: string;
  tracking_number: string;
  customer_name: string;
  customer_phone: string;
  products: ProductItem[];
  stops: Stop[];
  delivery_date: string;
  delivery_time_slot: string;
  pickup_address: {
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  special_instructions?: string;
  total_cost: number;
}

// Updated interface to match VehicleFleetManagementService.VehicleAvailability
interface VehicleAvailability {
  vehicle_id: string;
  vehicle_type_id: string;
  driver_id?: string;
  status: 'available' | 'assigned' | 'in_transit' | 'maintenance' | 'offline';
  current_location?: {
    lat: number;
    lng: number;
    address: string;
  };
  capacity_used: {
    weight_kg: number;
    volume_m3: number;
  };
  next_available_time?: string;
  maintenance_due?: string;
}

// Interface for the object returned by VehicleFleetManagementService.getAvailableVehicles
interface VehicleOptionFromService {
  vehicle: VehicleType;
  availability: VehicleAvailability;
  estimated_cost: number;
  distance_to_pickup: number;
}

interface VehicleOption extends VehicleOptionFromService {
  efficiency_score: number;
}

interface Driver {
  id: string;
  full_name: string;
  phone: string;
  status: string;
  vehicle_id?: string;
  license_number: string;
  rating: number;
}

export default function AssignVehiclePage() {
  const params = useParams();
  const router = useRouter();
  const { user, loading } = useAuthStore();
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null);
  const [vehicleOptions, setVehicleOptions] = useState<VehicleOption[]>([]);
  const [selectedVehicle, setSelectedVehicle] = useState<string>('');
  const [selectedDriver, setSelectedDriver] = useState<string>('');
  const [routeOptimization, setRouteOptimization] = useState<
    'balanced' | 'fastest' | 'shortest' | 'eco'
  >('balanced');
  const [availableDrivers, setAvailableDrivers] = useState<Driver[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAssigning, setIsAssigning] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const orderId = params.id as string;
  const supabase = createClient();

  // Load order details and vehicle options
  const loadOrderData = useCallback(async () => {
    if (!orderId) return;

    setIsLoading(true);
    setError(null);

    try {
      // Get order details
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .select('*')
        .eq('id', orderId)
        .single();

      if (orderError) {
        throw new Error(orderError.message);
      }

      setOrderDetails(order);

      // Calculate requirements from products
      const totalWeight =
        order.products?.reduce((sum: number, product: ProductItem) => {
          const weight = product.weight || 1;
          const weightInKg =
            product.weight_unit === 'g' ? weight / 1000 : weight;
          return sum + weightInKg * product.quantity;
        }, 0) || 0;

      const totalVolume =
        order.products?.reduce((sum: number, product: ProductItem) => {
          if (!product.dimensions) return sum;
          const volume =
            (product.dimensions.length *
              product.dimensions.width *
              product.dimensions.height) /
            1000000;
          return sum + volume * product.quantity;
        }, 0) || 0;

      const specialHandling = {
        fragile:
          order.products?.some(
            (p: ProductItem) => p.special_handling?.fragile
          ) || false,
        perishable:
          order.products?.some(
            (p: ProductItem) => p.special_handling?.perishable
          ) || false,
        valuable:
          order.products?.some(
            (p: ProductItem) => p.special_handling?.valuable
          ) || false,
        hazardous:
          order.products?.some(
            (p: ProductItem) => p.special_handling?.hazardous
          ) || false,
        refrigerated:
          order.products?.some(
            (p: ProductItem) => p.special_handling?.refrigerated
          ) || false,
        oversized:
          order.products?.some(
            (p: ProductItem) => p.special_handling?.oversized
          ) || false,
      };

      // Get available vehicles
      const vehicleResult =
        await VehicleFleetManagementService.getAvailableVehicles({
          weight_kg: totalWeight,
          volume_m3: totalVolume,
          special_handling: specialHandling,
          pickup_location: order.pickup_address?.coordinates || {
            lat: 19.4326,
            lng: -99.1332,
          }, // Default to Mexico City
          delivery_date: order.delivery_date,
          delivery_time_slot: order.delivery_time_slot,
        });

      if (vehicleResult.success && vehicleResult.data) {
        // Add efficiency score to each option
        const enhancedOptions: VehicleOption[] = vehicleResult.data.map(
          option => ({
            ...option,
            efficiency_score: calculateEfficiencyScore(
              option,
              totalWeight,
              totalVolume
            ),
          })
        );

        // Sort by efficiency score
        enhancedOptions.sort((a, b) => b.efficiency_score - a.efficiency_score);
        setVehicleOptions(enhancedOptions);
      }

      // Get available drivers
      const { data: drivers, error: driversError } = await supabase
        .from('drivers')
        .select('*')
        .eq('status', 'available')
        .order('full_name');

      if (!driversError && drivers) {
        setAvailableDrivers(drivers);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  }, [orderId, supabase]);

  // Calculate efficiency score for vehicle options
  const calculateEfficiencyScore = (
    option: VehicleOptionFromService,
    weight: number,
    volume: number
  ): number => {
    const { vehicle, estimated_cost, distance_to_pickup } = option;

    // Base score from capacity utilization (higher is better)
    const weightUtilization = Math.min(weight / vehicle.max_weight_kg, 1);
    const volumeUtilization = Math.min(volume / vehicle.max_volume_m3, 1);
    const capacityScore = ((weightUtilization + volumeUtilization) / 2) * 40;

    // Cost efficiency (lower cost is better)
    const costScore = Math.max(0, 30 - estimated_cost / 100);

    // Distance efficiency (closer is better)
    const distanceScore = Math.max(0, 20 - distance_to_pickup);

    // Vehicle category bonus
    const categoryBonus =
      vehicle.category === 'light' ? 10 : vehicle.category === 'medium' ? 5 : 0;

    return Math.round(
      capacityScore + costScore + distanceScore + categoryBonus
    );
  };

  // Handle vehicle assignment
  const handleAssignVehicle = async () => {
    if (!selectedVehicle || !selectedDriver) {
      alert('Por favor selecciona un vehículo y un conductor');
      return;
    }

    setIsAssigning(true);

    try {
      const result = await VehicleFleetManagementService.assignVehicleToOrder(
        orderId,
        selectedVehicle,
        selectedDriver,
        routeOptimization
      );

      if (result.success) {
        alert('Vehículo asignado exitosamente');
        router.push('/admin/orders');
      } else {
        throw new Error(result.error || 'Error al asignar vehículo');
      }
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Error al asignar vehículo');
    } finally {
      setIsAssigning(false);
    }
  };

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
      return;
    }

    if (user && orderId) {
      loadOrderData();
    }
  }, [user, loading, orderId, loadOrderData, router]);

  if (loading || isLoading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600'></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <Card>
          <CardContent className='p-8 text-center'>
            <div className='text-red-600 text-xl mb-4'>❌ Error</div>
            <p className='text-gray-600 mb-4'>{error}</p>
            <Button onClick={() => router.push('/admin/orders')}>
              Volver a Pedidos
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!orderDetails) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <Card>
          <CardContent className='p-8 text-center'>
            <div className='text-gray-600 text-xl mb-4'>
              📦 Pedido no encontrado
            </div>
            <Button onClick={() => router.push('/admin/orders')}>
              Volver a Pedidos
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='max-w-6xl mx-auto space-y-6'>
        {/* Header */}
        <div className='flex justify-between items-start'>
          <div>
            <h1 className='text-3xl font-bold text-gray-900 mb-2'>
              🚛 Asignar Vehículo
            </h1>
            <p className='text-gray-600'>
              Pedido:{' '}
              <span className='font-mono font-semibold'>
                {orderDetails.tracking_number}
              </span>
            </p>
          </div>
          <Button
            variant='outline'
            onClick={() => router.push('/admin/orders')}
          >
            ← Volver a Pedidos
          </Button>
        </div>

        {/* Order Summary */}
        <Card>
          <CardHeader>
            <CardTitle>📋 Resumen del Pedido</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <div>
                <h4 className='font-semibold mb-2'>Cliente</h4>
                <p>{orderDetails.customer_name}</p>
                <p className='text-sm text-gray-600'>
                  {orderDetails.customer_phone}
                </p>
              </div>
              <div>
                <h4 className='font-semibold mb-2'>Entrega</h4>
                <p>{orderDetails.delivery_date}</p>
                <p className='text-sm text-gray-600'>
                  {orderDetails.delivery_time_slot}
                </p>
              </div>
              <div>
                <h4 className='font-semibold mb-2'>Total</h4>
                <p className='text-xl font-bold text-green-600'>
                  ${orderDetails.total_cost?.toFixed(2) || '0.00'} MXN
                </p>
              </div>
            </div>

            <div className='mt-4'>
              <h4 className='font-semibold mb-2'>
                Productos ({orderDetails.products?.length || 0})
              </h4>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
                {orderDetails.products?.map((product, index) => (
                  <div key={index} className='text-sm bg-gray-50 p-2 rounded'>
                    <span className='font-medium'>{product.name}</span>
                    <span className='text-gray-600 ml-2'>
                      {product.quantity} {product.unit_measure}
                    </span>
                    {product.special_handling &&
                      Object.values(product.special_handling).some(Boolean) && (
                        <div className='text-xs text-orange-600 mt-1'>
                          Manejo especial requerido
                        </div>
                      )}
                  </div>
                ))}
              </div>
            </div>

            {orderDetails.stops && orderDetails.stops.length > 0 && (
              <div className='mt-4'>
                <h4 className='font-semibold mb-2'>
                  Paradas Múltiples ({orderDetails.stops.length})
                </h4>
                <div className='text-sm text-blue-600'>
                  🗺️ Este pedido requiere múltiples paradas de entrega
                </div>
              </div>
            )}

            {orderDetails.special_instructions && (
              <div className='mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3'>
                <h4 className='font-semibold text-yellow-800 mb-2'>
                  📝 Instrucciones Especiales
                </h4>
                <p className='text-yellow-700'>
                  {orderDetails.special_instructions}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Vehicle Options */}
        <Card>
          <CardHeader>
            <CardTitle>🚚 Opciones de Vehículos Disponibles</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {vehicleOptions.map((option, index) => (
                <div
                  key={option.vehicle.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    selectedVehicle === option.availability.vehicle_id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() =>
                    setSelectedVehicle(option.availability.vehicle_id)
                  }
                >
                  <div className='flex items-start justify-between mb-3'>
                    <div className='flex items-center space-x-3'>
                      <input
                        type='radio'
                        name='vehicle'
                        checked={
                          selectedVehicle === option.availability.vehicle_id
                        }
                        onChange={() =>
                          setSelectedVehicle(option.availability.vehicle_id)
                        }
                        className='mt-1'
                      />
                      <div>
                        <h3 className='font-semibold'>{option.vehicle.name}</h3>
                        <p className='text-sm text-gray-600'>
                          {option.vehicle.description}
                        </p>
                      </div>
                    </div>
                    <div className='flex items-center space-x-2'>
                      {index === 0 && (
                        <Badge className='bg-green-100 text-green-800'>
                          RECOMENDADO
                        </Badge>
                      )}
                      <Badge variant='outline'>
                        Eficiencia: {option.efficiency_score}%
                      </Badge>
                    </div>
                  </div>

                  <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
                    <div>
                      <strong>Capacidad:</strong>
                      <br />
                      {option.vehicle.max_weight_kg} kg
                      <br />
                      {option.vehicle.max_volume_m3} m³
                    </div>
                    <div>
                      <strong>Costo Estimado:</strong>
                      <br />${option.estimated_cost.toFixed(2)} MXN
                    </div>
                    <div>
                      <strong>Distancia:</strong>
                      <br />
                      {option.distance_to_pickup.toFixed(1)} km
                    </div>
                    <div>
                      <strong>Categoría:</strong>
                      <br />
                      <Badge variant='secondary'>
                        {option.vehicle.category}
                      </Badge>
                    </div>
                  </div>

                  {option.vehicle.special_capabilities &&
                    option.vehicle.special_capabilities.length > 0 && (
                      <div className='mt-3'>
                        <strong className='text-sm'>
                          Capacidades especiales:
                        </strong>
                        <div className='flex flex-wrap gap-1 mt-1'>
                          {option.vehicle.special_capabilities.map(
                            (capability: string) => (
                              <Badge
                                key={capability}
                                variant='outline'
                                className='text-xs'
                              >
                                {capability}
                              </Badge>
                            )
                          )}
                        </div>
                      </div>
                    )}
                </div>
              ))}

              {vehicleOptions.length === 0 && (
                <div className='text-center py-8 text-gray-600'>
                  No hay vehículos disponibles que cumplan con los requisitos
                  del pedido
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Driver and Route Selection */}
        {selectedVehicle && (
          <Card>
            <CardHeader>
              <CardTitle>👨‍💼 Selección de Conductor y Ruta</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Conductor Disponible
                  </label>
                  <Select
                    value={selectedDriver}
                    onValueChange={setSelectedDriver}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Seleccionar conductor' />
                    </SelectTrigger>
                    <SelectContent>
                      {availableDrivers.map(driver => (
                        <SelectItem key={driver.id} value={driver.id}>
                          {driver.full_name} - {driver.phone}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Optimización de Ruta
                  </label>
                  <Select
                    value={routeOptimization}
                    onValueChange={(
                      value: 'balanced' | 'fastest' | 'shortest' | 'eco'
                    ) => setRouteOptimization(value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='balanced'>
                        Balanceada (Recomendado)
                      </SelectItem>
                      <SelectItem value='fastest'>Más Rápida</SelectItem>
                      <SelectItem value='shortest'>Más Corta</SelectItem>
                      <SelectItem value='eco'>Eco-Friendly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className='mt-6 flex justify-end space-x-4'>
                <Button
                  variant='outline'
                  onClick={() => router.push('/admin/orders')}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleAssignVehicle}
                  disabled={!selectedVehicle || !selectedDriver || isAssigning}
                >
                  {isAssigning ? 'Asignando...' : 'Asignar Vehículo'}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
