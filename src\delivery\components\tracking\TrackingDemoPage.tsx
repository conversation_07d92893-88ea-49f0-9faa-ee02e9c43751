'use client';

import { useState } from 'react';
import { RealTimeTracking } from './RealTimeTracking';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

export function TrackingDemoPage() {
  const [orderId, setOrderId] = useState('');
  const [submittedOrderId, setSubmittedOrderId] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (orderId) {
      setSubmittedOrderId(orderId);
    }
  };

  return (
    <div className='container mx-auto py-8 space-y-8'>
      <div className='text-center'>
        <h1 className='text-3xl font-bold text-gray-900 mb-2'>
          Sistema de Seguimiento en Tiempo Real
        </h1>
        <p className='text-gray-600'>
          Seguimiento de pedidos en tiempo real para logística mexicana
        </p>
      </div>

      {!submittedOrderId ? (
        <Card className='max-w-md mx-auto'>
          <CardHeader>
            <CardTitle>🔍 Buscar Pedido</CardTitle>
            <CardDescription>
              Ingresa el ID del pedido para ver su seguimiento
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className='space-y-4'>
              <div className='space-y-2'>
                <Label htmlFor='orderId'>ID del Pedido</Label>
                <Input
                  id='orderId'
                  value={orderId}
                  onChange={e => setOrderId(e.target.value)}
                  placeholder='ej., 123e4567-e89b-12d3-a456-426614174000'
                  required
                />
              </div>
              <Button type='submit' className='w-full'>
                Seguir Pedido
              </Button>
            </form>
          </CardContent>
        </Card>
      ) : (
        <div className='space-y-6'>
          <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-bold text-gray-900'>
              Seguimiento del Pedido: {submittedOrderId}
            </h2>
            <Button variant='outline' onClick={() => setSubmittedOrderId('')}>
              Buscar Otro Pedido
            </Button>
          </div>

          <RealTimeTracking
            orderId={submittedOrderId}
            className='max-w-6xl mx-auto'
          />
        </div>
      )}

      {/* Information Section */}
      <Card>
        <CardHeader>
          <CardTitle>ℹ️ Acerca del Sistema de Seguimiento</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div>
              <h3 className='font-semibold text-lg mb-2'>Características</h3>
              <ul className='space-y-2 text-gray-600'>
                <li className='flex items-start'>
                  <span className='text-green-500 mr-2'>✓</span>
                  <span>Ubicación en tiempo real del repartidor</span>
                </li>
                <li className='flex items-start'>
                  <span className='text-green-500 mr-2'>✓</span>
                  <span>Progreso actualizado del pedido</span>
                </li>
                <li className='flex items-start'>
                  <span className='text-green-500 mr-2'>✓</span>
                  <span>Integración con mapas de México</span>
                </li>
                <li className='flex items-start'>
                  <span className='text-green-500 mr-2'>✓</span>
                  <span>Notificaciones de estado</span>
                </li>
                <li className='flex items-start'>
                  <span className='text-green-500 mr-2'>✓</span>
                  <span>Historial de ubicaciones</span>
                </li>
              </ul>
            </div>
            <div>
              <h3 className='font-semibold text-lg mb-2'>Beneficios</h3>
              <ul className='space-y-2 text-gray-600'>
                <li className='flex items-start'>
                  <span className='text-blue-500 mr-2'>•</span>
                  <span>Transparencia para el cliente</span>
                </li>
                <li className='flex items-start'>
                  <span className='text-blue-500 mr-2'>•</span>
                  <span>Optimización de rutas</span>
                </li>
                <li className='flex items-start'>
                  <span className='text-blue-500 mr-2'>•</span>
                  <span>Mejora en la eficiencia de entregas</span>
                </li>
                <li className='flex items-start'>
                  <span className='text-blue-500 mr-2'>•</span>
                  <span>Reducción de tiempos de espera</span>
                </li>
                <li className='flex items-start'>
                  <span className='text-blue-500 mr-2'>•</span>
                  <span>Mejor servicio al cliente</span>
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
