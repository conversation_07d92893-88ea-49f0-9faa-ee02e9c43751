'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  CreditCard,
  User,
  Mail,
  Phone,
  Wallet,
  Plus,
  X,
  Edit,
} from 'lucide-react';
import { createClient } from '@/utils/supabase/client';

export default function ProfilePage() {
  const { user, profile, loading, isCustomer } = useAuthStore();
  const router = useRouter();
  const [balance, setBalance] = useState(0);
  const [showAddBalanceModal, setShowAddBalanceModal] = useState(false);
  const [amount, setAmount] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editForm, setEditForm] = useState({
    full_name: '',
    phone: '',
    preferred_name: '',
    emergency_contact_name: '',
    emergency_contact_phone: '',
    preferred_delivery_time: 'any',
  });
  const [customerProfile, setCustomerProfile] = useState<{
    preferred_name?: string;
    emergency_contact_name?: string;
    emergency_contact_phone?: string;
    preferred_delivery_time?: string;
  } | null>(null);

  const supabase = useMemo(() => createClient(), []);

  const fetchCustomerData = useCallback(async () => {
    if (!user) return;

    try {
      // Fetch customer profile
      const { data: customerData, error: customerError } = await supabase
        .from('customer_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (customerError && customerError.code !== 'PGRST116') {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error fetching customer profile:', customerError);
        }
      } else if (customerData) {
        setCustomerProfile(customerData);
      }

      // Use the balance API for comprehensive data including transaction history
      const response = await fetch('/api/user/balance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id }),
      });

      if (response.ok) {
        const balanceData = await response.json();
        setBalance(balanceData.balance || 0);
        setTransactionHistory(balanceData.recent_transactions || []);
      } else {
        // Fallback to direct Supabase query
        const { data: walletData, error: walletError } = await supabase
          .from('user_wallets')
          .select('balance')
          .eq('user_id', user.id)
          .single();

        if (walletError && walletError.code !== 'PGRST116') {
          if (process.env.NODE_ENV === 'development') {
            console.error('Error fetching wallet:', walletError);
          }
        } else if (walletData) {
          setBalance(parseFloat(walletData.balance) || 0);
        }
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching customer data:', error);
      }
    }
  }, [user, supabase]);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/');
      return;
    }

    if (!loading && profile && !isCustomer) {
      router.push('/dashboard');
      return;
    }

    // Fetch customer profile and wallet balance
    fetchCustomerData();

    // Refresh balance when returning from payment (detect URL parameters)
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('payment_success') === 'true') {
      // Remove the parameter and refresh balance after a short delay
      window.history.replaceState({}, '', window.location.pathname);
      setTimeout(fetchCustomerData, 1000);
    }
  }, [user, profile, loading, isCustomer, fetchCustomerData, router]);

  // Add transaction history state
  const [transactionHistory, setTransactionHistory] = useState<
    Array<{
      id: string;
      type: string;
      amount: number;
      description: string;
      status: string;
      stripe_payment_intent_id: string | null;
      created_at: string;
      transaction_ref: string | null;
    }>
  >([]);

  // Separate useEffect to update form when profile or customerProfile changes
  useEffect(() => {
    if (profile) {
      setEditForm({
        full_name: profile.full_name || '',
        phone: profile.phone || '',
        preferred_name: customerProfile?.preferred_name || '',
        emergency_contact_name: customerProfile?.emergency_contact_name || '',
        emergency_contact_phone: customerProfile?.emergency_contact_phone || '',
        preferred_delivery_time:
          customerProfile?.preferred_delivery_time || 'any',
      });
    }
  }, [profile, customerProfile]);

  const handleAddBalance = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      alert('Please enter a valid amount');
      return;
    }

    if (!user) {
      alert('User not authenticated');
      return;
    }

    setIsSubmitting(true);

    try {
      // Create Stripe payment link
      const response = await fetch('/api/stripe/create-payment-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: parseFloat(amount),
          userId: user.id,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create payment link');
      }

      // Redirect to Stripe Checkout
      window.location.href = data.paymentLink.url;
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error creating payment link:', error);
      }
      alert('Failed to create payment link. Please try again.');
      setIsSubmitting(false);
    }
  };

  const handleModalClose = () => {
    setShowAddBalanceModal(false);
    setAmount('');
  };

  const handleEditProfile = async () => {
    if (!user) return;

    setIsSubmitting(true);
    try {
      // Update profiles table
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          full_name: editForm.full_name,
          phone: editForm.phone,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (profileError) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error updating profile:', profileError);
        }
        alert('Error updating profile. Please try again.');
        return;
      }

      // Update customer_profiles table - use update instead of upsert to avoid duplicates
      const { error: customerError } = await supabase
        .from('customer_profiles')
        .update({
          preferred_name: editForm.preferred_name,
          emergency_contact_name: editForm.emergency_contact_name,
          emergency_contact_phone: editForm.emergency_contact_phone,
          preferred_delivery_time: editForm.preferred_delivery_time,

          updated_at: new Date().toISOString(),
        })
        .eq('user_id', user.id);

      if (customerError) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error updating customer profile:', customerError);
        }
        alert('Error updating customer profile. Please try again.');
        return;
      }

      // Close modal and refresh data
      setShowEditModal(false);
      window.location.reload();
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error updating profile:', error);
      }
      alert('Error updating profile. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditModalClose = () => {
    setShowEditModal(false);
    // Form will be reset by the useEffect when profile/customerProfile changes
  };

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-black'></div>
      </div>
    );
  }

  if (!user || !profile || !isCustomer) {
    return null;
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Header */}
      <header className='bg-white shadow-sm border-b'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center h-16'>
            <div className='flex items-center space-x-4'>
              <Button
                variant='ghost'
                onClick={() => router.push('/customer/dashboard')}
                className='text-blue-600'
              >
                ← Volver al Panel
              </Button>
              <h1 className='text-xl font-bold text-black'>Mi Perfil</h1>
            </div>
            <Badge variant='outline'>Cliente</Badge>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8'>
        <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
          {/* Profile Information */}
          <div className='lg:col-span-2 space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <User className='w-5 h-5' />
                  Información Personal
                </CardTitle>
                <CardDescription>
                  Tu información de perfil y contacto
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <div>
                    <Label className='text-sm font-medium text-gray-700'>
                      Nombre Completo
                    </Label>
                    <div className='mt-1 p-3 bg-gray-50 rounded-lg'>
                      <p className='text-gray-900'>
                        {profile.full_name || 'No especificado'}
                      </p>
                    </div>
                  </div>
                  <div>
                    <Label className='text-sm font-medium text-gray-700'>
                      Correo Electrónico
                    </Label>
                    <div className='mt-1 p-3 bg-gray-50 rounded-lg flex items-center gap-2'>
                      <Mail className='w-4 h-4 text-gray-500' />
                      <p className='text-gray-900'>{profile.email}</p>
                    </div>
                  </div>
                  <div>
                    <Label className='text-sm font-medium text-gray-700'>
                      Teléfono
                    </Label>
                    <div className='mt-1 p-3 bg-gray-50 rounded-lg flex items-center gap-2'>
                      <Phone className='w-4 h-4 text-gray-500' />
                      <p className='text-gray-900'>
                        {profile.phone || 'No especificado'}
                      </p>
                    </div>
                  </div>
                  <div>
                    <Label className='text-sm font-medium text-gray-700'>
                      Tipo de Usuario
                    </Label>
                    <div className='mt-1 p-3 bg-gray-50 rounded-lg'>
                      <Badge variant='outline' className='capitalize'>
                        {profile.role}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <Label className='text-sm font-medium text-gray-700'>
                      Nombre Preferido
                    </Label>
                    <div className='mt-1 p-3 bg-gray-50 rounded-lg'>
                      <p className='text-gray-900'>
                        {customerProfile?.preferred_name || 'No especificado'}
                      </p>
                    </div>
                  </div>
                  <div>
                    <Label className='text-sm font-medium text-gray-700'>
                      Horario Preferido de Entrega
                    </Label>
                    <div className='mt-1 p-3 bg-gray-50 rounded-lg'>
                      <p className='text-gray-900'>
                        {customerProfile?.preferred_delivery_time === 'morning'
                          ? 'Mañana (8:00 - 12:00)'
                          : customerProfile?.preferred_delivery_time ===
                              'afternoon'
                            ? 'Tarde (12:00 - 18:00)'
                            : customerProfile?.preferred_delivery_time ===
                                'evening'
                              ? 'Noche (18:00 - 22:00)'
                              : customerProfile?.preferred_delivery_time ===
                                  'any'
                                ? 'Cualquier hora'
                                : 'No especificado'}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Emergency Contact Information */}
                {(customerProfile?.emergency_contact_name ||
                  customerProfile?.emergency_contact_phone) && (
                  <div className='pt-4 border-t'>
                    <h4 className='text-sm font-medium text-gray-700 mb-3'>
                      Información de Emergencia
                    </h4>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                      {customerProfile?.emergency_contact_name && (
                        <div>
                          <Label className='text-sm font-medium text-gray-700'>
                            Contacto de Emergencia
                          </Label>
                          <div className='mt-1 p-3 bg-gray-50 rounded-lg'>
                            <p className='text-gray-900'>
                              {customerProfile.emergency_contact_name}
                            </p>
                          </div>
                        </div>
                      )}
                      {customerProfile?.emergency_contact_phone && (
                        <div>
                          <Label className='text-sm font-medium text-gray-700'>
                            Teléfono de Emergencia
                          </Label>
                          <div className='mt-1 p-3 bg-gray-50 rounded-lg flex items-center gap-2'>
                            <Phone className='w-4 h-4 text-gray-500' />
                            <p className='text-gray-900'>
                              {customerProfile.emergency_contact_phone}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className='pt-4 border-t'>
                  <Button
                    variant='outline'
                    onClick={() => setShowEditModal(true)}
                    className='w-full md:w-auto'
                  >
                    <Edit className='w-4 h-4 mr-2' />
                    Editar Información
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Account Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Actividad de la Cuenta</CardTitle>
                <CardDescription>
                  Información sobre tu cuenta y actividad reciente
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <div className='p-3 bg-gray-50 rounded-lg'>
                    <p className='text-sm text-gray-600'>Miembro desde</p>
                    <p className='font-medium'>
                      {new Date(profile.created_at).toLocaleDateString(
                        'es-MX',
                        {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        }
                      )}
                    </p>
                  </div>
                  <div className='p-3 bg-gray-50 rounded-lg'>
                    <p className='text-sm text-gray-600'>
                      Última actualización
                    </p>
                    <p className='font-medium'>
                      {new Date(profile.updated_at).toLocaleDateString(
                        'es-MX',
                        {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        }
                      )}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Balance and Wallet */}
          <div className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Wallet className='w-5 h-5' />
                  Mi Saldo
                </CardTitle>
                <CardDescription>
                  Saldo actual disponible para pedidos
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='text-center py-6'>
                  <div className='text-3xl font-bold text-green-600 mb-2'>
                    ${balance.toFixed(2)} MXN
                  </div>
                  <p className='text-sm text-gray-600 mb-4'>Saldo disponible</p>
                  <div className='space-y-2'>
                    <Button
                      onClick={() => setShowAddBalanceModal(true)}
                      className='w-full flex items-center gap-2'
                    >
                      <Plus className='w-4 h-4' />
                      Agregar Saldo
                    </Button>
                    <Button
                      variant='outline'
                      onClick={() => window.location.reload()}
                      className='w-full flex items-center gap-2 text-xs'
                      size='sm'
                    >
                      <Wallet className='w-3 h-3' />
                      Actualizar Saldo
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Estadísticas Rápidas</CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='flex justify-between items-center'>
                  <span className='text-sm text-gray-600'>Pedidos totales</span>
                  <span className='font-medium'>0</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-sm text-gray-600'>
                    Gastado este mes
                  </span>
                  <span className='font-medium'>$0.00</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-sm text-gray-600'>
                    Pedidos completados
                  </span>
                  <span className='font-medium'>0</span>
                </div>
              </CardContent>
            </Card>

            {/* Recent Transactions */}
            {transactionHistory.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <CreditCard className='w-5 h-5' />
                    Transacciones Recientes
                  </CardTitle>
                  <CardDescription>
                    Historial de movimientos en tu billetera
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='space-y-3 max-h-64 overflow-y-auto'>
                    {transactionHistory.slice(0, 5).map(tx => (
                      <div
                        key={tx.id}
                        className='flex items-center justify-between p-3 border rounded-lg'
                      >
                        <div className='flex-1'>
                          <div className='flex items-center gap-2'>
                            <span
                              className={`font-medium ${
                                tx.status === 'failed'
                                  ? 'text-red-600'
                                  : tx.status === 'pending'
                                    ? 'text-yellow-600'
                                    : tx.type === 'deposit'
                                      ? 'text-green-600'
                                      : 'text-red-600'
                              }`}
                            >
                              {tx.type === 'deposit' ? '+' : '-'}$
                              {tx.amount.toFixed(2)} MXN
                            </span>
                            <span
                              className={`text-xs px-2 py-1 rounded-full ${
                                tx.status === 'completed'
                                  ? 'bg-green-100 text-green-700'
                                  : tx.status === 'pending'
                                    ? 'bg-yellow-100 text-yellow-700'
                                    : 'bg-red-100 text-red-700'
                              }`}
                            >
                              {tx.status}
                            </span>
                          </div>
                          <p className='text-sm text-gray-600 mt-1'>
                            {tx.description}
                          </p>
                          <div className='flex items-center gap-4 mt-2 text-xs text-gray-500'>
                            <span>
                              {new Date(tx.created_at).toLocaleDateString(
                                'es-MX',
                                {
                                  year: 'numeric',
                                  month: 'short',
                                  day: 'numeric',
                                  hour: '2-digit',
                                  minute: '2-digit',
                                }
                              )}
                            </span>
                            {tx.transaction_ref && (
                              <span>Ref: {tx.transaction_ref}</span>
                            )}
                          </div>
                        </div>
                        {tx.stripe_payment_intent_id && (
                          <div className='text-xs text-blue-600 flex items-center gap-1'>
                            <CreditCard className='w-3 h-3' />
                            <span>Stripe</span>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                  {transactionHistory.length > 5 && (
                    <div className='mt-3 text-center'>
                      <p className='text-sm text-gray-500'>
                        Mostrando las 5 transacciones más recientes
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </main>

      {/* Add Balance Modal */}
      {showAddBalanceModal && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50'>
          <div className='bg-white rounded-lg max-w-md w-full p-6'>
            <div className='flex items-center justify-between mb-4'>
              <h3 className='text-lg font-semibold'>Agregar Saldo</h3>
              <Button
                variant='ghost'
                size='sm'
                onClick={handleModalClose}
                className='h-8 w-8 p-0'
              >
                <X className='w-4 h-4' />
              </Button>
            </div>

            <div className='space-y-4'>
              <div>
                <Label htmlFor='amount' className='text-sm font-medium'>
                  Cantidad (MXN)
                </Label>
                <Input
                  id='amount'
                  type='number'
                  placeholder='0.00'
                  value={amount}
                  onChange={e => setAmount(e.target.value)}
                  className='mt-1'
                  min='1'
                  step='0.01'
                />
                <p className='text-xs text-gray-500 mt-1'>Mínimo: $1.00 MXN</p>
              </div>

              <div className='bg-blue-50 p-4 rounded-lg'>
                <div className='flex items-center gap-2 mb-2'>
                  <CreditCard className='w-4 h-4 text-blue-600' />
                  <span className='text-sm font-medium text-blue-900'>
                    Método de Pago
                  </span>
                </div>
                <p className='text-sm text-blue-700'>
                  Se procesará con Stripe Checkout de forma segura
                </p>
              </div>

              <div className='flex gap-3 pt-4'>
                <Button
                  variant='outline'
                  onClick={handleModalClose}
                  className='flex-1'
                  disabled={isSubmitting}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleAddBalance}
                  className='flex-1'
                  disabled={isSubmitting || !amount || parseFloat(amount) <= 0}
                >
                  {isSubmitting ? (
                    <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
                  ) : (
                    'Agregar Saldo'
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Profile Modal */}
      {showEditModal && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50'>
          <div className='bg-white rounded-lg max-w-md w-full p-6'>
            <div className='flex items-center justify-between mb-4'>
              <h3 className='text-lg font-semibold'>Editar Información</h3>
              <Button
                variant='ghost'
                size='sm'
                onClick={handleEditModalClose}
                className='h-8 w-8 p-0'
              >
                <X className='w-4 h-4' />
              </Button>
            </div>

            <div className='space-y-4 max-h-96 overflow-y-auto'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <Label htmlFor='full_name' className='text-sm font-medium'>
                    Nombre Completo
                  </Label>
                  <Input
                    id='full_name'
                    type='text'
                    placeholder='Tu nombre completo'
                    value={editForm.full_name}
                    onChange={e =>
                      setEditForm({ ...editForm, full_name: e.target.value })
                    }
                    className='mt-1'
                  />
                </div>

                <div>
                  <Label
                    htmlFor='preferred_name'
                    className='text-sm font-medium'
                  >
                    Nombre Preferido
                  </Label>
                  <Input
                    id='preferred_name'
                    type='text'
                    placeholder='¿Cómo te gusta que te llamen?'
                    value={editForm.preferred_name}
                    onChange={e =>
                      setEditForm({
                        ...editForm,
                        preferred_name: e.target.value,
                      })
                    }
                    className='mt-1'
                  />
                </div>
              </div>

              <div>
                <Label htmlFor='phone' className='text-sm font-medium'>
                  Teléfono
                </Label>
                <Input
                  id='phone'
                  type='tel'
                  placeholder='Tu número de teléfono'
                  value={editForm.phone}
                  onChange={e =>
                    setEditForm({ ...editForm, phone: e.target.value })
                  }
                  className='mt-1'
                />
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <Label
                    htmlFor='emergency_contact_name'
                    className='text-sm font-medium'
                  >
                    Contacto de Emergencia
                  </Label>
                  <Input
                    id='emergency_contact_name'
                    type='text'
                    placeholder='Nombre del contacto'
                    value={editForm.emergency_contact_name}
                    onChange={e =>
                      setEditForm({
                        ...editForm,
                        emergency_contact_name: e.target.value,
                      })
                    }
                    className='mt-1'
                  />
                </div>

                <div>
                  <Label
                    htmlFor='emergency_contact_phone'
                    className='text-sm font-medium'
                  >
                    Teléfono de Emergencia
                  </Label>
                  <Input
                    id='emergency_contact_phone'
                    type='tel'
                    placeholder='Teléfono del contacto'
                    value={editForm.emergency_contact_phone}
                    onChange={e =>
                      setEditForm({
                        ...editForm,
                        emergency_contact_phone: e.target.value,
                      })
                    }
                    className='mt-1'
                  />
                </div>
              </div>

              <div>
                <Label
                  htmlFor='preferred_delivery_time'
                  className='text-sm font-medium'
                >
                  Horario Preferido de Entrega
                </Label>
                <select
                  id='preferred_delivery_time'
                  value={editForm.preferred_delivery_time}
                  onChange={e =>
                    setEditForm({
                      ...editForm,
                      preferred_delivery_time: e.target.value,
                    })
                  }
                  className='mt-1 w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
                >
                  <option value='any'>Cualquier hora</option>
                  <option value='morning'>Mañana (8:00 - 12:00)</option>
                  <option value='afternoon'>Tarde (12:00 - 18:00)</option>
                  <option value='evening'>Noche (18:00 - 22:00)</option>
                </select>
              </div>

              <div className='bg-gray-50 p-4 rounded-lg'>
                <p className='text-sm text-gray-600'>
                  <strong>Nota:</strong> El correo electrónico no se puede
                  cambiar por motivos de seguridad.
                </p>
              </div>

              <div className='flex gap-3 pt-4'>
                <Button
                  variant='outline'
                  onClick={handleEditModalClose}
                  className='flex-1'
                  disabled={isSubmitting}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleEditProfile}
                  className='flex-1'
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
                  ) : (
                    'Guardar Cambios'
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
