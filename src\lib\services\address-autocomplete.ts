/**
 * Address Autocomplete Service for Mexican Addresses
 * 
 * Provides address suggestions, validation, and geolocation services
 * specifically designed for Mexican postal addresses
 */

export interface AddressSuggestion {
  id: string;
  display_name: string;
  street: string;
  number?: string;
  colony: string;
  city: string;
  state: string;
  zip: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  confidence: number; // 0-1 score
}

export interface GeolocationResult {
  success: boolean;
  coordinates?: {
    lat: number;
    lng: number;
  };
  address?: string;
  error?: string;
}

export class AddressAutocompleteService {
  private static readonly MEXICAN_STATES = [
    'Aguascalientes', 'Baja California', 'Baja California Sur', 'Campeche',
    'Chiapas', 'Chihuahua', 'Ciudad de México', 'Coahuila', 'Colima',
    'Durango', 'Estado de México', 'Guanajuato', 'Guerrero', 'Hidalgo',
    'Jalisco', 'Michoacán', 'Morelos', 'Nayarit', 'Nuevo León', 'Oaxaca',
    'Puebla', 'Querétaro', 'Quintana Roo', 'San Luis Potosí', 'Sinaloa',
    'Sonora', 'Tabasco', 'Tamaulipas', 'Tlaxcala', 'Veracruz', 'Yucatán', 'Zacatecas'
  ];

  // Mock data for demonstration - in production, this would come from APIs
  private static readonly MOCK_ADDRESSES: AddressSuggestion[] = [
    {
      id: '1',
      display_name: 'Av. Insurgentes Sur 1234, Roma Norte, Ciudad de México',
      street: 'Av. Insurgentes Sur',
      number: '1234',
      colony: 'Roma Norte',
      city: 'Ciudad de México',
      state: 'Ciudad de México',
      zip: '06700',
      coordinates: { lat: 19.4326, lng: -99.1332 },
      confidence: 0.95
    },
    {
      id: '2',
      display_name: 'Calle Madero 456, Centro Histórico, Ciudad de México',
      street: 'Calle Madero',
      number: '456',
      colony: 'Centro Histórico',
      city: 'Ciudad de México',
      state: 'Ciudad de México',
      zip: '06000',
      coordinates: { lat: 19.4342, lng: -99.1376 },
      confidence: 0.92
    },
    {
      id: '3',
      display_name: 'Av. Revolución 789, San Ángel, Ciudad de México',
      street: 'Av. Revolución',
      number: '789',
      colony: 'San Ángel',
      city: 'Ciudad de México',
      state: 'Ciudad de México',
      zip: '01000',
      coordinates: { lat: 19.3467, lng: -99.1898 },
      confidence: 0.88
    },
    {
      id: '4',
      display_name: 'Blvd. Miguel de Cervantes Saavedra 123, Granada, Ciudad de México',
      street: 'Blvd. Miguel de Cervantes Saavedra',
      number: '123',
      colony: 'Granada',
      city: 'Ciudad de México',
      state: 'Ciudad de México',
      zip: '11520',
      coordinates: { lat: 19.4521, lng: -99.1944 },
      confidence: 0.90
    },
    {
      id: '5',
      display_name: 'Av. Universidad 567, Copilco, Ciudad de México',
      street: 'Av. Universidad',
      number: '567',
      colony: 'Copilco',
      city: 'Ciudad de México',
      state: 'Ciudad de México',
      zip: '04360',
      coordinates: { lat: 19.3319, lng: -99.1839 },
      confidence: 0.87
    },
    // Guadalajara addresses
    {
      id: '6',
      display_name: 'Av. Chapultepec 890, Americana, Guadalajara, Jalisco',
      street: 'Av. Chapultepec',
      number: '890',
      colony: 'Americana',
      city: 'Guadalajara',
      state: 'Jalisco',
      zip: '44160',
      coordinates: { lat: 20.6597, lng: -103.3496 },
      confidence: 0.91
    },
    // Monterrey addresses
    {
      id: '7',
      display_name: 'Av. Constitución 234, Centro, Monterrey, Nuevo León',
      street: 'Av. Constitución',
      number: '234',
      colony: 'Centro',
      city: 'Monterrey',
      state: 'Nuevo León',
      zip: '64000',
      coordinates: { lat: 25.6866, lng: -100.3161 },
      confidence: 0.89
    },
    // Puebla addresses
    {
      id: '8',
      display_name: 'Calle 5 de Mayo 345, Centro Histórico, Puebla, Puebla',
      street: 'Calle 5 de Mayo',
      number: '345',
      colony: 'Centro Histórico',
      city: 'Puebla',
      state: 'Puebla',
      zip: '72000',
      coordinates: { lat: 19.0414, lng: -98.2063 },
      confidence: 0.86
    }
  ];

  /**
   * Search for address suggestions based on user input
   */
  static async searchAddresses(query: string, limit: number = 5): Promise<AddressSuggestion[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    if (!query || query.length < 3) {
      return [];
    }

    const normalizedQuery = query.toLowerCase().trim();
    
    // Filter mock addresses based on query
    const filtered = this.MOCK_ADDRESSES.filter(address => {
      const searchText = `${address.street} ${address.number} ${address.colony} ${address.city} ${address.state}`.toLowerCase();
      return searchText.includes(normalizedQuery);
    });

    // Sort by confidence score
    const sorted = filtered.sort((a, b) => b.confidence - a.confidence);
    
    return sorted.slice(0, limit);
  }

  /**
   * Validate a Mexican postal code
   */
  static validatePostalCode(postalCode: string): boolean {
    const cleanCode = postalCode.replace(/\s/g, '');
    return /^\d{5}$/.test(cleanCode) && parseInt(cleanCode) >= 1000 && parseInt(cleanCode) <= 99999;
  }

  /**
   * Get current user location using browser geolocation
   */
  static async getCurrentLocation(): Promise<GeolocationResult> {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        resolve({
          success: false,
          error: 'Geolocalización no soportada por este navegador'
        });
        return;
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;
          
          // Reverse geocode to get address
          try {
            const address = await this.reverseGeocode(latitude, longitude);
            resolve({
              success: true,
              coordinates: { lat: latitude, lng: longitude },
              address
            });
          } catch (error) {
            resolve({
              success: true,
              coordinates: { lat: latitude, lng: longitude },
              address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`
            });
          }
        },
        (error) => {
          let errorMessage = 'Error desconocido';
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Permiso de geolocalización denegado';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Ubicación no disponible';
              break;
            case error.TIMEOUT:
              errorMessage = 'Tiempo de espera agotado';
              break;
          }
          
          resolve({
            success: false,
            error: errorMessage
          });
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    });
  }

  /**
   * Reverse geocode coordinates to address
   */
  static async reverseGeocode(lat: number, lng: number): Promise<string> {
    try {
      // Using Nominatim for reverse geocoding (free alternative to Google Maps)
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&countrycodes=mx&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'Mouvers/1.0'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Reverse geocoding failed');
      }

      const data = await response.json();
      return data.display_name || `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    }
  }

  /**
   * Geocode an address to coordinates
   */
  static async geocodeAddress(address: string): Promise<GeolocationResult> {
    try {
      const encodedAddress = encodeURIComponent(`${address}, México`);
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodedAddress}&countrycodes=mx&limit=1`,
        {
          headers: {
            'User-Agent': 'Mouvers/1.0'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Geocoding failed');
      }

      const data = await response.json();
      
      if (data.length === 0) {
        return {
          success: false,
          error: 'Dirección no encontrada'
        };
      }

      const result = data[0];
      return {
        success: true,
        coordinates: {
          lat: parseFloat(result.lat),
          lng: parseFloat(result.lon)
        },
        address: result.display_name
      };
    } catch (error) {
      console.error('Geocoding error:', error);
      return {
        success: false,
        error: 'Error al geocodificar la dirección'
      };
    }
  }

  /**
   * Get address suggestions for a specific state
   */
  static getStateAddresses(state: string): AddressSuggestion[] {
    return this.MOCK_ADDRESSES.filter(address => 
      address.state.toLowerCase() === state.toLowerCase()
    );
  }

  /**
   * Validate if a state is valid in Mexico
   */
  static isValidMexicanState(state: string): boolean {
    return this.MEXICAN_STATES.includes(state);
  }

  /**
   * Get all Mexican states
   */
  static getMexicanStates(): string[] {
    return [...this.MEXICAN_STATES];
  }

  /**
   * Format address for display
   */
  static formatAddress(address: Partial<AddressSuggestion>): string {
    const parts = [
      address.street,
      address.number,
      address.colony,
      address.city,
      address.state
    ].filter(Boolean);
    
    return parts.join(', ');
  }

  /**
   * Calculate distance between two coordinates (Haversine formula)
   */
  static calculateDistance(
    lat1: number, lng1: number, 
    lat2: number, lng2: number
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private static toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}
