import { NextResponse } from 'next/server';
import { secureAPI, sanitizeInput } from '@/lib/security/api-security';
import { auditLog } from '@/lib/security/audit-logger';

// POST /api/cost-calculation - Calculate dynamic cost for Mexican logistics
export const POST = secureAPI(
  {
    POST: async (request, context) => {
      try {
        // Parse and sanitize request body
        let requestBody;
        try {
          requestBody = sanitizeInput(await request.json());
        } catch {
          return NextResponse.json(
            { error: 'Formato de cuerpo de solicitud inválido' },
            { status: 400 }
          );
        }

        const {
          origin,
          destination,
          vehicleTypeId,
          cargoTypeId,
          packageWeight,
          packageVolume,
          deliveryMode,
          scheduledTime,
          stops,
        } = requestBody as {
          origin?: {
            lat: number;
            lng: number;
            address: string;
          };
          destination?: {
            lat: number;
            lng: number;
            address: string;
          };
          vehicleTypeId?: string;
          cargoTypeId?: string;
          packageWeight?: number;
          packageVolume?: number;
          deliveryMode?: 'primera-milla' | 'media-milla' | 'ultima-milla';
          scheduledTime?: string;
          stops?: Array<{
            lat: number;
            lng: number;
            address: string;
          }>;
        };

        // Validate required fields
        if (!origin || !destination) {
          return NextResponse.json(
            { error: 'Se requiere origen y destino' },
            { status: 400 }
          );
        }

        // Validate coordinates
        if (
          !origin.lat ||
          !origin.lng ||
          !destination.lat ||
          !destination.lng
        ) {
          return NextResponse.json(
            { error: 'Coordenadas inválidas' },
            { status: 400 }
          );
        }

        // For testing purposes, we'll skip database calls and use mock data
        // In a real implementation, you would fetch actual vehicle and cargo type data
        const vehicleTypeData = vehicleTypeId
          ? {
              id: vehicleTypeId,
              name: 'Mock Vehicle',
              base_rate_per_km: 15,
              max_weight_kg: 100,
              max_volume_m3: 10,
            }
          : null;

        const cargoTypeData = cargoTypeId
          ? {
              id: cargoTypeId,
              name: 'Mock Cargo',
              rate_multiplier: 1.2,
            }
          : null;

        // Calculate distance (simplified - in a real implementation, you would use a mapping API)
        const distance = calculateDistance(
          origin.lat,
          origin.lng,
          destination.lat,
          destination.lng
        );

        // Base cost calculation
        let baseCost = 0;
        const costBreakdown = {
          distanceCost: 0,
          vehicleCost: 0,
          cargoCost: 0,
          deliveryModeCost: 0,
          timeCost: 0,
          stopsCost: 0,
          total: 0,
        };

        // Distance cost (MXN per km)
        const distanceRate = 15; // Base rate per km
        costBreakdown.distanceCost = distance * distanceRate;
        baseCost += costBreakdown.distanceCost;

        // Vehicle type cost
        if (vehicleTypeData) {
          // Use vehicle's base rate if available, otherwise default to distance rate
          const vehicleRate = vehicleTypeData.base_rate_per_km || distanceRate;
          costBreakdown.vehicleCost = distance * vehicleRate;
          baseCost = costBreakdown.vehicleCost; // Override base cost with vehicle-specific rate
        }

        // Cargo type cost
        if (cargoTypeData) {
          const cargoMultiplier = cargoTypeData.rate_multiplier || 1;
          costBreakdown.cargoCost = baseCost * (cargoMultiplier - 1);
          baseCost *= cargoMultiplier;
        }

        // Delivery mode cost
        const deliveryModeMultipliers = {
          'primera-milla': 1.2,
          'media-milla': 1.0,
          'ultima-milla': 1.3,
        };
        const modeMultiplier =
          deliveryModeMultipliers[deliveryMode || 'media-milla'];
        costBreakdown.deliveryModeCost = baseCost * (modeMultiplier - 1);
        baseCost *= modeMultiplier;

        // Time-based cost (peak hours, night delivery, etc.)
        let timeMultiplier = 1.0;
        if (scheduledTime) {
          const scheduledHour = new Date(scheduledTime).getHours();
          // Night delivery (8 PM - 6 AM) costs more
          if (scheduledHour >= 20 || scheduledHour < 6) {
            timeMultiplier = 1.3;
          }
          // Peak hours (7-9 AM, 6-8 PM) cost more
          else if (
            (scheduledHour >= 7 && scheduledHour <= 9) ||
            (scheduledHour >= 18 && scheduledHour <= 20)
          ) {
            timeMultiplier = 1.2;
          }
        }
        costBreakdown.timeCost = baseCost * (timeMultiplier - 1);
        baseCost *= timeMultiplier;

        // Multi-stop cost
        const stopsCount = stops?.length || 0;
        const stopsMultiplier = 1 + stopsCount * 0.15; // 15% more per stop
        costBreakdown.stopsCost = baseCost * (stopsMultiplier - 1);
        baseCost *= stopsMultiplier;

        // Weight and volume surcharges
        let weightSurcharge = 0;
        let volumeSurcharge = 0;

        if (packageWeight && vehicleTypeData) {
          // If package exceeds 80% of vehicle capacity, add surcharge
          if (packageWeight > vehicleTypeData.max_weight_kg * 0.8) {
            weightSurcharge = baseCost * 0.1; // 10% surcharge
          }
        }

        if (packageVolume && vehicleTypeData) {
          // If package exceeds 80% of vehicle capacity, add surcharge
          if (packageVolume > vehicleTypeData.max_volume_m3 * 0.8) {
            volumeSurcharge = baseCost * 0.1; // 10% surcharge
          }
        }

        baseCost += weightSurcharge + volumeSurcharge;

        // Apply minimum cost
        const minimumCost = 150; // MXN
        const finalCost = Math.max(baseCost, minimumCost);

        // Update cost breakdown with final values
        costBreakdown.total = finalCost;

        const result = {
          success: true,
          data: {
            totalCost: parseFloat(finalCost.toFixed(2)),
            currency: 'MXN',
            breakdown: costBreakdown,
            distance: parseFloat(distance.toFixed(2)),
            estimatedDeliveryTime: calculateEstimatedDeliveryTime(
              distance,
              deliveryMode
            ),
          },
        };

        await auditLog({
          event: 'COST_CALCULATED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            origin,
            destination,
            vehicleTypeId,
            cargoTypeId,
            packageWeight,
            packageVolume,
            deliveryMode,
            stopsCount,
            result,
          },
          severity: 'LOW',
          category: 'DATA',
        });

        return NextResponse.json(result);
      } catch (error) {
        await auditLog({
          event: 'COST_CALCULATION_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: false,
    rateLimitRpm: 60,
  }
);

// GET /api/cost-calculation - Get cost calculation parameters
export const GET = secureAPI(
  {
    GET: async (request, context) => {
      try {
        // Delivery mode multipliers
        const deliveryModeMultipliers = {
          'primera-milla': 1.2,
          'media-milla': 1.0,
          'ultima-milla': 1.3,
        };

        // Time-based multipliers
        const timeMultipliers = {
          night: 1.3, // 8 PM - 6 AM
          peak: 1.2, // 7-9 AM, 6-8 PM
          normal: 1.0,
        };

        // Stops multiplier
        const stopsMultiplier = 0.15; // 15% per stop

        const result = {
          success: true,
          data: {
            deliveryModes: deliveryModeMultipliers,
            timeMultipliers,
            stopsMultiplier,
            baseRatePerKm: 15,
            minimumCost: 150,
            currency: 'MXN',
          },
        };

        await auditLog({
          event: 'COST_PARAMETERS_FETCHED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: result,
          severity: 'LOW',
          category: 'DATA',
        });

        return NextResponse.json(result);
      } catch (error) {
        await auditLog({
          event: 'COST_PARAMETERS_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: false,
    rateLimitRpm: 120,
  }
);

// Helper function to calculate distance between two points (simplified)
function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  // Using Haversine formula for distance calculation
  const R = 6371; // Radius of the Earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) *
      Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const d = R * c; // Distance in km
  return d;
}

function deg2rad(deg: number): number {
  return deg * (Math.PI / 180);
}

// Helper function to estimate delivery time
function calculateEstimatedDeliveryTime(
  distance: number,
  deliveryMode?: string
): string {
  // Base time per km in minutes
  let minutesPerKm = 3;

  // Adjust based on delivery mode
  switch (deliveryMode) {
    case 'primera-milla':
      minutesPerKm = 4; // Slower for primera milla
      break;
    case 'ultima-milla':
      minutesPerKm = 2; // Faster for última milla
      break;
    default: // media milla
      minutesPerKm = 3;
  }

  const totalMinutes = distance * minutesPerKm;

  // Add handling time (in minutes)
  const handlingTime = 15;
  const totalWithHandling = totalMinutes + handlingTime;

  // Convert to hours and minutes
  const hours = Math.floor(totalWithHandling / 60);
  const minutes = Math.floor(totalWithHandling % 60);

  if (hours > 0) {
    return `${hours} hora${hours > 1 ? 's' : ''} y ${minutes} minuto${minutes !== 1 ? 's' : ''}`;
  } else {
    return `${minutes} minuto${minutes !== 1 ? 's' : ''}`;
  }
}
