/**
 * Password validation utility with comprehensive security checks
 */

export interface PasswordValidationResult {
  valid: boolean;
  score: number; // 0-100, higher is better
  feedback: string[];
  errors: string[];
}

export interface PasswordRequirements {
  minLength: number;
  maxLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  preventCommonPasswords: boolean;
  preventPersonalInfo: boolean;
}

// Default password requirements for customer accounts
export const defaultPasswordRequirements: PasswordRequirements = {
  minLength: 8,
  maxLength: 128,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  preventCommonPasswords: true,
  preventPersonalInfo: true,
};

// Common weak passwords to prevent
const commonWeakPasswords = [
  'password',
  'password123',
  '********9',
  '********',
  'qwerty123',
  'abc123456',
  'password1',
  'admin123',
  'letmein123',
  'welcome123',
  'monkey123',
  'dragon123',
  'master123',
  'shadow123',
  'superman123',
  'michael123',
  'football123',
  'baseball123',
  'liverpool123',
  'jordan123',
  'princess123',
  'sunshine123',
  'iloveyou123',
  'charlie123',
  'aa123456',
  'passw0rd',
  'p@ssw0rd',
  'p@ssword',
  '1q2w3e4r',
  'qwertyuiop',
  'asdfghjkl',
  'zxcvbnm123',
  '1qaz2wsx',
  'qwerty12',
  'password12',
];

// Patterns that indicate weak passwords
const weakPatterns = [
  /^(.)\1+$/, // All same character
  /^(012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)+/i, // Sequential characters
  /^(qwe|wer|ert|rty|tyu|yui|uio|iop|asd|sdf|dfg|fgh|ghj|hjk|jkl|zxc|xcv|cvb|vbn|bnm)+/i, // Keyboard patterns
];

export class PasswordValidator {
  private requirements: PasswordRequirements;

  constructor(
    requirements: PasswordRequirements = defaultPasswordRequirements
  ) {
    this.requirements = requirements;
  }

  validate(
    password: string,
    personalInfo?: { email?: string; name?: string; phone?: string }
  ): PasswordValidationResult {
    const result: PasswordValidationResult = {
      valid: true,
      score: 0,
      feedback: [],
      errors: [],
    };

    if (!password || typeof password !== 'string') {
      result.valid = false;
      result.errors.push('La contraseña es requerida');
      return result;
    }

    // Length checks
    if (password.length < this.requirements.minLength) {
      result.valid = false;
      result.errors.push(
        `La contraseña debe tener al menos ${this.requirements.minLength} caracteres`
      );
    } else if (password.length >= this.requirements.minLength) {
      result.score += 10;
      if (password.length >= 12) result.score += 10;
      if (password.length >= 16) result.score += 10;
    }

    if (password.length > this.requirements.maxLength) {
      result.valid = false;
      result.errors.push(
        `La contraseña debe tener menos de ${this.requirements.maxLength} caracteres`
      );
    }

    // Character type requirements
    if (this.requirements.requireUppercase && !/[A-Z]/.test(password)) {
      result.valid = false;
      result.errors.push(
        'La contraseña debe contener al menos una letra mayúscula'
      );
    } else if (/[A-Z]/.test(password)) {
      result.score += 10;
    }

    if (this.requirements.requireLowercase && !/[a-z]/.test(password)) {
      result.valid = false;
      result.errors.push(
        'La contraseña debe contener al menos una letra minúscula'
      );
    } else if (/[a-z]/.test(password)) {
      result.score += 10;
    }

    if (this.requirements.requireNumbers && !/\d/.test(password)) {
      result.valid = false;
      result.errors.push('La contraseña debe contener al menos un número');
    } else if (/\d/.test(password)) {
      result.score += 10;
    }

    if (
      this.requirements.requireSpecialChars &&
      !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/.test(password)
    ) {
      result.valid = false;
      result.errors.push(
        'La contraseña debe contener al menos un carácter especial (!@#$%^&*()_+-=[]{}|;:,.<>?)'
      );
    } else if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/.test(password)) {
      result.score += 15;
    }

    // Advanced security checks
    if (this.requirements.preventCommonPasswords) {
      const lowerPassword = password.toLowerCase();
      if (commonWeakPasswords.includes(lowerPassword)) {
        result.valid = false;
        result.errors.push(
          'La contraseña es muy común. Por favor elige una contraseña más única'
        );
      }

      // Check for weak patterns
      for (const pattern of weakPatterns) {
        if (pattern.test(password)) {
          result.valid = false;
          result.errors.push(
            'La contraseña contiene patrones predecibles. Por favor usa una combinación más aleatoria'
          );
          break;
        }
      }
    }

    // Personal information checks
    if (this.requirements.preventPersonalInfo && personalInfo) {
      const lowerPassword = password.toLowerCase();

      if (personalInfo.email) {
        const emailParts = personalInfo.email.toLowerCase().split('@')[0];
        if (
          lowerPassword.includes(emailParts) ||
          emailParts.includes(lowerPassword)
        ) {
          result.valid = false;
          result.errors.push(
            'La contraseña no debe contener partes de tu dirección de correo electrónico'
          );
        }
      }

      if (personalInfo.name) {
        const nameParts = personalInfo.name.toLowerCase().split(' ');
        for (const part of nameParts) {
          if (
            part.length > 2 &&
            (lowerPassword.includes(part) || part.includes(lowerPassword))
          ) {
            result.valid = false;
            result.errors.push(
              'La contraseña no debe contener partes de tu nombre'
            );
            break;
          }
        }
      }

      if (personalInfo.phone) {
        const phoneDigits = personalInfo.phone.replace(/\D/g, '');
        if (
          phoneDigits.length > 4 &&
          password.includes(phoneDigits.slice(-4))
        ) {
          result.valid = false;
          result.errors.push(
            'La contraseña no debe contener partes de tu número de teléfono'
          );
        }
      }
    }

    // Bonus points for diversity
    const uniqueChars = new Set(password).size;
    if (uniqueChars >= password.length * 0.7) {
      result.score += 10;
      result.feedback.push('Buena diversidad de caracteres');
    }

    // Bonus for mixed case within words
    if (/[a-z].*[A-Z]|[A-Z].*[a-z]/.test(password)) {
      result.score += 5;
      result.feedback.push('Buen uso de mayúsculas y minúsculas');
    }

    // Bonus for numbers not just at the end
    if (/\d.*[a-zA-Z]|[a-zA-Z].*\d.*[a-zA-Z]/.test(password)) {
      result.score += 5;
      result.feedback.push('Buena ubicación de los números');
    }

    // Cap the score at 100
    result.score = Math.min(result.score, 100);

    // Provide feedback based on score
    if (result.valid) {
      if (result.score >= 80) {
        result.feedback.unshift('Excelente fuerza de contraseña');
      } else if (result.score >= 60) {
        result.feedback.unshift('Buena fuerza de contraseña');
      } else if (result.score >= 40) {
        result.feedback.unshift(
          'Fuerza de contraseña aceptable - considera hacerla más fuerte'
        );
      } else {
        result.feedback.unshift('Contraseña débil - por favor mejórala');
      }
    }

    return result;
  }

  // Quick validation for API endpoints
  validateQuick(password: string): { valid: boolean; message?: string } {
    const result = this.validate(password);
    return {
      valid: result.valid,
      message: result.errors.length > 0 ? result.errors[0] : undefined,
    };
  }

  // Get password strength as a percentage
  getStrength(password: string): number {
    const result = this.validate(password);
    return result.score;
  }

  // Check if password meets minimum requirements
  meetsMinimumRequirements(password: string): boolean {
    const result = this.validate(password);
    return result.valid;
  }
}

// Export a default instance
export const passwordValidator = new PasswordValidator();

// Utility function for client-side password strength indication
export function getPasswordStrengthColor(score: number): string {
  if (score >= 80) return 'text-green-600';
  if (score >= 60) return 'text-yellow-600';
  if (score >= 40) return 'text-orange-600';
  return 'text-red-600';
}

export function getPasswordStrengthText(score: number): string {
  if (score >= 80) return 'Fuerte';
  if (score >= 60) return 'Buena';
  if (score >= 40) return 'Aceptable';
  return 'Débil';
}

// Password generation utility (for suggesting strong passwords)
export function generateStrongPassword(length: number = 16): string {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0********9';
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

  const allChars = uppercase + lowercase + numbers + symbols;
  let password = '';

  // Ensure at least one character from each required set
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += symbols[Math.floor(Math.random() * symbols.length)];

  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }

  // Shuffle the password
  return password
    .split('')
    .sort(() => Math.random() - 0.5)
    .join('');
}
