import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _request: NextRequest
) {
  try {
    // Check environment variables
    if (
      !process.env.NEXT_PUBLIC_SUPABASE_URL ||
      !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    ) {
      return NextResponse.json(
        { error: 'Error de configuración del servidor' },
        { status: 500 }
      );
    }

    let supabase;
    try {
      supabase = await createClient();
    } catch {
      return NextResponse.json(
        { error: 'Error al inicializar la conexión a la base de datos' },
        { status: 500 }
      );
    }

    // Get the current user
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Get user profile to check role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'Perfil no encontrado' },
        { status: 404 }
      );
    }

    // Only allow admins to fetch available drivers
    if (profile.role !== 'admin') {
      return NextResponse.json(
        {
          error: 'Solo los administradores pueden ver la lista de repartidores',
        },
        { status: 403 }
      );
    }

    // Fetch all active delivery drivers
    const { data: drivers, error: driversError } = await supabase
      .from('profiles')
      .select('id, full_name, email, phone, created_at, is_active')
      .eq('role', 'delivery')
      .eq('is_active', true)
      .order('full_name', { ascending: true });

    if (driversError) {
      console.error('Error fetching drivers:', driversError);
      return NextResponse.json(
        { error: 'Error al obtener la lista de repartidores' },
        { status: 500 }
      );
    }

    // Get current order counts for each driver (optional - for workload balancing)
    const driverIds = drivers?.map(driver => driver.id) || [];

    const driverWorkloads: Record<string, number> = {};
    if (driverIds.length > 0) {
      const { data: orderCounts, error: countsError } = await supabase
        .from('orders')
        .select('delivery_id')
        .in('delivery_id', driverIds)
        .in('status', ['confirmed', 'in-transit']); // Active orders only

      if (!countsError && orderCounts) {
        // Count orders per driver
        orderCounts.forEach(order => {
          if (order.delivery_id) {
            driverWorkloads[order.delivery_id] =
              (driverWorkloads[order.delivery_id] || 0) + 1;
          }
        });
      }
    }

    // Format the response with driver info and current workload
    const availableDrivers =
      drivers?.map(driver => ({
        id: driver.id,
        name: driver.full_name || 'Sin nombre',
        email: driver.email,
        phone: driver.phone || 'Sin teléfono',
        activeOrders: driverWorkloads[driver.id] || 0,
        isActive: driver.is_active,
        joinedDate: driver.created_at,
      })) || [];

    return NextResponse.json({
      success: true,
      drivers: availableDrivers,
      totalDrivers: availableDrivers.length,
    });
  } catch (error) {
    console.error('Unexpected error in available drivers API:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
