/**
 * Security utility functions
 *
 * Provides common security utilities for device fingerprinting,
 * validation, and other security-related operations.
 */

// Using standard Web API Headers type

/**
 * Generate a device fingerprint from request headers
 *
 * Creates a unique identifier for the device based on various
 * request headers and characteristics.
 *
 * @param headers - Request headers object
 * @returns Device fingerprint string
 */
export function generateDeviceFingerprint(headers: Headers): string {
  const components: string[] = [];

  // User Agent (most important component)
  const userAgent = headers.get('user-agent') || 'unknown';
  components.push(userAgent);

  // Accept headers
  const accept = headers.get('accept') || '';
  const acceptLanguage = headers.get('accept-language') || '';
  const acceptEncoding = headers.get('accept-encoding') || '';

  components.push(accept, acceptLanguage, acceptEncoding);

  // Connection info
  const connection = headers.get('connection') || '';
  const upgradeInsecureRequests =
    headers.get('upgrade-insecure-requests') || '';

  components.push(connection, upgradeInsecureRequests);

  // Screen resolution (if available via client hints)
  const viewportWidth = headers.get('viewport-width') || '';
  const viewportHeight = headers.get('viewport-height') || '';

  if (viewportWidth && viewportHeight) {
    components.push(`${viewportWidth}x${viewportHeight}`);
  }

  // Create hash from components
  const fingerprint = components.join('|');

  // Simple hash function (in production, use crypto.subtle.digest)
  let hash = 0;
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  return Math.abs(hash).toString(36);
}

/**
 * Validate device fingerprint format
 *
 * @param fingerprint - Device fingerprint to validate
 * @returns True if valid format
 */
export function isValidDeviceFingerprint(fingerprint: string): boolean {
  return (
    typeof fingerprint === 'string' &&
    fingerprint.length > 0 &&
    fingerprint.length <= 50 &&
    /^[a-zA-Z0-9]+$/.test(fingerprint)
  );
}

/**
 * Generate a secure random token
 *
 * @param length - Token length (default: 32)
 * @returns Secure random token
 */
export function generateSecureToken(length: number = 32): string {
  const chars =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';

  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return result;
}

/**
 * Sanitize input string for security
 *
 * @param input - Input string to sanitize
 * @returns Sanitized string
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/['"]/g, '') // Remove quotes
    .replace(/[;]/g, '') // Remove semicolons
    .trim()
    .substring(0, 1000); // Limit length
}

/**
 * Check if IP address is in private range
 *
 * @param ip - IP address to check
 * @returns True if private IP
 */
export function isPrivateIP(ip: string): boolean {
  const privateRanges = [
    /^10\./,
    /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
    /^192\.168\./,
    /^127\./,
    /^::1$/,
    /^fc00:/,
    /^fe80:/,
  ];

  return privateRanges.some(range => range.test(ip));
}

/**
 * Extract client IP from request headers
 *
 * @param headers - Request headers
 * @returns Client IP address
 */
export function extractClientIP(headers: Headers): string {
  // Check various headers for real IP
  const xForwardedFor = headers.get('x-forwarded-for');
  const xRealIP = headers.get('x-real-ip');
  const cfConnectingIP = headers.get('cf-connecting-ip');

  // X-Forwarded-For can contain multiple IPs, take the first one
  if (xForwardedFor) {
    const ips = xForwardedFor.split(',');
    const firstIP = ips[0]?.trim();
    if (firstIP && !isPrivateIP(firstIP)) {
      return firstIP;
    }
  }

  if (cfConnectingIP && !isPrivateIP(cfConnectingIP)) {
    return cfConnectingIP;
  }

  if (xRealIP && !isPrivateIP(xRealIP)) {
    return xRealIP;
  }

  return 'unknown';
}
