import { useMemo } from 'react';

interface User {
  id: string;
  full_name: string | null;
  email: string;
  phone: string | null;
  role: 'customer' | 'delivery' | 'admin';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface UseUserFilteringProps {
  users: User[];
  searchTerm: string;
  roleFilter: string;
  statusFilter: string;
}

export function useUserFiltering({
  users,
  searchTerm,
  roleFilter,
  statusFilter,
}: UseUserFilteringProps) {
  const filteredUsers = useMemo(() => {
    return users.filter(user => {
      const matchesSearch =
        (user.full_name &&
          user.full_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.phone && user.phone.includes(searchTerm));

      const matchesRole = roleFilter === 'all' || user.role === roleFilter;
      const matchesStatus =
        statusFilter === 'all' ||
        (statusFilter === 'active' ? user.is_active : !user.is_active);

      return matchesSearch && matchesRole && matchesStatus;
    });
  }, [users, searchTerm, roleFilter, statusFilter]);

  return filteredUsers;
}
