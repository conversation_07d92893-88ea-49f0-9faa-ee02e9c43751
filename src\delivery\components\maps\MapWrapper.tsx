'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Importar el mapa dinámicamente solo en el cliente
const SafeDeliveryMap = dynamic(() => import('./SafeDeliveryMap'), {
  ssr: false,
  loading: () => (
    <div className='w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center'>
      <div className='text-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2'></div>
        <p className='text-sm text-gray-600'>Preparando mapa...</p>
      </div>
    </div>
  ),
});

// Importar el mapa simple como fallback
const SimpleMap = dynamic(() => import('./SimpleMap'), {
  ssr: false,
  loading: () => (
    <div className='w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center'>
      <div className='text-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2'></div>
        <p className='text-sm text-gray-600'>Cargando mapa simple...</p>
      </div>
    </div>
  ),
});

interface MapWrapperProps {
  pickupAddress?:
    | {
        street?: string;
        number?: string;
        city?: string;
        state?: string;
        zip?: string;
        lat?: number;
        lng?: number;
        name?: string;
        coordinates?: { lat: number; lng: number } | [number, number];
      }
    | string;
  deliveryAddress?:
    | {
        street?: string;
        number?: string;
        city?: string;
        state?: string;
        zip?: string;
        lat?: number;
        lng?: number;
        name?: string;
        coordinates?: { lat: number; lng: number } | [number, number];
      }
    | string;
  className?: string;
}

// Helper function to convert complex address types to SimpleMap address types
const convertToSimpleAddress = (
  address: { lat?: number; lng?: number; name?: string } | string | undefined
) => {
  if (!address) return undefined;

  // If it's already a string, we can't extract coordinates
  if (typeof address === 'string') {
    return undefined;
  }

  // If it's an object, extract the relevant properties
  return {
    lat: address.lat,
    lng: address.lng,
    name: address.name,
  };
};

export default function MapWrapper({
  pickupAddress,
  deliveryAddress,
  className,
}: MapWrapperProps) {
  const [isMounted, setIsMounted] = useState(false);
  const [useSimpleMap, setUseSimpleMap] = useState(false);

  // Convert addresses for SimpleMap
  const simplePickupAddress = convertToSimpleAddress(pickupAddress);
  const simpleDeliveryAddress = convertToSimpleAddress(deliveryAddress);

  useEffect(() => {
    setIsMounted(true);

    // Set a timeout to fallback to simple map if complex map doesn't load
    const timeoutId = setTimeout(() => {
      console.log('⏰ MapWrapper timeout - switching to SimpleMap');
      setUseSimpleMap(true);
    }, 3000);

    return () => clearTimeout(timeoutId);
  }, []);

  if (!isMounted) {
    return (
      <div className={`w-full ${className}`}>
        <div className='bg-gray-100 rounded-lg flex items-center justify-center h-full'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2'></div>
            <p className='text-sm text-gray-600'>Inicializando mapa...</p>
          </div>
        </div>
      </div>
    );
  }

  // Use SimpleMap as fallback
  if (useSimpleMap) {
    console.log('🗺️ MapWrapper using SimpleMap fallback');
    return (
      <div className={`w-full ${className}`} style={{ minHeight: '256px' }}>
        <SimpleMap
          pickupAddress={simplePickupAddress}
          deliveryAddress={simpleDeliveryAddress}
          className='w-full h-full'
        />
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`} style={{ minHeight: '256px' }}>
      <SafeDeliveryMap
        pickupAddress={pickupAddress}
        deliveryAddress={deliveryAddress}
        className='w-full h-full'
      />
    </div>
  );
}
