'use client';

import { useEffect, useState } from 'react';
import {
  passwordValidator,
  getPasswordStrengthColor,
  getPasswordStrengthText,
} from '@/lib/password-validator';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface PasswordStrengthIndicatorProps {
  password: string;
  personalInfo?: {
    email?: string;
    name?: string;
    phone?: string;
  };
  showRequirements?: boolean;
  className?: string;
}

export function PasswordStrengthIndicator({
  password,
  personalInfo,
  showRequirements = true,
  className = '',
}: PasswordStrengthIndicatorProps) {
  const [validation, setValidation] = useState(passwordValidator.validate(''));

  useEffect(() => {
    if (password) {
      setValidation(passwordValidator.validate(password, personalInfo));
    } else {
      setValidation(passwordValidator.validate(''));
    }
  }, [password, personalInfo]);

  if (!password) {
    return null;
  }

  const strengthColor = getPasswordStrengthColor(validation.score);
  const strengthText = getPasswordStrengthText(validation.score);

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Strength Bar */}
      <div className='space-y-2'>
        <div className='flex justify-between items-center'>
          <span className='text-sm font-medium text-gray-700'>
            Fuerza de la Contraseña
          </span>
          <span className={`text-sm font-medium ${strengthColor}`}>
            {strengthText} ({validation.score}/100)
          </span>
        </div>

        <div className='w-full bg-gray-200 rounded-full h-2'>
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              validation.score >= 80
                ? 'bg-green-500'
                : validation.score >= 60
                  ? 'bg-yellow-500'
                  : validation.score >= 40
                    ? 'bg-orange-500'
                    : 'bg-red-500'
            }`}
            style={{ width: `${validation.score}%` }}
          />
        </div>
      </div>

      {/* Requirements Checklist */}
      {showRequirements && (
        <div className='space-y-2'>
          <h4 className='text-sm font-medium text-gray-700'>
            Requisitos de la Contraseña
          </h4>
          <div className='space-y-1'>
            <RequirementItem
              met={password.length >= 8}
              text='Al menos 8 caracteres'
            />
            <RequirementItem
              met={/[A-Z]/.test(password)}
              text='Una letra mayúscula'
            />
            <RequirementItem
              met={/[a-z]/.test(password)}
              text='Una letra minúscula'
            />
            <RequirementItem met={/\d/.test(password)} text='Un número' />
            <RequirementItem
              met={/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/.test(password)}
              text='Un carácter especial'
            />
            <RequirementItem
              met={validation.valid}
              text='No es una contraseña común'
            />
          </div>
        </div>
      )}

      {/* Feedback Messages */}
      {validation.feedback.length > 0 && (
        <div className='space-y-1'>
          {validation.feedback.map((feedback, index) => (
            <div
              key={index}
              className='flex items-center space-x-2 text-sm text-green-600'
            >
              <CheckCircle className='w-4 h-4' />
              <span>{feedback}</span>
            </div>
          ))}
        </div>
      )}

      {/* Error Messages */}
      {validation.errors.length > 0 && (
        <div className='space-y-1'>
          {validation.errors.map((error, index) => (
            <div
              key={index}
              className='flex items-center space-x-2 text-sm text-red-600'
            >
              <XCircle className='w-4 h-4' />
              <span>{error}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

interface RequirementItemProps {
  met: boolean;
  text: string;
}

function RequirementItem({ met, text }: RequirementItemProps) {
  return (
    <div
      className={`flex items-center space-x-2 text-sm ${met ? 'text-green-600' : 'text-gray-500'}`}
    >
      {met ? (
        <CheckCircle className='w-4 h-4' />
      ) : (
        <AlertCircle className='w-4 h-4' />
      )}
      <span>{text}</span>
    </div>
  );
}

// Simplified version for inline use
export function PasswordStrengthBadge({
  password,
  className = '',
}: {
  password: string;
  className?: string;
}) {
  const [score, setScore] = useState(0);

  useEffect(() => {
    if (password) {
      setScore(passwordValidator.getStrength(password));
    } else {
      setScore(0);
    }
  }, [password]);

  if (!password) {
    return null;
  }

  const strengthColor = getPasswordStrengthColor(score);
  const strengthText = getPasswordStrengthText(score);

  return (
    <span
      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${strengthColor} bg-opacity-10 ${className}`}
    >
      {strengthText}
    </span>
  );
}

// Hook for password validation
export function usePasswordValidation(
  password: string,
  personalInfo?: { email?: string; name?: string; phone?: string }
) {
  const [validation, setValidation] = useState(passwordValidator.validate(''));

  useEffect(() => {
    if (password) {
      setValidation(passwordValidator.validate(password, personalInfo));
    } else {
      setValidation(passwordValidator.validate(''));
    }
  }, [password, personalInfo]);

  return validation;
}
