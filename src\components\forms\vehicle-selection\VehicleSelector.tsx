'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ProductItem } from '@/types/order-form';

// Database vehicle type interface
interface VehicleType {
  id: string;
  name: string;
  category: string;
  max_weight_kg: number;
  max_volume_m3: number;
  base_rate_per_km: number;
  special_capabilities: string[] | null;
  description: string | null;
  image_path?: string | null;
}

interface VehicleSelectorProps {
  products: ProductItem[];
  selectedVehicleId?: string;
  onVehicleSelect: (vehicleId: string) => void;
  className?: string;
}

export function VehicleSelector({
  products,
  selectedVehicleId,
  onVehicleSelect,
  className = '',
}: VehicleSelectorProps) {
  const [vehicleTypes, setVehicleTypes] = useState<VehicleType[]>([]);
  const [suggestions, setSuggestions] = useState<VehicleType[]>([]);
  const [showAllVehicles, setShowAllVehicles] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());

  // Calculate totals from products
  const totals = React.useMemo(() => {
    const totalWeight = products.reduce((sum, product) => {
      const weight = product.weight || 1;
      const weightInKg = product.weight_unit === 'g' ? weight / 1000 : weight;
      return sum + weightInKg * product.quantity;
    }, 0);

    const totalVolume = products.reduce((sum, product) => {
      if (!product.dimensions) return sum;
      const volume =
        (product.dimensions.length *
          product.dimensions.width *
          product.dimensions.height) /
        1000000;
      return sum + volume * product.quantity;
    }, 0);

    const specialHandling = {
      fragile: products.some(p => p.special_handling?.fragile),
      perishable: products.some(p => p.special_handling?.perishable),
      valuable: products.some(p => p.special_handling?.valuable),
      hazardous: products.some(p => p.special_handling?.hazardous),
      refrigerated: products.some(p => p.special_handling?.refrigerated),
      oversized: products.some(p => p.special_handling?.oversized),
    };

    return { totalWeight, totalVolume, specialHandling };
  }, [products]);

  // Fetch vehicle types from API
  useEffect(() => {
    const fetchVehicleTypes = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/vehicle-types');
        const result = await response.json();

        if (result.success) {
          setVehicleTypes(result.data);
          setError(null);
        } else {
          setError('Error al cargar tipos de vehículos');
        }
      } catch (error) {
        console.error('Error fetching vehicle types:', error);
        setError('Error de conexión al cargar vehículos');
      } finally {
        setLoading(false);
      }
    };

    fetchVehicleTypes();
  }, []);

  // Calculate suggestions based on fetched data
  useEffect(() => {
    if (vehicleTypes.length === 0) return;

    const suggested = vehicleTypes.filter(vehicle => {
      // Check weight and volume capacity
      if (
        totals.totalWeight > vehicle.max_weight_kg ||
        totals.totalVolume > vehicle.max_volume_m3
      ) {
        return false;
      }

      // Check special handling requirements
      if (
        totals.specialHandling.refrigerated &&
        !vehicle.special_capabilities?.includes('temperature_control')
      ) {
        return false;
      }

      if (
        totals.specialHandling.hazardous &&
        vehicle.category === 'motorcycle'
      ) {
        return false;
      }

      if (totals.specialHandling.oversized && vehicle.category !== 'special') {
        return false;
      }

      return true;
    });

    // Sort by efficiency (cost per kg capacity)
    const sortedSuggestions = suggested.sort((a, b) => {
      const efficiencyA = a.base_rate_per_km / a.max_weight_kg;
      const efficiencyB = b.base_rate_per_km / b.max_weight_kg;
      return efficiencyA - efficiencyB;
    });

    setSuggestions(sortedSuggestions.slice(0, 3)); // Top 3 suggestions
  }, [vehicleTypes, totals]);

  const vehiclesToShow = showAllVehicles ? vehicleTypes : suggestions;

  const getVehicleStatusBadge = (vehicle: VehicleType) => {
    const canCarryWeight = totals.totalWeight <= vehicle.max_weight_kg;
    const canCarryVolume = totals.totalVolume <= vehicle.max_volume_m3;

    if (!canCarryWeight || !canCarryVolume) {
      return <Badge variant='destructive'>Capacidad Insuficiente</Badge>;
    }

    if (suggestions.includes(vehicle)) {
      return <Badge variant='default'>Recomendado</Badge>;
    }

    return <Badge variant='secondary'>Disponible</Badge>;
  };

  const isVehicleCompatible = (vehicle: VehicleType) => {
    const canCarryWeight = totals.totalWeight <= vehicle.max_weight_kg;
    const canCarryVolume = totals.totalVolume <= vehicle.max_volume_m3;

    // Check special handling requirements
    if (
      totals.specialHandling.refrigerated &&
      !vehicle.special_capabilities?.includes('temperature_control')
    ) {
      return false;
    }

    if (totals.specialHandling.hazardous && vehicle.category === 'motorcycle') {
      return false;
    }

    if (totals.specialHandling.oversized && vehicle.category !== 'special') {
      return false;
    }

    return canCarryWeight && canCarryVolume;
  };

  if (loading) {
    return (
      <div className={className}>
        <Card>
          <CardContent className='p-6'>
            <div className='text-center'>
              <div className='text-lg'>Cargando tipos de vehículos...</div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        <Card>
          <CardContent className='p-6'>
            <div className='text-center text-red-600'>
              <div className='text-lg font-semibold'>Error</div>
              <div>{error}</div>
              <Button
                onClick={() => window.location.reload()}
                className='mt-4'
                variant='outline'
              >
                Reintentar
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Cargo Summary */}
      <Card className='mb-6'>
        <CardHeader>
          <CardTitle className='text-lg'>Resumen de Carga</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-center'>
            <div>
              <div className='text-2xl font-bold text-blue-600'>
                {totals.totalWeight.toFixed(2)}
              </div>
              <div className='text-sm text-gray-600'>kg Total</div>
            </div>
            <div>
              <div className='text-2xl font-bold text-blue-600'>
                {totals.totalVolume.toFixed(3)}
              </div>
              <div className='text-sm text-gray-600'>m³ Total</div>
            </div>
            <div>
              <div className='text-2xl font-bold text-blue-600'>
                {products.length}
              </div>
              <div className='text-sm text-gray-600'>Productos</div>
            </div>
            <div>
              <div className='text-2xl font-bold text-blue-600'>
                {Object.values(totals.specialHandling).filter(Boolean).length}
              </div>
              <div className='text-sm text-gray-600'>Req. Especiales</div>
            </div>
          </div>

          {Object.values(totals.specialHandling).some(Boolean) && (
            <div className='mt-4 pt-4 border-t'>
              <div className='text-sm font-medium mb-2'>
                Requerimientos Especiales:
              </div>
              <div className='flex flex-wrap gap-2'>
                {totals.specialHandling.fragile && (
                  <Badge variant='outline'>🔸 Frágil</Badge>
                )}
                {totals.specialHandling.perishable && (
                  <Badge variant='outline'>❄️ Perecedero</Badge>
                )}
                {totals.specialHandling.valuable && (
                  <Badge variant='outline'>💎 Valioso</Badge>
                )}
                {totals.specialHandling.hazardous && (
                  <Badge variant='outline'>⚠️ Peligroso</Badge>
                )}
                {totals.specialHandling.refrigerated && (
                  <Badge variant='outline'>🧊 Refrigerado</Badge>
                )}
                {totals.specialHandling.oversized && (
                  <Badge variant='outline'>📏 Sobredimensionado</Badge>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Vehicle Selection */}
      <div className='space-y-4'>
        <div className='flex justify-between items-center'>
          <h3 className='text-lg font-semibold'>
            {showAllVehicles ? 'Todos los Vehículos' : 'Vehículos Recomendados'}
          </h3>
          <Button
            variant='outline'
            onClick={() => setShowAllVehicles(!showAllVehicles)}
          >
            {showAllVehicles ? 'Ver Recomendados' : 'Ver Todos'}
          </Button>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
          {vehiclesToShow.map(vehicle => {
            const isSelected = selectedVehicleId === vehicle.id;
            const isCompatible = isVehicleCompatible(vehicle);

            return (
              <Card
                key={vehicle.id}
                className={`cursor-pointer transition-all ${
                  isSelected
                    ? 'ring-2 ring-blue-500 bg-blue-50'
                    : isCompatible
                      ? 'hover:shadow-md'
                      : 'opacity-60'
                }`}
                onClick={() => isCompatible && onVehicleSelect(vehicle.id)}
              >
                <CardContent className='p-4'>
                  <div className='flex items-start justify-between mb-3'>
                    <div className='flex-1'>
                      <h4 className='font-semibold text-sm'>{vehicle.name}</h4>
                      <p className='text-xs text-gray-600'>
                        {vehicle.category}
                      </p>
                    </div>
                    {getVehicleStatusBadge(vehicle)}
                  </div>

                  <div className='relative h-24 mb-3 bg-gray-100 rounded overflow-hidden flex items-center justify-center'>
                    {vehicle.image_path && !imageErrors.has(vehicle.id) ? (
                      <Image
                        src={vehicle.image_path}
                        alt={vehicle.name}
                        fill
                        className='object-cover'
                        onError={() =>
                          setImageErrors(prev => new Set(prev).add(vehicle.id))
                        }
                      />
                    ) : (
                      <div className='text-4xl'>
                        {vehicle.category === 'motorcycle' && '🏍️'}
                        {vehicle.category === 'van' && '🚐'}
                        {vehicle.category === 'truck' && '🚚'}
                        {vehicle.category === 'trailer' && '🚛'}
                        {vehicle.category === 'special' && '🚜'}
                        {![
                          'motorcycle',
                          'van',
                          'truck',
                          'trailer',
                          'special',
                        ].includes(vehicle.category) && '�'}
                      </div>
                    )}
                  </div>

                  <div className='space-y-2 text-xs'>
                    <div className='flex justify-between'>
                      <span>Capacidad:</span>
                      <span className='font-medium'>
                        {vehicle.max_weight_kg}kg / {vehicle.max_volume_m3}m³
                      </span>
                    </div>
                    <div className='flex justify-between'>
                      <span>Tarifa base:</span>
                      <span className='font-medium'>
                        ${vehicle.base_rate_per_km}/km
                      </span>
                    </div>
                    <div className='flex justify-between'>
                      <span>Categoría:</span>
                      <span className='font-medium capitalize'>
                        {vehicle.category}
                      </span>
                    </div>
                  </div>

                  {vehicle.special_capabilities &&
                    vehicle.special_capabilities.length > 0 && (
                      <div className='mt-3 pt-2 border-t'>
                        <div className='text-xs text-gray-600 mb-1'>
                          Capacidades:
                        </div>
                        <div className='flex flex-wrap gap-1'>
                          {vehicle.special_capabilities
                            .slice(0, 2)
                            .map(capability => (
                              <Badge
                                key={capability}
                                variant='outline'
                                className='text-xs'
                              >
                                {capability.replace('_', ' ')}
                              </Badge>
                            ))}
                          {vehicle.special_capabilities.length > 2 && (
                            <Badge variant='outline' className='text-xs'>
                              +{vehicle.special_capabilities.length - 2}
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}

                  <p className='text-xs text-gray-600 mt-2 line-clamp-2'>
                    {vehicle.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </div>
  );
}
