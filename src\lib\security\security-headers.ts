/**
 * Security Headers Implementation
 *
 * Implements comprehensive security headers following OWASP recommendations
 */

import { NextResponse } from 'next/server';

export interface SecurityHeadersConfig {
  contentSecurityPolicy?: {
    enabled: boolean;
    directives?: Record<string, string[]>;
    reportOnly?: boolean;
  };
  strictTransportSecurity?: {
    enabled: boolean;
    maxAge?: number;
    includeSubDomains?: boolean;
    preload?: boolean;
  };
  frameOptions?: 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM';
  contentTypeOptions?: boolean;
  referrerPolicy?: string;
  permissionsPolicy?: Record<string, string[]>;
  crossOriginEmbedderPolicy?: 'require-corp' | 'credentialless';
  crossOriginOpenerPolicy?:
    | 'same-origin'
    | 'same-origin-allow-popups'
    | 'unsafe-none';
  crossOriginResourcePolicy?: 'same-site' | 'same-origin' | 'cross-origin';
}

/**
 * Apply security headers to response
 */
export function applySecurityHeaders(
  response: NextResponse,
  config: SecurityHeadersConfig = {}
): NextResponse {
  // Default configuration
  const defaultConfig: SecurityHeadersConfig = {
    contentSecurityPolicy: {
      enabled: true,
      directives: {
        'default-src': ["'self'"],
        'script-src': [
          "'self'",
          "'unsafe-inline'", // Required for Next.js
          "'unsafe-eval'", // Required for Next.js dev mode
          'https://js.stripe.com',
          'https://checkout.stripe.com',
          'https://maps.googleapis.com',
        ],
        'style-src': [
          "'self'",
          "'unsafe-inline'", // Required for styled-components and CSS-in-JS
          'https://fonts.googleapis.com',
        ],
        'img-src': [
          "'self'",
          'data:',
          'blob:',
          'https:',
          'https://images.unsplash.com',
          'https://via.placeholder.com',
        ],
        'font-src': ["'self'", 'https://fonts.gstatic.com'],
        'connect-src': [
          "'self'",
          'https://api.stripe.com',
          'https://*.supabase.co',
          'wss://*.supabase.co',
          process.env.NEXT_PUBLIC_SUPABASE_URL || '',
        ].filter(Boolean),
        'frame-src': [
          "'self'",
          'https://js.stripe.com',
          'https://checkout.stripe.com',
        ],
        'object-src': ["'none'"],
        'base-uri': ["'self'"],
        'form-action': ["'self'"],
        'frame-ancestors': ["'none'"],
        'upgrade-insecure-requests': [],
      },
      reportOnly: process.env.NODE_ENV === 'development',
    },
    strictTransportSecurity: {
      enabled: process.env.NODE_ENV === 'production',
      maxAge: 31536000, // 1 year
      includeSubDomains: true,
      preload: true,
    },
    frameOptions: 'DENY',
    contentTypeOptions: true,
    referrerPolicy: 'strict-origin-when-cross-origin',
    permissionsPolicy: {
      camera: [],
      microphone: [],
      geolocation: ["'self'"],
      payment: ["'self'"],
      usb: [],
      magnetometer: [],
      gyroscope: [],
      accelerometer: [],
    },
    crossOriginEmbedderPolicy: 'credentialless',
    crossOriginOpenerPolicy: 'same-origin',
    crossOriginResourcePolicy: 'same-origin',
  };

  // Merge configurations
  const finalConfig = mergeConfigs(defaultConfig, config);

  // Apply Content Security Policy
  if (finalConfig.contentSecurityPolicy?.enabled) {
    const cspHeader = buildCSPHeader(finalConfig.contentSecurityPolicy);
    const headerName = finalConfig.contentSecurityPolicy.reportOnly
      ? 'Content-Security-Policy-Report-Only'
      : 'Content-Security-Policy';
    response.headers.set(headerName, cspHeader);
  }

  // Apply Strict Transport Security
  if (finalConfig.strictTransportSecurity?.enabled) {
    const hstsValue = buildHSTSHeader(finalConfig.strictTransportSecurity);
    response.headers.set('Strict-Transport-Security', hstsValue);
  }

  // Apply X-Frame-Options
  if (finalConfig.frameOptions) {
    response.headers.set('X-Frame-Options', finalConfig.frameOptions);
  }

  // Apply X-Content-Type-Options
  if (finalConfig.contentTypeOptions) {
    response.headers.set('X-Content-Type-Options', 'nosniff');
  }

  // Apply Referrer Policy
  if (finalConfig.referrerPolicy) {
    response.headers.set('Referrer-Policy', finalConfig.referrerPolicy);
  }

  // Apply Permissions Policy
  if (finalConfig.permissionsPolicy) {
    const permissionsValue = buildPermissionsPolicyHeader(
      finalConfig.permissionsPolicy
    );
    response.headers.set('Permissions-Policy', permissionsValue);
  }

  // Apply Cross-Origin-Embedder-Policy
  if (finalConfig.crossOriginEmbedderPolicy) {
    response.headers.set(
      'Cross-Origin-Embedder-Policy',
      finalConfig.crossOriginEmbedderPolicy
    );
  }

  // Apply Cross-Origin-Opener-Policy
  if (finalConfig.crossOriginOpenerPolicy) {
    response.headers.set(
      'Cross-Origin-Opener-Policy',
      finalConfig.crossOriginOpenerPolicy
    );
  }

  // Apply Cross-Origin-Resource-Policy
  if (finalConfig.crossOriginResourcePolicy) {
    response.headers.set(
      'Cross-Origin-Resource-Policy',
      finalConfig.crossOriginResourcePolicy
    );
  }

  // Additional security headers
  response.headers.set('X-DNS-Prefetch-Control', 'off');
  response.headers.set('X-Download-Options', 'noopen');
  response.headers.set('X-Permitted-Cross-Domain-Policies', 'none');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  // Remove potentially sensitive headers
  response.headers.delete('Server');
  response.headers.delete('X-Powered-By');

  return response;
}

/**
 * Build Content Security Policy header value
 */
function buildCSPHeader(
  config: NonNullable<SecurityHeadersConfig['contentSecurityPolicy']>
): string {
  if (!config.directives) return '';

  const directives = Object.entries(config.directives)
    .map(([directive, sources]) => {
      if (sources.length === 0) {
        return directive;
      }
      return `${directive} ${sources.join(' ')}`;
    })
    .join('; ');

  return directives;
}

/**
 * Build Strict Transport Security header value
 */
function buildHSTSHeader(
  config: NonNullable<SecurityHeadersConfig['strictTransportSecurity']>
): string {
  let value = `max-age=${config.maxAge || 31536000}`;

  if (config.includeSubDomains) {
    value += '; includeSubDomains';
  }

  if (config.preload) {
    value += '; preload';
  }

  return value;
}

/**
 * Build Permissions Policy header value
 */
function buildPermissionsPolicyHeader(
  permissions: Record<string, string[]>
): string {
  return Object.entries(permissions)
    .map(([directive, allowlist]) => {
      if (allowlist.length === 0) {
        return `${directive}=()`;
      }
      return `${directive}=(${allowlist.join(' ')})`;
    })
    .join(', ');
}

/**
 * Merge security configurations
 */
function mergeConfigs(
  defaultConfig: SecurityHeadersConfig,
  userConfig: SecurityHeadersConfig
): SecurityHeadersConfig {
  const merged = { ...defaultConfig };

  // Merge CSP directives
  if (
    userConfig.contentSecurityPolicy?.directives &&
    merged.contentSecurityPolicy?.directives
  ) {
    merged.contentSecurityPolicy.directives = {
      ...merged.contentSecurityPolicy.directives,
      ...userConfig.contentSecurityPolicy.directives,
    };
  }

  // Merge permissions policy
  if (userConfig.permissionsPolicy && merged.permissionsPolicy) {
    merged.permissionsPolicy = {
      ...merged.permissionsPolicy,
      ...userConfig.permissionsPolicy,
    };
  }

  // Override other properties
  Object.keys(userConfig).forEach(key => {
    if (key !== 'contentSecurityPolicy' && key !== 'permissionsPolicy') {
      (merged as Record<string, unknown>)[key] = (
        userConfig as Record<string, unknown>
      )[key];
    }
  });

  return merged;
}

/**
 * Get security headers for API routes
 */
export function getAPISecurityHeaders(): Record<string, string> {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Cross-Origin-Resource-Policy': 'same-origin',
    'Cross-Origin-Opener-Policy': 'same-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    ...(process.env.NODE_ENV === 'production' && {
      'Strict-Transport-Security':
        'max-age=31536000; includeSubDomains; preload',
    }),
  };
}

// CSP Violation Report interface
interface CSPViolationReport {
  'document-uri': string;
  'violated-directive': string;
  'blocked-uri': string;
  'original-policy'?: string;
  'user-agent'?: string;
  [key: string]: string | undefined;
}

/**
 * Validate CSP violation reports
 */
export function validateCSPReport(
  report: unknown
): report is CSPViolationReport {
  if (!report || typeof report !== 'object') return false;

  const requiredFields = ['document-uri', 'violated-directive', 'blocked-uri'];
  return requiredFields.every(field => field in report);
}

/**
 * Process CSP violation report
 */
export async function processCSPViolation(
  report: CSPViolationReport
): Promise<void> {
  if (!validateCSPReport(report)) {
    console.warn('Invalid CSP report received');
    return;
  }

  // Log the violation
  console.warn('CSP Violation:', {
    documentUri: report['document-uri'],
    violatedDirective: report['violated-directive'],
    blockedUri: report['blocked-uri'],
    originalPolicy: report['original-policy'],
    userAgent: report['user-agent'],
  });

  // TODO: Send to monitoring service
  // await sendToMonitoringService('CSP_VIOLATION', report);
}
