'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useDeliveryTracking } from '@/delivery/hooks/useDeliveryTracking';
import dynamic from 'next/dynamic';
import { VehicleImage } from '@/components/vehicles/VehicleImage';

// Dynamically import the map component to avoid SSR issues
const DeliveryMap = dynamic(
  () => import('@/delivery/components/maps/DeliveryMap'),
  {
    ssr: false,
    loading: () => (
      <div className='bg-gray-100 rounded-lg flex items-center justify-center h-96'>
        <p className='text-gray-600'>Cargando mapa...</p>
      </div>
    ),
  }
);

interface RealTimeTrackingProps {
  orderId: string;
  className?: string;
}

export function RealTimeTracking({
  orderId,
  className = '',
}: RealTimeTrackingProps) {
  const { trackingData, loading, error } = useDeliveryTracking(orderId);
  const [elapsedTime, setElapsedTime] = useState<string>('00:00:00');
  const [vehicleInfo, setVehicleInfo] = useState<{
    name: string;
    category: string;
  } | null>(null);

  // Fetch vehicle information
  useEffect(() => {
    const fetchVehicleInfo = async () => {
      if (!trackingData.orderId) return;

      try {
        // In a real implementation, you would fetch the order details
        // which would include the vehicle_type_id, then fetch the vehicle info
        // For now, we'll simulate this with a mock API call
        const response = await fetch(`/api/orders/${trackingData.orderId}`);
        const result = await response.json();

        if (result.success && result.data.vehicle_type_id) {
          const vehicleResponse = await fetch(
            `/api/vehicle-types/${result.data.vehicle_type_id}`
          );
          const vehicleResult = await vehicleResponse.json();

          if (vehicleResult.success) {
            setVehicleInfo({
              name: vehicleResult.data.name,
              category: vehicleResult.data.category,
            });
          }
        }
      } catch (error) {
        console.error('Error fetching vehicle info:', error);
      }
    };

    fetchVehicleInfo();
  }, [trackingData.orderId]);

  // Calculate elapsed time since delivery started
  useEffect(() => {
    if (!trackingData.currentLocation) return;

    const startTime = new Date(trackingData.currentLocation.timestamp);
    const interval = setInterval(() => {
      const now = new Date();
      const diff = now.getTime() - startTime.getTime();

      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      setElapsedTime(
        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      );
    }, 1000);

    return () => clearInterval(interval);
  }, [trackingData.currentLocation]);

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4'></div>
          <p className='text-gray-600'>
            Cargando información de seguimiento...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className='text-red-600'>Error de Seguimiento</CardTitle>
        </CardHeader>
        <CardContent>
          <p className='text-red-500'>{error}</p>
          <Button className='mt-4' onClick={() => window.location.reload()}>
            Reintentar
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Get status display info
  const statusInfo = getStatusInfo(trackingData.status);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Tracking Header */}
      <Card>
        <CardHeader>
          <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
            <div>
              <CardTitle className='flex items-center gap-2'>
                📍 Seguimiento en Tiempo Real
              </CardTitle>
              <CardDescription>
                Pedido #{trackingData.trackingNumber || orderId}
              </CardDescription>
            </div>
            <div className='flex flex-col sm:flex-row gap-2'>
              <Badge className={`${statusInfo.badgeClass} text-sm py-1 px-3`}>
                {statusInfo.label}
              </Badge>
              {vehicleInfo && (
                <div className='flex items-center gap-2'>
                  <VehicleImage
                    vehicleCategory={vehicleInfo.category}
                    className='w-6 h-6 object-contain'
                    alt={vehicleInfo.name}
                  />
                  <Badge className='bg-blue-100 text-blue-800 text-sm py-1 px-3'>
                    {vehicleInfo.name}
                  </Badge>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div className='bg-blue-50 p-4 rounded-lg'>
              <p className='text-sm text-blue-700 font-medium'>Progreso</p>
              <p className='text-2xl font-bold text-blue-900'>
                {Math.round(trackingData.routeProgress)}%
              </p>
            </div>
            <div className='bg-green-50 p-4 rounded-lg'>
              <p className='text-sm text-green-700 font-medium'>
                Tiempo Transcurrido
              </p>
              <p className='text-2xl font-bold text-green-900'>{elapsedTime}</p>
            </div>
            <div className='bg-purple-50 p-4 rounded-lg'>
              <p className='text-sm text-purple-700 font-medium'>Repartidor</p>
              <p className='text-lg font-bold text-purple-900'>
                {trackingData.driverId
                  ? `ID: ${trackingData.driverId.substring(0, 8)}...`
                  : 'Asignando...'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Map View */}
      <Card>
        <CardHeader>
          <CardTitle>🗺️ Ubicación Actual</CardTitle>
          <CardDescription>
            Seguimiento en tiempo real de la ruta de entrega
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='h-96'>
            {trackingData.currentLocation ? (
              <DeliveryMap
                pickupAddress={{
                  lat:
                    trackingData.locationHistory[
                      trackingData.locationHistory.length - 1
                    ]?.lat || 25.6866,
                  lng:
                    trackingData.locationHistory[
                      trackingData.locationHistory.length - 1
                    ]?.lng || -100.3161,
                  name: 'Punto de Recogida',
                }}
                deliveryAddress={{
                  lat: trackingData.currentLocation.lat,
                  lng: trackingData.currentLocation.lng,
                  name: 'Ubicación Actual',
                }}
                className='h-full'
              />
            ) : (
              <div className='bg-gray-100 rounded-lg flex items-center justify-center h-full'>
                <p className='text-gray-600'>
                  Esperando ubicación del repartidor...
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Location Details */}
      {trackingData.currentLocation && (
        <Card>
          <CardHeader>
            <CardTitle>📊 Detalles de Ubicación</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <p className='text-sm text-gray-600'>Latitud</p>
                <p className='font-mono'>
                  {trackingData.currentLocation.lat.toFixed(6)}
                </p>
              </div>
              <div>
                <p className='text-sm text-gray-600'>Longitud</p>
                <p className='font-mono'>
                  {trackingData.currentLocation.lng.toFixed(6)}
                </p>
              </div>
              <div>
                <p className='text-sm text-gray-600'>Velocidad</p>
                <p>
                  {trackingData.currentLocation.speed?.toFixed(1) || 'N/A'} km/h
                </p>
              </div>
              <div>
                <p className='text-sm text-gray-600'>Precisión</p>
                <p>
                  {trackingData.currentLocation.accuracy?.toFixed(1) || 'N/A'}{' '}
                  metros
                </p>
              </div>
              <div className='md:col-span-2'>
                <p className='text-sm text-gray-600'>Última Actualización</p>
                <p>
                  {new Date(
                    trackingData.currentLocation.timestamp
                  ).toLocaleString('es-MX', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                  })}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Progress Bar */}
      <Card>
        <CardHeader>
          <CardTitle>📈 Progreso del Pedido</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            <div className='w-full bg-gray-200 rounded-full h-4'>
              <div
                className='bg-blue-600 h-4 rounded-full transition-all duration-500'
                style={{ width: `${trackingData.routeProgress}%` }}
              ></div>
            </div>
            <div className='flex justify-between text-sm text-gray-600'>
              <span>Inicio</span>
              <span>{Math.round(trackingData.routeProgress)}% completado</span>
              <span>Entregado</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Helper function to get status display information
function getStatusInfo(status: string) {
  switch (status) {
    case 'pending':
      return {
        label: 'Pendiente',
        badgeClass: 'bg-yellow-100 text-yellow-800',
      };
    case 'confirmed':
      return { label: 'Confirmado', badgeClass: 'bg-blue-100 text-blue-800' };
    case 'in-transit':
      return {
        label: 'En Tránsito',
        badgeClass: 'bg-green-100 text-green-800',
      };
    case 'delivered':
      return {
        label: 'Entregado',
        badgeClass: 'bg-purple-100 text-purple-800',
      };
    case 'cancelled':
      return { label: 'Cancelado', badgeClass: 'bg-red-100 text-red-800' };
    default:
      return { label: status, badgeClass: 'bg-gray-100 text-gray-800' };
  }
}
