'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { LoginForm } from '@/components/auth/login-form';
import { AuthLayout } from '@/components/auth/auth-layout';

export default function HomePage() {
  const { user, loading, dbError } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    // Don't redirect while loading
    if (loading) {
      return;
    }

    // Only redirect if user exists and no database error
    if (user && !dbError) {
      router.push('/dashboard');
    }
  }, [user, loading, dbError, router]);

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-900'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-white'></div>
      </div>
    );
  }

  if (user && !dbError) {
    return null; // Will redirect to dashboard
  }

  return (
    <AuthLayout>
      <div className='w-full max-w-md'>
        <div className='text-center mb-8'>
          <h1 className='text-3xl font-bold text-white mb-2'>Mouvers</h1>
          <p className='text-gray-300 mb-4'>
            Accede a tu cuenta para gestionar entregas
          </p>
        </div>

        <LoginForm />
      </div>
    </AuthLayout>
  );
}
