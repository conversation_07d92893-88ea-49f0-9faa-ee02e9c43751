'use client';

import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { VehicleSelector } from '@/components/forms/vehicle-selection/VehicleSelector';
import { OrderFormData } from '@/types/order-form';
import { MEXICAN_CARGO_TYPES } from '@/lib/data/mexican-vehicles';

// Define types for cargo
interface CargoType {
  id: string;
  name: string;
}

interface Step8Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
  errors?: Record<string, string>;
}

export function OrderWizardStep8({ formData, updateFormData }: Step8Props) {
  const [cargoTypes, setCargoTypes] = useState<CargoType[]>([]);

  useEffect(() => {
    // Use static cargo types from mexican-vehicles data
    const staticCargoTypes = MEXICAN_CARGO_TYPES.map(cargoType => ({
      id: cargoType.id,
      name: cargoType.name,
    }));
    setCargoTypes(staticCargoTypes);
  }, []);

  return (
    <div className='space-y-6'>
      {/* Enhanced Vehicle Selection */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            🚚 Selección de Vehículo
          </CardTitle>
          <CardDescription>
            Selecciona el vehículo más adecuado para tu carga
          </CardDescription>
        </CardHeader>
        <CardContent>
          <VehicleSelector
            products={formData.products}
            selectedVehicleId={formData.vehicle_type_id}
            onVehicleSelect={vehicleId =>
              updateFormData({ vehicle_type_id: vehicleId })
            }
          />
        </CardContent>
      </Card>

      {/* Additional Logistics Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            ⚙️ Configuración Adicional
          </CardTitle>
          <CardDescription>
            Detalles específicos de la logística mexicana
          </CardDescription>
        </CardHeader>
        <CardContent className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          {/* Cargo Type */}
          <div className='space-y-2'>
            <Label htmlFor='cargo_type_id'>Clasificación de Carga</Label>
            <select
              id='cargo_type_id'
              value={formData.cargo_type_id}
              onChange={e =>
                updateFormData({
                  cargo_type_id: e.target.value,
                })
              }
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            >
              <option value=''>Seleccionar clasificación</option>
              {cargoTypes.map(cargo => (
                <option key={cargo.id} value={cargo.id}>
                  {cargo.name}
                </option>
              ))}
            </select>
          </div>

          {/* Scheduled Pickup Time */}
          <div className='space-y-2'>
            <Label htmlFor='scheduled_pickup_time'>
              Hora de Recolección Programada
            </Label>
            <Input
              id='scheduled_pickup_time'
              type='time'
              value={formData.scheduled_pickup_time || ''}
              onChange={e =>
                updateFormData({
                  scheduled_pickup_time: e.target.value,
                })
              }
            />
          </div>

          {/* Scheduled Delivery Time */}
          <div className='space-y-2'>
            <Label htmlFor='scheduled_delivery_time'>
              Hora de Entrega Programada
            </Label>
            <Input
              id='scheduled_delivery_time'
              type='time'
              value={formData.scheduled_delivery_time || ''}
              onChange={e =>
                updateFormData({
                  scheduled_delivery_time: e.target.value,
                })
              }
            />
          </div>

          {/* Delivery Instructions */}
          <div className='space-y-2'>
            <Label htmlFor='delivery_instructions'>
              Instrucciones de Entrega
            </Label>
            <Textarea
              id='delivery_instructions'
              value={formData.delivery_instructions || ''}
              onChange={e =>
                updateFormData({
                  delivery_instructions: e.target.value,
                })
              }
              placeholder='Instrucciones específicas para la entrega'
              rows={3}
            />
          </div>

          {/* Special Handling Notes */}
          <div className='space-y-2 md:col-span-2'>
            <Label htmlFor='special_handling_notes'>
              Notas de Manejo Especial
            </Label>
            <Textarea
              id='special_handling_notes'
              value={formData.special_handling_notes || ''}
              onChange={e =>
                updateFormData({
                  special_handling_notes: e.target.value,
                })
              }
              placeholder='Notas sobre el manejo especial de la carga'
              rows={3}
            />
          </div>

          {/* Route Optimization */}
          <div className='space-y-2'>
            <Label htmlFor='route_optimization'>Optimización de Ruta</Label>
            <select
              id='route_optimization'
              value={formData.route_optimization}
              onChange={e =>
                updateFormData({
                  route_optimization: e.target.value as
                    | 'balanced'
                    | 'fastest'
                    | 'shortest'
                    | 'eco',
                })
              }
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            >
              <option value='balanced'>Balanceada</option>
              <option value='fastest'>Más Rápida</option>
              <option value='shortest'>Más Corta</option>
              <option value='eco'>Eco-Friendly</option>
            </select>
          </div>

          {/* Delivery Zone */}
          <div className='space-y-2'>
            <Label htmlFor='delivery_zone'>Zona de Entrega</Label>
            <Input
              id='delivery_zone'
              value={formData.delivery_zone || ''}
              onChange={e =>
                updateFormData({
                  delivery_zone: e.target.value,
                })
              }
              placeholder='ej., Centro, Norte, Sur'
            />
          </div>

          {/* Delivery Region */}
          <div className='space-y-2'>
            <Label htmlFor='delivery_region'>Región de Entrega</Label>
            <select
              id='delivery_region'
              value={formData.delivery_region}
              onChange={e =>
                updateFormData({
                  delivery_region: e.target.value as
                    | 'local'
                    | 'regional'
                    | 'national'
                    | 'international',
                })
              }
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            >
              <option value='local'>Local</option>
              <option value='regional'>Regional</option>
              <option value='national'>Nacional</option>
              <option value='international'>Internacional</option>
            </select>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
