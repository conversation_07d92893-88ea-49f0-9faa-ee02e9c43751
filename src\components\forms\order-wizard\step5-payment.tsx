'use client';

import React from 'react';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { OrderFormData } from '@/types/order-form';

interface Step5Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
}

export function OrderWizardStep5({ formData, updateFormData }: Step5Props) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          💳 Método de Pago
        </CardTitle>
        <CardDescription>
          Selecciona cómo deseas pagar tu pedido
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='space-y-2'>
          <Label>Método de Pago</Label>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <label className='flex items-center space-x-2 p-3 border rounded-lg cursor-pointer hover:bg-gray-50'>
              <input
                type='radio'
                name='payment_method'
                value='card'
                checked={formData.payment_method === 'card'}
                onChange={e =>
                  updateFormData({
                    payment_method: e.target.value as
                      | 'card'
                      | 'cash'
                      | 'digital_wallet'
                      | 'bank_transfer',
                  })
                }
                className='text-blue-600'
              />
              <span>💳 Tarjeta de crédito/débito</span>
            </label>
            <label className='flex items-center space-x-2 p-3 border rounded-lg cursor-pointer hover:bg-gray-50'>
              <input
                type='radio'
                name='payment_method'
                value='cash'
                checked={formData.payment_method === 'cash'}
                onChange={e =>
                  updateFormData({
                    payment_method: e.target.value as
                      | 'card'
                      | 'cash'
                      | 'digital_wallet'
                      | 'bank_transfer',
                  })
                }
                className='text-blue-600'
              />
              <span>💵 Efectivo contra entrega</span>
            </label>
            <label className='flex items-center space-x-2 p-3 border rounded-lg cursor-pointer hover:bg-gray-50'>
              <input
                type='radio'
                name='payment_method'
                value='digital_wallet'
                checked={formData.payment_method === 'digital_wallet'}
                onChange={e =>
                  updateFormData({
                    payment_method: e.target.value as
                      | 'card'
                      | 'cash'
                      | 'digital_wallet'
                      | 'bank_transfer',
                  })
                }
                className='text-blue-600'
              />
              <span>📱 Billetera digital</span>
            </label>
            <label className='flex items-center space-x-2 p-3 border rounded-lg cursor-pointer hover:bg-gray-50'>
              <input
                type='radio'
                name='payment_method'
                value='bank_transfer'
                checked={formData.payment_method === 'bank_transfer'}
                onChange={e =>
                  updateFormData({
                    payment_method: e.target.value as
                      | 'card'
                      | 'cash'
                      | 'digital_wallet'
                      | 'bank_transfer',
                  })
                }
                className='text-blue-600'
              />
              <span>🏦 Transferencia bancaria</span>
            </label>
          </div>
        </div>
        <div className='space-y-2'>
          <Label>Facturación</Label>
          <div className='flex gap-4'>
            <label className='flex items-center space-x-2'>
              <input
                type='radio'
                name='invoice_required'
                value='true'
                checked={formData.invoice_required === true}
                onChange={e =>
                  updateFormData({
                    invoice_required: e.target.value === 'true',
                  })
                }
                className='text-blue-600'
              />
              <span>✅ Sí, requiero factura</span>
            </label>
            <label className='flex items-center space-x-2'>
              <input
                type='radio'
                name='invoice_required'
                value='false'
                checked={formData.invoice_required === false}
                onChange={e =>
                  updateFormData({
                    invoice_required: e.target.value === 'true',
                  })
                }
                className='text-blue-600'
              />
              <span>❌ No, ticket de compra</span>
            </label>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
