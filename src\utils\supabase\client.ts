import { createBrowserClient } from '@supabase/ssr';

export const createClient = () =>
  createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      // Updated configuration for ECC P-256 JWT keys
      auth: {
        flowType: 'pkce', // Use PKCE flow for enhanced security with ECC keys
        persistSession: true,
        detectSessionInUrl: true,
        autoRefreshToken: true,
        // Enhanced JWT validation for ECC keys
        storage: undefined, // Use default storage with JWT improvements
      },
      cookies: {
        get(name: string) {
          if (typeof document === 'undefined') return undefined;
          const cookieString = document.cookie;
          const entries = cookieString.split('; ').map(part => part.split('='));
          for (const [key, ...rest] of entries) {
            if (key === name) {
              return rest.join('=');
            }
          }
          return undefined;
        },
        set(name: string, value: string, options: { maxAge?: number } = {}) {
          if (typeof document === 'undefined') return;
          // Enhanced cookie security for JWT tokens
          const secure =
            process.env.NODE_ENV === 'production' ? '; secure' : '';
          const sameSite = '; SameSite=Lax';
          const maxAge = options.maxAge || 60 * 60 * 24 * 7; // 7 days default
          document.cookie = `${name}=${value}; Path=/; Max-Age=${maxAge}${sameSite}${secure}`;
        },
        remove(name: string) {
          if (typeof document === 'undefined') return;
          const secure =
            process.env.NODE_ENV === 'production' ? '; secure' : '';
          document.cookie = `${name}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax${secure}`;
        },
      },
    }
  );
