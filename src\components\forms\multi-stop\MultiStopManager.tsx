'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Stop } from '@/lib/validation/order-schemas';

interface MultiStopManagerProps {
  stops: Stop[];
  onStopsChange: (stops: Stop[]) => void;
  deliveryMode: 'single' | 'multi';
  onDeliveryModeChange: (mode: 'single' | 'multi') => void;
  className?: string;
}

export function MultiStopManager({
  stops,
  onStopsChange,
  deliveryMode,
  onDeliveryModeChange,
  className = ''
}: MultiStopManagerProps) {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  const addStop = () => {
    const newStop: Stop = {
      id: Date.now().toString(),
      order: stops.length + 1,
      recipient_name: '',
      recipient_phone: '',
      address: {
        id: Date.now().toString(),
        street: '',
        number: '',
        colony: '',
        city: '',
        state: 'Ciudad de México',
        zip: '',
        references: '',
      },
      scheduled_time: '',
      delivery_instructions: '',
      products: [],
    };
    onStopsChange([...stops, newStop]);
  };

  const removeStop = (stopId: string) => {
    const updatedStops = stops
      .filter(stop => stop.id !== stopId)
      .map((stop, index) => ({ ...stop, order: index + 1 }));
    onStopsChange(updatedStops);
  };

  const updateStop = (stopId: string, field: string, value: any) => {
    const updatedStops = stops.map(stop => {
      if (stop.id === stopId) {
        if (field.startsWith('address.')) {
          const addressField = field.replace('address.', '');
          return {
            ...stop,
            address: {
              ...stop.address,
              [addressField]: value
            }
          };
        }
        return { ...stop, [field]: value };
      }
      return stop;
    });
    onStopsChange(updatedStops);
  };

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (draggedIndex === null) return;

    const newStops = [...stops];
    const draggedStop = newStops[draggedIndex];
    newStops.splice(draggedIndex, 1);
    newStops.splice(dropIndex, 0, draggedStop);

    // Update order numbers
    const reorderedStops = newStops.map((stop, index) => ({
      ...stop,
      order: index + 1
    }));

    onStopsChange(reorderedStops);
    setDraggedIndex(null);
  };

  const calculateEstimatedTime = (stopIndex: number) => {
    // Simple estimation: 30 minutes per stop + 15 minutes travel time
    const baseTime = new Date();
    baseTime.setHours(baseTime.getHours() + (stopIndex * 0.75));
    return baseTime.toLocaleTimeString('es-MX', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className={className}>
      {/* Delivery Mode Selection */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Modalidad de Entrega</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                deliveryMode === 'single'
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => onDeliveryModeChange('single')}
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl">📍</div>
                <div>
                  <h3 className="font-semibold">Punto a Punto</h3>
                  <p className="text-sm text-gray-600">
                    Entrega directa a una sola dirección
                  </p>
                </div>
              </div>
            </div>
            
            <div
              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                deliveryMode === 'multi'
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => onDeliveryModeChange('multi')}
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl">🗺️</div>
                <div>
                  <h3 className="font-semibold">Multi-Parada</h3>
                  <p className="text-sm text-gray-600">
                    Múltiples entregas en una sola ruta
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Multi-Stop Management */}
      {deliveryMode === 'multi' && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Gestión de Paradas</CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  Arrastra las paradas para reordenar la ruta
                </p>
              </div>
              <Button onClick={addStop} variant="outline">
                + Agregar Parada
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {stops.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <div className="text-4xl mb-2">📦</div>
                <p>No hay paradas configuradas</p>
                <p className="text-sm">Haz clic en "Agregar Parada" para comenzar</p>
              </div>
            ) : (
              <div className="space-y-4">
                {stops.map((stop, index) => (
                  <div
                    key={stop.id}
                    draggable
                    onDragStart={(e) => handleDragStart(e, index)}
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, index)}
                    className={`border rounded-lg p-4 cursor-move transition-all ${
                      draggedIndex === index ? 'opacity-50' : 'hover:shadow-md'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <Badge variant="outline">Parada #{stop.order}</Badge>
                        <div className="text-sm text-gray-600">
                          ETA: {calculateEstimatedTime(index)}
                        </div>
                      </div>
                      {stops.length > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeStop(stop.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          Eliminar
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Recipient Information */}
                      <div className="space-y-3">
                        <h4 className="font-medium text-gray-900">Destinatario</h4>
                        <div className="space-y-2">
                          <div>
                            <Label htmlFor={`recipient_name_${stop.id}`}>
                              Nombre Completo
                            </Label>
                            <Input
                              id={`recipient_name_${stop.id}`}
                              value={stop.recipient_name}
                              onChange={(e) =>
                                updateStop(stop.id, 'recipient_name', e.target.value)
                              }
                              placeholder="Nombre del destinatario"
                              required
                            />
                          </div>
                          <div>
                            <Label htmlFor={`recipient_phone_${stop.id}`}>
                              Teléfono
                            </Label>
                            <Input
                              id={`recipient_phone_${stop.id}`}
                              value={stop.recipient_phone}
                              onChange={(e) =>
                                updateStop(stop.id, 'recipient_phone', e.target.value)
                              }
                              placeholder="+52 55 1234 5678"
                              required
                            />
                          </div>
                        </div>
                      </div>

                      {/* Address Information */}
                      <div className="space-y-3">
                        <h4 className="font-medium text-gray-900">Dirección</h4>
                        <div className="grid grid-cols-2 gap-2">
                          <Input
                            value={stop.address.street}
                            onChange={(e) =>
                              updateStop(stop.id, 'address.street', e.target.value)
                            }
                            placeholder="Calle"
                            required
                          />
                          <Input
                            value={stop.address.number || ''}
                            onChange={(e) =>
                              updateStop(stop.id, 'address.number', e.target.value)
                            }
                            placeholder="Número"
                          />
                          <Input
                            value={stop.address.colony}
                            onChange={(e) =>
                              updateStop(stop.id, 'address.colony', e.target.value)
                            }
                            placeholder="Colonia"
                            required
                          />
                          <Input
                            value={stop.address.zip}
                            onChange={(e) =>
                              updateStop(stop.id, 'address.zip', e.target.value)
                            }
                            placeholder="C.P."
                            required
                          />
                        </div>
                      </div>
                    </div>

                    {/* Delivery Instructions */}
                    <div className="mt-4">
                      <Label htmlFor={`instructions_${stop.id}`}>
                        Instrucciones de Entrega
                      </Label>
                      <Textarea
                        id={`instructions_${stop.id}`}
                        value={stop.delivery_instructions || ''}
                        onChange={(e) =>
                          updateStop(stop.id, 'delivery_instructions', e.target.value)
                        }
                        placeholder="Instrucciones específicas para esta parada..."
                        rows={2}
                      />
                    </div>
                  </div>
                ))}

                {/* Route Summary */}
                {stops.length > 1 && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="font-medium text-blue-800 mb-2">
                      Resumen de Ruta
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold text-blue-600">
                          {stops.length}
                        </div>
                        <div className="text-sm text-blue-700">Paradas</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-blue-600">
                          ~{Math.round(stops.length * 0.75 * 60)}
                        </div>
                        <div className="text-sm text-blue-700">Minutos Est.</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-blue-600">
                          ~{Math.round(stops.length * 15)}
                        </div>
                        <div className="text-sm text-blue-700">km Est.</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
