import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import {
  secureAPI,
  sanitizeInput,
  isValidUUID,
} from '@/lib/security/api-security';
import { auditLog } from '@/lib/security/audit-logger';

// GET /api/cargo-types/[id] - Get a specific cargo type by ID
export const GET = secureAPI(
  {
    GET: async (request, context) => {
      try {
        // Extract ID from URL parameters
        const url = new URL(request.url);
        const id = url.pathname.split('/').pop();

        // Validate UUID format
        if (!id || !isValidUUID(id)) {
          return NextResponse.json(
            { error: 'ID de tipo de carga inválido' },
            { status: 400 }
          );
        }

        const supabase = await createClient();

        // Get specific cargo type by ID
        const { data, error } = await supabase
          .from('cargo_types')
          .select('*')
          .eq('id', id)
          .single();

        if (error) {
          console.error('Database error:', error);
          await auditLog({
            event: 'CARGO_TYPE_FETCH_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { error: error.message, cargoTypeId: id },
            severity: 'MEDIUM',
            category: 'SYSTEM',
          });

          if (error.code === 'PGRST116') {
            return NextResponse.json(
              { error: 'Tipo de carga no encontrado' },
              { status: 404 }
            );
          }

          return NextResponse.json(
            { error: 'Error al obtener el tipo de carga' },
            { status: 500 }
          );
        }

        if (!data) {
          return NextResponse.json(
            { error: 'Tipo de carga no encontrado' },
            { status: 404 }
          );
        }

        await auditLog({
          event: 'CARGO_TYPE_FETCHED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { cargoTypeId: id },
          severity: 'LOW',
          category: 'DATA',
        });

        return NextResponse.json({
          success: true,
          data,
        });
      } catch (error) {
        await auditLog({
          event: 'CARGO_TYPE_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: false,
    rateLimitRpm: 60,
  }
);

// PUT /api/cargo-types/[id] - Update a specific cargo type (admin only)
export const PUT = secureAPI(
  {
    PUT: async (request, context) => {
      // Check if user is admin
      if (context.user?.role !== 'admin') {
        await auditLog({
          event: 'UNAUTHORIZED_CARGO_TYPE_UPDATE_ATTEMPT',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { userRole: context.user?.role },
          severity: 'HIGH',
          category: 'SECURITY',
        });

        return NextResponse.json(
          {
            error: 'Solo los administradores pueden actualizar tipos de carga',
          },
          { status: 403 }
        );
      }

      try {
        // Extract ID from URL parameters
        const url = new URL(request.url);
        const id = url.pathname.split('/').slice(-2)[0];

        // Validate UUID format
        if (!id || !isValidUUID(id)) {
          return NextResponse.json(
            { error: 'ID de tipo de carga inválido' },
            { status: 400 }
          );
        }

        // Parse and sanitize request body
        let requestBody;
        try {
          requestBody = sanitizeInput(await request.json());
        } catch {
          return NextResponse.json(
            { error: 'Formato de cuerpo de solicitud inválido' },
            { status: 400 }
          );
        }

        const {
          name,
          category,
          special_requirements,
          rate_multiplier,
          description,
        } = requestBody as {
          name?: string;
          category?: string;
          special_requirements?: Record<string, unknown>;
          rate_multiplier?: number;
          description?: string;
        };

        const supabase = await createClient();

        // Update cargo type
        const { data, error } = await supabase
          .from('cargo_types')
          .update({
            name,
            category,
            special_requirements,
            rate_multiplier,
            description,
            updated_at: new Date().toISOString(),
          })
          .eq('id', id)
          .select()
          .single();

        if (error) {
          console.error('Database error:', error);
          await auditLog({
            event: 'CARGO_TYPE_UPDATE_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: {
              error: error.message,
              cargoTypeId: id,
              requestData: requestBody,
            },
            severity: 'MEDIUM',
            category: 'DATA',
          });

          if (error.code === 'PGRST116') {
            return NextResponse.json(
              { error: 'Tipo de carga no encontrado' },
              { status: 404 }
            );
          }

          return NextResponse.json(
            { error: 'Error al actualizar el tipo de carga' },
            { status: 500 }
          );
        }

        if (!data) {
          return NextResponse.json(
            { error: 'Tipo de carga no encontrado' },
            { status: 404 }
          );
        }

        await auditLog({
          event: 'CARGO_TYPE_UPDATED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { cargoTypeId: id, name: data.name },
          severity: 'MEDIUM',
          category: 'DATA',
        });

        return NextResponse.json({
          success: true,
          message: 'Tipo de carga actualizado exitosamente',
          data,
        });
      } catch (error) {
        await auditLog({
          event: 'CARGO_TYPE_UPDATE_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: true,
    allowedRoles: ['admin'],
    requireMFA: false,
    validateCSRF: true,
    rateLimitRpm: 30,
  }
);

// DELETE /api/cargo-types/[id] - Delete a specific cargo type (admin only)
export const DELETE = secureAPI(
  {
    DELETE: async (request, context) => {
      // Check if user is admin
      if (context.user?.role !== 'admin') {
        await auditLog({
          event: 'UNAUTHORIZED_CARGO_TYPE_DELETE_ATTEMPT',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { userRole: context.user?.role },
          severity: 'HIGH',
          category: 'SECURITY',
        });

        return NextResponse.json(
          { error: 'Solo los administradores pueden eliminar tipos de carga' },
          { status: 403 }
        );
      }

      try {
        // Extract ID from URL parameters
        const url = new URL(request.url);
        const id = url.pathname.split('/').slice(-2)[0];

        // Validate UUID format
        if (!id || !isValidUUID(id)) {
          return NextResponse.json(
            { error: 'ID de tipo de carga inválido' },
            { status: 400 }
          );
        }

        const supabase = await createClient();

        // Check if cargo type exists
        const { data: existingData, error: fetchError } = await supabase
          .from('cargo_types')
          .select('name')
          .eq('id', id)
          .single();

        if (fetchError) {
          console.error('Database error:', fetchError);
          await auditLog({
            event: 'CARGO_TYPE_DELETE_CHECK_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { error: fetchError.message, cargoTypeId: id },
            severity: 'MEDIUM',
            category: 'DATA',
          });

          if (fetchError.code === 'PGRST116') {
            return NextResponse.json(
              { error: 'Tipo de carga no encontrado' },
              { status: 404 }
            );
          }

          return NextResponse.json(
            { error: 'Error al verificar el tipo de carga' },
            { status: 500 }
          );
        }

        if (!existingData) {
          return NextResponse.json(
            { error: 'Tipo de carga no encontrado' },
            { status: 404 }
          );
        }

        // Delete cargo type
        const { error } = await supabase
          .from('cargo_types')
          .delete()
          .eq('id', id);

        if (error) {
          console.error('Database error:', error);
          await auditLog({
            event: 'CARGO_TYPE_DELETE_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: {
              error: error.message,
              cargoTypeId: id,
              name: existingData.name,
            },
            severity: 'MEDIUM',
            category: 'DATA',
          });
          return NextResponse.json(
            { error: 'Error al eliminar el tipo de carga' },
            { status: 500 }
          );
        }

        await auditLog({
          event: 'CARGO_TYPE_DELETED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { cargoTypeId: id, name: existingData.name },
          severity: 'MEDIUM',
          category: 'DATA',
        });

        return NextResponse.json({
          success: true,
          message: 'Tipo de carga eliminado exitosamente',
        });
      } catch (error) {
        await auditLog({
          event: 'CARGO_TYPE_DELETE_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: true,
    allowedRoles: ['admin'],
    requireMFA: false,
    validateCSRF: true,
    rateLimitRpm: 30,
  }
);
