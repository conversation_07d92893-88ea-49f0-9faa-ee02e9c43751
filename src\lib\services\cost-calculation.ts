/**
 * Comprehensive Cost Calculation Service for Mexican Logistics
 * 
 * Calculates real-time pricing based on multiple factors including:
 * - Vehicle type and capacity
 * - Distance and route optimization
 * - Special handling requirements
 * - Time-based pricing
 * - Multi-stop surcharges
 */

import { MEXICAN_VEHICLE_TYPES, MEXICAN_CARGO_TYPES } from '@/lib/data/mexican-vehicles';
import { ProductItem, Stop } from '@/lib/validation/order-schemas';

export interface CostCalculationRequest {
  products: ProductItem[];
  vehicleTypeId?: string;
  cargoTypeId?: string;
  deliveryMode: 'single' | 'multi';
  stops?: Stop[];
  deliveryDate: string;
  deliveryTimeSlot: string;
  deliveryRegion: 'local' | 'regional' | 'national' | 'international';
  specialHandlingRequired: boolean;
  origin?: {
    lat: number;
    lng: number;
    address: string;
  };
  destination?: {
    lat: number;
    lng: number;
    address: string;
  };
}

export interface CostBreakdown {
  baseCost: number;
  vehicleCost: number;
  distanceCost: number;
  specialHandlingCost: number;
  timeSlotCost: number;
  multiStopCost: number;
  urgencyCost: number;
  regionalCost: number;
  subtotal: number;
  taxes: number;
  total: number;
  currency: 'MXN';
  breakdown: {
    label: string;
    amount: number;
    description: string;
  }[];
}

export class CostCalculationService {
  private static readonly BASE_RATES = {
    local: 50,      // Base rate for local deliveries
    regional: 100,  // Base rate for regional deliveries
    national: 200,  // Base rate for national deliveries
    international: 500, // Base rate for international deliveries
  };

  private static readonly TIME_SLOT_MULTIPLIERS = {
    '08:00-11:00': 1.0,   // Standard rate
    '11:00-14:00': 1.1,   // Peak hours
    '14:00-17:00': 1.2,   // Peak hours
    '17:00-20:00': 1.15,  // Evening premium
    '08:00-10:00': 1.0,
    '10:00-12:00': 1.1,
    '12:00-14:00': 1.2,
    '14:00-16:00': 1.2,
    '16:00-18:00': 1.15,
    '18:00-20:00': 1.15,
  };

  private static readonly SPECIAL_HANDLING_RATES = {
    fragile: 25,
    perishable: 50,
    valuable: 75,
    hazardous: 100,
    refrigerated: 80,
    oversized: 150,
  };

  static calculateCost(request: CostCalculationRequest): CostBreakdown {
    const breakdown: { label: string; amount: number; description: string; }[] = [];
    
    // 1. Base cost calculation
    const baseCost = this.BASE_RATES[request.deliveryRegion];
    breakdown.push({
      label: 'Costo base',
      amount: baseCost,
      description: `Tarifa base para entrega ${request.deliveryRegion}`
    });

    // 2. Vehicle cost calculation
    const vehicleCost = this.calculateVehicleCost(request.vehicleTypeId, request.products);
    if (vehicleCost > 0) {
      breakdown.push({
        label: 'Costo de vehículo',
        amount: vehicleCost,
        description: 'Tarifa específica del tipo de vehículo seleccionado'
      });
    }

    // 3. Distance cost calculation
    const distanceCost = this.calculateDistanceCost(request);
    breakdown.push({
      label: 'Costo por distancia',
      amount: distanceCost,
      description: 'Cálculo basado en la distancia estimada de la ruta'
    });

    // 4. Special handling cost
    const specialHandlingCost = this.calculateSpecialHandlingCost(request.products);
    if (specialHandlingCost > 0) {
      breakdown.push({
        label: 'Manejo especial',
        amount: specialHandlingCost,
        description: 'Cargos adicionales por requerimientos especiales'
      });
    }

    // 5. Time slot premium
    const timeSlotCost = this.calculateTimeSlotCost(baseCost, request.deliveryTimeSlot);
    if (timeSlotCost > 0) {
      breakdown.push({
        label: 'Horario premium',
        amount: timeSlotCost,
        description: 'Cargo adicional por horario específico'
      });
    }

    // 6. Multi-stop surcharge
    const multiStopCost = this.calculateMultiStopCost(request.stops || []);
    if (multiStopCost > 0) {
      breakdown.push({
        label: 'Paradas múltiples',
        amount: multiStopCost,
        description: `Cargo por ${(request.stops || []).length} paradas adicionales`
      });
    }

    // 7. Urgency cost (same day delivery)
    const urgencyCost = this.calculateUrgencyCost(request.deliveryDate);
    if (urgencyCost > 0) {
      breakdown.push({
        label: 'Entrega urgente',
        amount: urgencyCost,
        description: 'Cargo por entrega el mismo día'
      });
    }

    // 8. Regional cost adjustment
    const regionalCost = this.calculateRegionalCost(request.deliveryRegion, baseCost);
    if (regionalCost > 0) {
      breakdown.push({
        label: 'Ajuste regional',
        amount: regionalCost,
        description: 'Ajuste por zona de entrega'
      });
    }

    // Calculate subtotal
    const subtotal = baseCost + vehicleCost + distanceCost + specialHandlingCost + 
                    timeSlotCost + multiStopCost + urgencyCost + regionalCost;

    // Calculate taxes (16% IVA in Mexico)
    const taxes = subtotal * 0.16;
    breakdown.push({
      label: 'IVA (16%)',
      amount: taxes,
      description: 'Impuesto al Valor Agregado'
    });

    const total = subtotal + taxes;

    return {
      baseCost,
      vehicleCost,
      distanceCost,
      specialHandlingCost,
      timeSlotCost,
      multiStopCost,
      urgencyCost,
      regionalCost,
      subtotal,
      taxes,
      total,
      currency: 'MXN',
      breakdown
    };
  }

  private static calculateVehicleCost(vehicleTypeId?: string, products: ProductItem[] = []): number {
    if (!vehicleTypeId) return 0;

    const vehicle = MEXICAN_VEHICLE_TYPES.find(v => v.id === vehicleTypeId);
    if (!vehicle) return 0;

    // Calculate total weight and volume
    const totalWeight = products.reduce((sum, product) => {
      const weight = product.weight || 1;
      const weightInKg = product.weight_unit === 'g' ? weight / 1000 : weight;
      return sum + (weightInKg * product.quantity);
    }, 0);

    // Base vehicle cost + utilization factor
    const utilizationFactor = Math.min(totalWeight / vehicle.max_weight_kg, 1);
    return vehicle.base_rate_per_km * 15 * (0.5 + utilizationFactor * 0.5); // Assuming 15km average
  }

  private static calculateDistanceCost(request: CostCalculationRequest): number {
    // Mock distance calculation - in real implementation, use Google Maps API
    const baseDistance = request.deliveryRegion === 'local' ? 15 : 
                        request.deliveryRegion === 'regional' ? 50 :
                        request.deliveryRegion === 'national' ? 200 : 500;
    
    const additionalStops = (request.stops || []).length;
    const totalDistance = baseDistance + (additionalStops * 5); // 5km per additional stop
    
    return totalDistance * 2.5; // $2.50 per km
  }

  private static calculateSpecialHandlingCost(products: ProductItem[]): number {
    let totalCost = 0;
    
    products.forEach(product => {
      if (!product.special_handling) return;
      
      Object.entries(product.special_handling).forEach(([key, value]) => {
        if (value && key in this.SPECIAL_HANDLING_RATES) {
          totalCost += this.SPECIAL_HANDLING_RATES[key as keyof typeof this.SPECIAL_HANDLING_RATES] * product.quantity;
        }
      });
    });
    
    return totalCost;
  }

  private static calculateTimeSlotCost(baseCost: number, timeSlot: string): number {
    const multiplier = this.TIME_SLOT_MULTIPLIERS[timeSlot as keyof typeof this.TIME_SLOT_MULTIPLIERS] || 1.0;
    return baseCost * (multiplier - 1.0);
  }

  private static calculateMultiStopCost(stops: Stop[]): number {
    if (stops.length <= 1) return 0;
    return (stops.length - 1) * 25; // $25 per additional stop
  }

  private static calculateUrgencyCost(deliveryDate: string): number {
    const today = new Date();
    const delivery = new Date(deliveryDate);
    const diffTime = delivery.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays <= 0) return 100; // Same day delivery
    if (diffDays === 1) return 50;  // Next day delivery
    return 0; // Standard delivery
  }

  private static calculateRegionalCost(region: string, baseCost: number): number {
    const multipliers = {
      local: 0,
      regional: 0.2,
      national: 0.5,
      international: 1.0
    };
    
    return baseCost * (multipliers[region as keyof typeof multipliers] || 0);
  }

  // Utility method to get estimated delivery time
  static getEstimatedDeliveryTime(request: CostCalculationRequest): {
    minHours: number;
    maxHours: number;
    description: string;
  } {
    const baseHours = {
      local: { min: 2, max: 6 },
      regional: { min: 12, max: 24 },
      national: { min: 24, max: 72 },
      international: { min: 72, max: 168 }
    };
    
    const base = baseHours[request.deliveryRegion];
    const additionalStops = (request.stops || []).length;
    const stopDelay = additionalStops * 0.5; // 30 minutes per additional stop
    
    return {
      minHours: base.min + stopDelay,
      maxHours: base.max + stopDelay,
      description: `Tiempo estimado de entrega para región ${request.deliveryRegion}${
        additionalStops > 0 ? ` con ${additionalStops} paradas adicionales` : ''
      }`
    };
  }
}
