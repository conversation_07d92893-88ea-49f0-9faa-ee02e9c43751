'use client';

import { useState, useEffect } from 'react';
import { useAuthStore } from '@/stores/authStore';

interface CompleteProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function CompleteProfileModal({
  isOpen,
  onClose,
}: CompleteProfileModalProps) {
  const [formData, setFormData] = useState({
    fullName: '',
    phone: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { completeProfile, profile } = useAuthStore();

  // Pre-llenar con datos existentes si los hay
  useEffect(() => {
    if (profile) {
      setFormData({
        fullName: profile.full_name || '',
        phone: profile.phone || '',
      });
    }
  }, [profile]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const validateForm = () => {
    if (!formData.fullName.trim()) {
      setError('El nombre completo es requerido');
      return false;
    }
    if (!formData.phone.trim()) {
      setError('El teléfono es requerido');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    if (!validateForm()) {
      setLoading(false);
      return;
    }

    const { error } = await completeProfile(formData.fullName, formData.phone);

    if (error) {
      setError(error.message || 'Error al completar perfil');
    } else {
      onClose(); // Cerrar modal al completar
    }

    setLoading(false);
  };

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
      <div className='bg-white rounded-lg p-8 max-w-md w-full mx-4'>
        <div className='text-center mb-6'>
          <h2 className='text-2xl font-bold text-gray-900 mb-2'>
            Completa tu Perfil de Repartidor
          </h2>
          <p className='text-sm text-gray-600'>
            Necesitamos algunos datos para activar tu cuenta como repartidor
          </p>
        </div>

        <form onSubmit={handleSubmit} className='space-y-4'>
          <div>
            <label
              htmlFor='fullName'
              className='block text-sm font-medium text-gray-700 mb-1'
            >
              Nombre Completo
            </label>
            <input
              id='fullName'
              name='fullName'
              type='text'
              required
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500'
              placeholder='Tu nombre completo'
              value={formData.fullName}
              onChange={handleChange}
            />
          </div>

          <div>
            <label
              htmlFor='phone'
              className='block text-sm font-medium text-gray-700 mb-1'
            >
              Teléfono
            </label>
            <input
              id='phone'
              name='phone'
              type='tel'
              required
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500'
              placeholder='+1234567890'
              value={formData.phone}
              onChange={handleChange}
            />
          </div>

          {error && (
            <div className='rounded-md bg-red-50 p-3'>
              <div className='text-sm text-red-700'>{error}</div>
            </div>
          )}

          <div className='flex space-x-3 pt-4'>
            <button
              type='button'
              onClick={onClose}
              className='flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500'
            >
              Cancelar
            </button>
            <button
              type='submit'
              disabled={loading}
              className='flex-1 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50'
            >
              {loading ? 'Guardando...' : 'Completar Perfil'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
