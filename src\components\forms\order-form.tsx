'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { OrderFormData, OrderFormProps, ProductItem } from '@/types/order-form';

export function OrderForm({
  onSubmit,
  onCancel,
  loading = false,
}: OrderFormProps) {
  const [formData, setFormData] = useState<OrderFormData>({
    // Customer Information
    customer_name: '',
    customer_phone: '',
    customer_email: '',
    customer_type: 'individual',

    // Delivery Address
    delivery_address: {
      id: '1',
      street: '',
      number: '',
      colony: '',
      city: '',
      state: 'Ciudad de México', // Changed from '' to a valid default state
      zip: '',
      references: '',
    },

    pickup_address: {
      id: '2',
      street: '',
      number: '',
      colony: '',
      city: '',
      state: 'Ciudad de México', // Changed from '' to a valid default state
      zip: '',
      references: '',
    },

    // Shopping Cart
    products: [
      {
        id: '1',
        name: '',
        quantity: 1,
        unit_measure: 'kg',
        unit_price: 0,
        subtotal: 0,
        weight: 0,
        weight_unit: 'kg',
        dimensions: {
          length: 0,
          width: 0,
          height: 0,
          unit: 'cm',
        },
        special_handling: {
          fragile: false,
          perishable: false,
          valuable: false,
          hazardous: false,
          refrigerated: false,
          oversized: false,
        },
        notes: '',
      },
    ],

    // Delivery Options
    delivery_mode: 'home',
    stops: [],
    delivery_date: '',
    delivery_time_slot: '08:00-11:00',
    pickup_time_slot: '08:00-11:00',

    // Payment
    payment_method: 'card',
    invoice_required: false,
    payment_method_details: {},

    // Additional Notes
    special_instructions: '',
    allow_substitutions: false,
    communication_preferences: {
      sms_notifications: true,
      email_notifications: true,
      whatsapp_notifications: false,
      call_before_delivery: false,
    },

    // Mexican Logistics Configuration
    route_optimization: 'balanced',
    delivery_region: 'local',
    regulatory_requirements: {},

    // Order Confirmation
    terms_accepted: false,
  });

  // Helper functions for products
  const addProduct = () => {
    const newProduct: ProductItem = {
      id: Date.now().toString(),
      name: '',
      quantity: 1,
      unit_measure: 'kg',
      unit_price: 0,
      subtotal: 0,
      weight: 0,
      weight_unit: 'kg',
      dimensions: {
        length: 0,
        width: 0,
        height: 0,
        unit: 'cm',
      },
      special_handling: {
        fragile: false,
        perishable: false,
        valuable: false,
        hazardous: false,
        refrigerated: false,
        oversized: false,
      },
      notes: '',
    };
    setFormData(prev => ({
      ...prev,
      products: [...prev.products, newProduct],
    }));
  };

  const removeProduct = (id: string) => {
    setFormData(prev => ({
      ...prev,
      products: prev.products.filter(product => product.id !== id),
    }));
  };

  const updateProduct = (
    id: string,
    field: keyof ProductItem,
    value: string | number
  ) => {
    setFormData(prev => ({
      ...prev,
      products: prev.products.map(product => {
        if (product.id === id) {
          const updatedProduct = { ...product, [field]: value };
          // Recalculate subtotal
          if (field === 'quantity' || field === 'unit_price') {
            updatedProduct.subtotal =
              updatedProduct.quantity * updatedProduct.unit_price;
          }
          return updatedProduct;
        }
        return product;
      }),
    }));
  };

  // Calculate total cost
  const totalCost = formData.products.reduce(
    (sum, product) => sum + product.subtotal,
    0
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className='max-w-4xl mx-auto p-6 space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>
            Crear Nuevo Pedido
          </h1>
          <p className='text-gray-600'>
            Completa los detalles para tu pedido de entrega
          </p>
        </div>
        {onCancel && (
          <Button variant='ghost' onClick={onCancel}>
            Cancelar
          </Button>
        )}
      </div>

      <form onSubmit={handleSubmit} className='space-y-6'>
        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              👤 Datos del Cliente
            </CardTitle>
            <CardDescription>
              Información de contacto para el pedido
            </CardDescription>
          </CardHeader>
          <CardContent className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='customer_name'>Nombre Completo</Label>
              <Input
                id='customer_name'
                value={formData.customer_name}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    customer_name: e.target.value,
                  }))
                }
                required
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='customer_phone'>Teléfono de Contacto</Label>
              <Input
                id='customer_phone'
                type='tel'
                value={formData.customer_phone}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    customer_phone: e.target.value,
                  }))
                }
                required
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='customer_email'>
                Correo Electrónico (Opcional)
              </Label>
              <Input
                id='customer_email'
                type='email'
                value={formData.customer_email || ''}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    customer_email: e.target.value,
                  }))
                }
              />
            </div>
          </CardContent>
        </Card>

        {/* Pickup Address */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              🏠 Dirección de Recolección
            </CardTitle>
            <CardDescription>¿Dónde debemos recoger tu pedido?</CardDescription>
          </CardHeader>
          <CardContent className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='pickup_street'>Calle y Número</Label>
              <Input
                id='pickup_street'
                value={formData.pickup_address.street}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    pickup_address: {
                      ...prev.pickup_address,
                      street: e.target.value,
                    },
                  }))
                }
                placeholder='ej., Av. Revolución 1203'
                required
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='pickup_number'>Número (Opcional)</Label>
              <Input
                id='pickup_number'
                value={formData.pickup_address.number || ''}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    pickup_address: {
                      ...prev.pickup_address,
                      number: e.target.value,
                    },
                  }))
                }
                placeholder='ej., 1203'
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='pickup_colony'>Colonia / Barrio</Label>
              <Input
                id='pickup_colony'
                value={formData.pickup_address.colony}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    pickup_address: {
                      ...prev.pickup_address,
                      colony: e.target.value,
                    },
                  }))
                }
                placeholder='ej., Del Valle'
                required
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='pickup_city'>Ciudad</Label>
              <Input
                id='pickup_city'
                value={formData.pickup_address.city}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    pickup_address: {
                      ...prev.pickup_address,
                      city: e.target.value,
                    },
                  }))
                }
                placeholder='ej., Monterrey'
                required
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='pickup_state'>Estado</Label>
              <select
                id='pickup_state'
                value={formData.pickup_address.state}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    pickup_address: {
                      ...prev.pickup_address,
                      state: e.target
                        .value as OrderFormData['pickup_address']['state'],
                    },
                  }))
                }
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                required
              >
                <option value=''>Seleccionar estado</option>
                <option value='Aguascalientes'>Aguascalientes</option>
                <option value='Baja California'>Baja California</option>
                <option value='Baja California Sur'>Baja California Sur</option>
                <option value='Campeche'>Campeche</option>
                <option value='Chiapas'>Chiapas</option>
                <option value='Chihuahua'>Chihuahua</option>
                <option value='Ciudad de México'>Ciudad de México</option>
                <option value='Coahuila'>Coahuila</option>
                <option value='Colima'>Colima</option>
                <option value='Durango'>Durango</option>
                <option value='Estado de México'>Estado de México</option>
                <option value='Guanajuato'>Guanajuato</option>
                <option value='Guerrero'>Guerrero</option>
                <option value='Hidalgo'>Hidalgo</option>
                <option value='Jalisco'>Jalisco</option>
                <option value='Michoacán'>Michoacán</option>
                <option value='Morelos'>Morelos</option>
                <option value='Nayarit'>Nayarit</option>
                <option value='Nuevo León'>Nuevo León</option>
                <option value='Oaxaca'>Oaxaca</option>
                <option value='Puebla'>Puebla</option>
                <option value='Querétaro'>Querétaro</option>
                <option value='Quintana Roo'>Quintana Roo</option>
                <option value='San Luis Potosí'>San Luis Potosí</option>
                <option value='Sinaloa'>Sinaloa</option>
                <option value='Sonora'>Sonora</option>
                <option value='Tabasco'>Tabasco</option>
                <option value='Tamaulipas'>Tamaulipas</option>
                <option value='Tlaxcala'>Tlaxcala</option>
                <option value='Veracruz'>Veracruz</option>
                <option value='Yucatán'>Yucatán</option>
                <option value='Zacatecas'>Zacatecas</option>
              </select>
            </div>
            <div className='space-y-2'>
              <Label htmlFor='pickup_zip'>Código Postal</Label>
              <Input
                id='pickup_zip'
                value={formData.pickup_address.zip}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    pickup_address: {
                      ...prev.pickup_address,
                      zip: e.target.value,
                    },
                  }))
                }
                placeholder='ej., 64700'
                required
              />
            </div>
            <div className='space-y-2 md:col-span-2'>
              <Label htmlFor='pickup_references'>
                Referencias para Recolección
              </Label>
              <Textarea
                id='pickup_references'
                value={formData.pickup_address.references || ''}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    pickup_address: {
                      ...prev.pickup_address,
                      references: e.target.value,
                    },
                  }))
                }
                placeholder='ej., Frente a parque, portón negro'
                rows={2}
              />
            </div>
          </CardContent>
        </Card>

        {/* Shopping Cart - Product List */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <div>
                <CardTitle className='flex items-center gap-2'>
                  🛒 Carrito de Compras
                </CardTitle>
                <CardDescription>
                  Lista de productos para tu pedido
                </CardDescription>
              </div>
              <Button
                type='button'
                variant='outline'
                onClick={addProduct}
                className='text-sm'
              >
                + Agregar Producto
              </Button>
            </div>
          </CardHeader>
          <CardContent className='space-y-6'>
            {formData.products.map((product, index) => (
              <div
                key={product.id}
                className='border border-gray-200 rounded-lg p-4 space-y-4'
              >
                <div className='flex items-center justify-between'>
                  <h4 className='font-medium text-gray-900'>
                    Producto #{index + 1}
                  </h4>
                  {formData.products.length > 1 && (
                    <Button
                      type='button'
                      variant='ghost'
                      onClick={() => removeProduct(product.id)}
                      className='text-red-600 text-sm'
                    >
                      Eliminar
                    </Button>
                  )}
                </div>

                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <div className='space-y-2'>
                    <Label htmlFor={`product_name_${product.id}`}>
                      Nombre del Producto
                    </Label>
                    <Input
                      id={`product_name_${product.id}`}
                      value={product.name}
                      onChange={e =>
                        updateProduct(product.id, 'name', e.target.value)
                      }
                      placeholder='ej., Manzana roja, Leche deslactosada'
                      required
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor={`product_quantity_${product.id}`}>
                      Cantidad
                    </Label>
                    <Input
                      id={`product_quantity_${product.id}`}
                      type='number'
                      min='0.1'
                      step='0.1'
                      value={product.quantity}
                      onChange={e =>
                        updateProduct(
                          product.id,
                          'quantity',
                          parseFloat(e.target.value) || 0
                        )
                      }
                      required
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor={`product_unit_${product.id}`}>
                      Unidad de Medida
                    </Label>
                    <select
                      id={`product_unit_${product.id}`}
                      value={product.unit_measure}
                      onChange={e =>
                        updateProduct(
                          product.id,
                          'unit_measure',
                          e.target.value
                        )
                      }
                      className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                      required
                    >
                      <option value='kg'>kg</option>
                      <option value='pieza'>Pieza</option>
                      <option value='paquete'>Paquete</option>
                      <option value='litro'>Litro</option>
                      <option value='gramo'>Gramo</option>
                      <option value='unidad'>Unidad</option>
                    </select>
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor={`product_price_${product.id}`}>
                      Precio Unitario (MXN)
                    </Label>
                    <Input
                      id={`product_price_${product.id}`}
                      type='number'
                      min='0'
                      step='0.01'
                      value={product.unit_price}
                      onChange={e =>
                        updateProduct(
                          product.id,
                          'unit_price',
                          parseFloat(e.target.value) || 0
                        )
                      }
                      placeholder='0.00'
                      required
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor={`product_subtotal_${product.id}`}>
                      Subtotal
                    </Label>
                    <Input
                      id={`product_subtotal_${product.id}`}
                      type='text'
                      value={`$${product.subtotal.toFixed(2)}`}
                      disabled
                      className='bg-gray-50'
                    />
                  </div>
                  <div className='space-y-2 md:col-span-2'>
                    <Label htmlFor={`product_notes_${product.id}`}>
                      Observaciones del Producto
                    </Label>
                    <Input
                      id={`product_notes_${product.id}`}
                      value={product.notes || ''}
                      onChange={e =>
                        updateProduct(product.id, 'notes', e.target.value)
                      }
                      placeholder='ej., maduro, sin azúcar, marca específica'
                    />
                  </div>
                </div>
              </div>
            ))}

            {/* Total Summary */}
            <div className='border-t pt-4'>
              <div className='flex justify-between items-center text-lg font-semibold'>
                <span>Total del Pedido:</span>
                <span className='text-green-600'>
                  ${totalCost.toFixed(2)} MXN
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Delivery Options */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              ⏰ Opciones de Entrega
            </CardTitle>
            <CardDescription>
              Cuándo y cómo quieres recibir tu pedido
            </CardDescription>
          </CardHeader>
          <CardContent className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='delivery_date'>Fecha de Entrega Deseada</Label>
              <Input
                id='delivery_date'
                type='date'
                value={formData.delivery_date}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    delivery_date: e.target.value,
                  }))
                }
                required
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='delivery_time_slot'>Franja Horaria</Label>
              <select
                id='delivery_time_slot'
                value={formData.delivery_time_slot}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    delivery_time_slot: e.target.value as
                      | '08:00-11:00'
                      | '11:00-14:00'
                      | '14:00-17:00'
                      | '17:00-20:00',
                  }))
                }
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                required
              >
                <option value=''>Selecciona horario</option>
                <option value='08:00-11:00'>8:00 am - 11:00 am</option>
                <option value='11:00-14:00'>11:00 am - 2:00 pm</option>
                <option value='14:00-17:00'>2:00 pm - 5:00 pm</option>
                <option value='17:00-20:00'>5:00 pm - 8:00 pm</option>
              </select>
            </div>
            <div className='space-y-2 md:col-span-2'>
              <Label htmlFor='delivery_mode'>Modalidad de Entrega</Label>
              <div className='flex gap-4'>
                <label className='flex items-center space-x-2'>
                  <input
                    type='radio'
                    name='delivery_mode'
                    value='home'
                    checked={formData.delivery_mode === 'home'}
                    onChange={e =>
                      setFormData(prev => ({
                        ...prev,
                        delivery_mode: e.target.value as
                          | 'home'
                          | 'pickup_point',
                      }))
                    }
                    className='text-blue-600'
                  />
                  <span>🚚 Entrega a domicilio</span>
                </label>
                <label className='flex items-center space-x-2'>
                  <input
                    type='radio'
                    name='delivery_mode'
                    value='pickup_point'
                    checked={formData.delivery_mode === 'pickup_point'}
                    onChange={e =>
                      setFormData(prev => ({
                        ...prev,
                        delivery_mode: e.target.value as
                          | 'home'
                          | 'pickup_point',
                      }))
                    }
                    className='text-blue-600'
                  />
                  <span>🏬 Recoger en tienda</span>
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Section */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              💳 Información de Pago
            </CardTitle>
            <CardDescription>Método de pago y facturación</CardDescription>
          </CardHeader>
          <CardContent className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='payment_method'>Método de Pago</Label>
              <select
                id='payment_method'
                value={formData.payment_method}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    payment_method: e.target.value as
                      | 'card'
                      | 'cash'
                      | 'digital_wallet'
                      | 'bank_transfer',
                  }))
                }
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                required
              >
                <option value='card'>💳 Tarjeta de crédito/débito</option>
                <option value='cash'>💰 Efectivo contra entrega</option>
                <option value='digital_wallet'>📱 Billetera digital</option>
                <option value='bank_transfer'>🏦 Transferencia bancaria</option>
              </select>
            </div>
            <div className='space-y-2'>
              <Label htmlFor='invoice_required'>¿Requieres Factura?</Label>
              <div className='flex gap-4'>
                <label className='flex items-center space-x-2'>
                  <input
                    type='radio'
                    name='invoice_required'
                    value='true'
                    checked={formData.invoice_required === true}
                    onChange={e =>
                      setFormData(prev => ({
                        ...prev,
                        invoice_required: e.target.value === 'true',
                      }))
                    }
                    className='text-blue-600'
                  />
                  <span>🧾 Sí</span>
                </label>
                <label className='flex items-center space-x-2'>
                  <input
                    type='radio'
                    name='invoice_required'
                    value='false'
                    checked={formData.invoice_required === false}
                    onChange={e =>
                      setFormData(prev => ({
                        ...prev,
                        invoice_required: e.target.value === 'true',
                      }))
                    }
                    className='text-blue-600'
                  />
                  <span>❌ No</span>
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Notes */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              📝 Notas Adicionales
            </CardTitle>
            <CardDescription>
              Instrucciones especiales y preferencias
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='special_instructions'>
                Instrucciones Especiales para el Repartidor
              </Label>
              <Textarea
                id='special_instructions'
                value={formData.special_instructions || ''}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    special_instructions: e.target.value,
                  }))
                }
                placeholder='ej., llamar antes de llegar, dejar en recepción, no tocar timbre'
                rows={3}
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='allow_substitutions'>
                Opción de Sustitución de Productos Agotados
              </Label>
              <div className='flex gap-4'>
                <label className='flex items-center space-x-2'>
                  <input
                    type='radio'
                    name='allow_substitutions'
                    value='true'
                    checked={formData.allow_substitutions === true}
                    onChange={e =>
                      setFormData(prev => ({
                        ...prev,
                        allow_substitutions: e.target.value === 'true',
                      }))
                    }
                    className='text-blue-600'
                  />
                  <span>✅ Sí, elegir reemplazo</span>
                </label>
                <label className='flex items-center space-x-2'>
                  <input
                    type='radio'
                    name='allow_substitutions'
                    value='false'
                    checked={formData.allow_substitutions === false}
                    onChange={e =>
                      setFormData(prev => ({
                        ...prev,
                        allow_substitutions: e.target.value === 'true',
                      }))
                    }
                    className='text-blue-600'
                  />
                  <span>❌ No, cancelar producto</span>
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Order Summary */}
        <Card className='bg-blue-50 border-blue-200'>
          <CardHeader>
            <CardTitle className='flex items-center gap-2 text-blue-800'>
              ✅ Resumen del Pedido
            </CardTitle>
            <CardDescription className='text-blue-700'>
              Revisa los detalles antes de confirmar
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <h4 className='font-medium text-blue-800'>Cliente</h4>
                <p className='text-sm text-blue-700'>
                  {formData.customer_name}
                </p>
                <p className='text-sm text-blue-700'>
                  {formData.customer_phone}
                </p>
                {formData.customer_email && (
                  <p className='text-sm text-blue-700'>
                    {formData.customer_email}
                  </p>
                )}
              </div>
              <div className='space-y-2'>
                <h4 className='font-medium text-blue-800'>Entrega</h4>
                <p className='text-sm text-blue-700'>
                  {formData.delivery_address.street}{' '}
                  {formData.delivery_address.number}
                </p>
                <p className='text-sm text-blue-700'>
                  {formData.delivery_address.colony},{' '}
                  {formData.delivery_address.city},{' '}
                  {formData.delivery_address.state}
                </p>
                <p className='text-sm text-blue-700'>
                  CP: {formData.delivery_address.zip}
                </p>
              </div>
            </div>

            <div className='border-t border-blue-300 pt-4'>
              <div className='flex justify-between items-center'>
                <span className='font-medium text-blue-800'>
                  Total del Pedido:
                </span>
                <span className='text-2xl font-bold text-blue-800'>
                  ${totalCost.toFixed(2)} MXN
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit Actions */}
        <div className='flex justify-end space-x-4 pt-6'>
          {onCancel && (
            <Button type='button' variant='outline' onClick={onCancel}>
              Cancelar
            </Button>
          )}
          <Button type='submit' disabled={loading} className='min-w-[120px]'>
            {loading ? (
              <div className='flex items-center gap-2'>
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
                Creando...
              </div>
            ) : (
              'Crear Pedido'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
