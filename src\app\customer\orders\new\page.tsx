'use client';

import { useState, useCallback, useMemo, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { OrderWizard } from '@/components/forms/order-wizard';
import { createClient } from '@/utils/supabase/client';
import { OrderFormData } from '@/types/order-form';

export default function NewOrderPage() {
  const { user, profile, loading } = useAuthStore();
  const router = useRouter();
  const [creating, setCreating] = useState(false);

  // Redirect to home page if user is not authenticated
  useEffect(() => {
    if (!loading && (!user || !profile)) {
      router.push('/');
    }
  }, [user, profile, loading, router]);

  // Memoize the Supabase client to prevent recreation on every render
  const supabase = useMemo(() => createClient(), []);

  const handleOrderSubmit = useCallback(
    async (orderData: OrderFormData) => {
      if (!user) return;

      setCreating(true);
      try {
        // Generate tracking number
        const trackingNumber = `MX${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

        // Create comprehensive order with enhanced logistics data
        const enhancedOrder = {
          customer_id: user.id,
          tracking_number: trackingNumber,
          customer_name: orderData.customer_name,
          customer_phone: orderData.customer_phone,
          customer_email: orderData.customer_email,
          customer_type: orderData.customer_type,
          pickup_address: orderData.pickup_address,
          products: orderData.products,
          delivery_mode: orderData.delivery_mode,
          stops: orderData.stops,
          delivery_date: orderData.delivery_date,
          delivery_time_slot: orderData.delivery_time_slot,
          pickup_time_slot: orderData.pickup_time_slot,
          payment_method: orderData.payment_method,
          invoice_required: orderData.invoice_required,
          fiscal_data: orderData.fiscal_data,
          special_instructions: orderData.special_instructions,
          allow_substitutions: orderData.allow_substitutions,
          communication_preferences: orderData.communication_preferences,
          vehicle_type_id: orderData.vehicle_type_id,
          cargo_type_id: orderData.cargo_type_id,
          route_optimization: orderData.route_optimization,
          delivery_region: orderData.delivery_region,
          regulatory_requirements: orderData.regulatory_requirements,
          total_cost: orderData.total_cost,
          estimated_delivery_time: orderData.estimated_delivery_time,
          terms_accepted: orderData.terms_accepted,
          status: 'pending',
          payment_status: 'pending',
          created_at: new Date().toISOString(),
        };

        // Create order in database
        const { data: order, error: orderError } = await supabase
          .from('orders')
          .insert(enhancedOrder)
          .select()
          .single();

        if (orderError) {
          throw new Error(orderError.message);
        }

        // Create initial tracking record
        await supabase.from('order_tracking').insert({
          order_id: order.id,
          status: 'created',
          timestamp: new Date().toISOString(),
          notes: 'Pedido creado exitosamente',
        });

        // Redirect to order tracking page
        router.push(`/customer/orders/${order.id}/tracking`);
      } catch (error) {
        console.error('Error creating order:', error);
        alert('Error al crear el pedido. Por favor intenta de nuevo.');
      } finally {
        setCreating(false);
      }
    },
    [user, supabase, router]
  );

  const handleCancel = useCallback(() => {
    router.push('/customer/dashboard');
  }, [router]);

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-black'></div>
      </div>
    );
  }

  // Show nothing while redirecting
  if (!user || !profile) {
    return null;
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='max-w-6xl mx-auto'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-gray-900 mb-2'>
            🚚 Crear Pedido de Logística
          </h1>
          <p className='text-gray-600'>
            Sistema completo de logística mexicana con seguimiento en tiempo
            real
          </p>
        </div>

        <OrderWizard
          onSubmit={handleOrderSubmit}
          onCancel={handleCancel}
          loading={creating}
        />
      </div>
    </div>
  );
}
