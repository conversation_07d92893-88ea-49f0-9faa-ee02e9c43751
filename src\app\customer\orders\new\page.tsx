'use client';

import { useState, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { OrderForm } from '@/components/forms/order-form';
import { createClient } from '@/utils/supabase/client';
import { OrderFormData } from '@/types/order-form';

export default function NewOrderPage() {
  const { user, profile, loading } = useAuthStore();
  const router = useRouter();
  const [creating, setCreating] = useState(false);

  // Memoize the Supabase client to prevent recreation on every render
  const supabase = useMemo(() => createClient(), []);

  const handleOrderSubmit = useCallback(
    async (orderData: OrderFormData) => {
      if (!user) return;

      setCreating(true);
      try {
        // Transform the new form data to match the existing database structure
        const transformedOrder = {
          customer_id: user.id,
          // Create pickup address from customer info
          pickup_address: {
            street_address:
              `${orderData.delivery_address.street} ${orderData.delivery_address.number || ''}`.trim(),
            city: orderData.delivery_address.city,
            state: orderData.delivery_address.state,
            postal_code: orderData.delivery_address.zip,
            contact_name: orderData.customer_name,
            contact_phone: orderData.customer_phone,
          },
          // Create delivery addresses array
          delivery_addresses: [
            {
              id: orderData.delivery_address.id,
              recipient_name: orderData.customer_name,
              phone: orderData.customer_phone,
              street_address:
                `${orderData.delivery_address.street} ${orderData.delivery_address.number || ''}`.trim(),
              city: orderData.delivery_address.city,
              state: orderData.delivery_address.state,
              postal_code: orderData.delivery_address.zip,
              notes: orderData.delivery_address.references,
            },
          ],
          // Create package details from products
          package_details: {
            description: orderData.products
              .map(p => `${p.quantity} ${p.unit_measure} de ${p.name}`)
              .join(', '),
            weight: orderData.products
              .reduce(
                (sum, p) => sum + (p.unit_measure === 'kg' ? p.quantity : 0),
                0
              )
              .toString(),
            dimensions: 'Variable',
            value: orderData.products
              .reduce((sum, p) => sum + p.subtotal, 0)
              .toString(),
            special_instructions: orderData.special_instructions,
          },
          // Add new fields to the order
          total_cost: orderData.products.reduce(
            (sum, p) => sum + p.subtotal,
            0
          ),
          status: 'pending',
          payment_status: 'pending',
          payment_method: orderData.payment_method,
          // Store additional form data as metadata
          metadata: {
            delivery_date: orderData.delivery_date,
            delivery_time_slot: orderData.delivery_time_slot,
            delivery_mode: orderData.delivery_mode,
            invoice_required: orderData.invoice_required,
            allow_substitutions: orderData.allow_substitutions,
            products: orderData.products,
          },
        };

        // Create order in database
        const { error } = await supabase
          .from('orders')
          .insert(transformedOrder)
          .select()
          .single();

        if (error) {
          alert('Failed to create order. Please try again.');
          return;
        }

        // Redirect to orders page
        router.push('/customer/orders');
      } catch {
        alert('Failed to create order. Please try again.');
      } finally {
        setCreating(false);
      }
    },
    [user, supabase, router]
  );

  const handleCancel = useCallback(() => {
    router.push('/customer/dashboard');
  }, [router]);

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-black'></div>
      </div>
    );
  }

  if (!user || !profile) {
    router.push('/');
    return null;
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      <OrderForm
        onSubmit={handleOrderSubmit}
        onCancel={handleCancel}
        loading={creating}
      />
    </div>
  );
}
