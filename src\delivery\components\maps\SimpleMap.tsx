'use client';

import { useEffect, useRef } from 'react';

interface SimpleMapProps {
  pickupAddress?: {
    lat?: number;
    lng?: number;
    name?: string;
  };
  deliveryAddress?: {
    lat?: number;
    lng?: number;
    name?: string;
  };
  className?: string;
}

export default function SimpleMap({
  pickupAddress,
  deliveryAddress,
  className = 'h-64',
}: SimpleMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<unknown>(null);

  useEffect(() => {
    if (!mapRef.current) return;

    // Clean up existing map
    if (
      mapInstanceRef.current &&
      typeof mapInstanceRef.current === 'object' &&
      'remove' in mapInstanceRef.current
    ) {
      (mapInstanceRef.current as { remove: () => void }).remove();
      mapInstanceRef.current = null;
    }

    // Default coordinates (Monterrey, Mexico)
    const pickup = {
      lat: pickupAddress?.lat || 25.6866,
      lng: pickupAddress?.lng || -100.3161,
      name: pickupAddress?.name || 'Punto de Recogida',
    };

    const delivery = {
      lat: deliveryAddress?.lat || 25.6595,
      lng: deliveryAddress?.lng || -100.3624,
      name: deliveryAddress?.name || 'Punto de Entrega',
    };

    console.log('🗺️ SimpleMap rendering with:', { pickup, delivery });

    // Check if Leaflet is available
    if (typeof window !== 'undefined' && window.L) {
      try {
        // Create map
        const map = window.L.map(mapRef.current, {
          center: [
            (pickup.lat + delivery.lat) / 2,
            (pickup.lng + delivery.lng) / 2,
          ],
          zoom: 13,
        });

        // Add tile layer
        window.L.tileLayer(
          'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
          {
            attribution:
              '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          }
        ).addTo(map);

        // Add markers
        const pickupMarker = window.L.marker([pickup.lat, pickup.lng]).addTo(
          map
        );
        pickupMarker.bindPopup(`<b>📍 ${pickup.name}</b>`);

        const deliveryMarker = window.L.marker([
          delivery.lat,
          delivery.lng,
        ]).addTo(map);
        deliveryMarker.bindPopup(`<b>🎯 ${delivery.name}</b>`);

        // Add route line
        window.L.polyline(
          [
            [pickup.lat, pickup.lng],
            [delivery.lat, delivery.lng],
          ],
          { color: '#3B82F6', weight: 4, opacity: 0.8 }
        ).addTo(map);

        mapInstanceRef.current = map;

        console.log('✅ SimpleMap created successfully');
      } catch (error) {
        console.error('❌ Error creating SimpleMap:', error);
      }
    } else {
      console.warn('⚠️ Leaflet not available, showing fallback');
    }

    return () => {
      if (
        mapInstanceRef.current &&
        typeof mapInstanceRef.current === 'object' &&
        'remove' in mapInstanceRef.current
      ) {
        (mapInstanceRef.current as { remove: () => void }).remove();
        mapInstanceRef.current = null;
      }
    };
  }, [pickupAddress, deliveryAddress]);

  return (
    <div className={`w-full ${className}`}>
      <div
        ref={mapRef}
        className='w-full h-full rounded-lg border border-gray-200'
        style={{ minHeight: '256px' }}
      />
    </div>
  );
}
