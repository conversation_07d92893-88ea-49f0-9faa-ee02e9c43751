'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';

interface NavItem {
  href: string;
  label: string;
  icon: string;
  adminOnly?: boolean;
  customerOnly?: boolean;
}

const customerNavItems: NavItem[] = [
  { href: '/customer/dashboard', label: 'Home', icon: '🏠' },
  { href: '/customer/orders/new', label: 'New Order', icon: '➕' },
  { href: '/customer/orders', label: 'Orders', icon: '📦' },
  { href: '/customer/profile', label: 'Profile', icon: '👤' },
];

const adminNavItems: NavItem[] = [
  { href: '/admin/dashboard', label: 'Dashboard', icon: '📊' },
  { href: '/admin/orders', label: 'Orders', icon: '📋' },
  { href: '/admin/users', label: 'Users', icon: '👥' },
  { href: '/admin/analytics', label: 'Reports', icon: '📈' },
];

export function MobileNav() {
  const pathname = usePathname();
  const { isAdmin } = useAuthStore();

  // Don't show on auth pages and demo pages
  if (
    pathname === '/' ||
    pathname === '/login' ||
    pathname === '/register' ||
    pathname === '/reset-password' ||
    pathname.startsWith('/auth/') ||
    pathname === '/demo-walmart'
  ) {
    return null;
  }

  const navItems = isAdmin ? adminNavItems : customerNavItems;

  return (
    <div className='fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 md:hidden'>
      <nav className='flex items-center justify-around py-2 px-4'>
        {navItems.map(item => {
          const isActive =
            pathname === item.href ||
            (item.href !== '/customer/dashboard' &&
              item.href !== '/admin/dashboard' &&
              pathname.startsWith(item.href));

          return (
            <Link
              key={item.href}
              href={item.href as never}
              className={`
                flex flex-col items-center py-2 px-3 rounded-lg transition-colors
                ${
                  isActive
                    ? 'text-black bg-gray-100'
                    : 'text-gray-500 hover:text-gray-700'
                }
              `}
            >
              <span className='text-lg mb-1'>{item.icon}</span>
              <span className='text-xs font-medium'>{item.label}</span>
            </Link>
          );
        })}
      </nav>
    </div>
  );
}

interface FloatingActionButtonProps {
  href: string;
  icon: string;
  label: string;
  className?: string;
}

export function FloatingActionButton({
  href,
  icon,
  label,
  className = '',
}: FloatingActionButtonProps) {
  return (
    <Link
      href={href as never}
      className={`
        fixed bottom-20 right-4 z-50 
        bg-black text-white rounded-full 
        w-14 h-14 flex items-center justify-center
        shadow-lg hover:shadow-xl transition-all duration-200
        hover:scale-105 active:scale-95
        md:hidden
        ${className}
      `}
      title={label}
    >
      <span className='text-xl'>{icon}</span>
    </Link>
  );
}
