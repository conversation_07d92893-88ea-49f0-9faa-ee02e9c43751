'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { OrderWizard } from '@/components/forms/order-wizard';
import { OrderFlowValidator } from '@/lib/tests/order-flow-validation';
import { MockDataSeeder } from '@/lib/data/mock-data-seeder';
import { CostCalculationService } from '@/lib/services/cost-calculation';
import { MEXICAN_VEHICLE_TYPES } from '@/lib/data/mexican-vehicles';

export default function MexicanLogisticsDemoPage() {
  const [validationResults, setValidationResults] = useState<any>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [mockOrders, setMockOrders] = useState<any[]>([]);
  const [selectedScenario, setSelectedScenario] = useState<string>('');
  const [showOrderWizard, setShowOrderWizard] = useState(false);

  useEffect(() => {
    // Generate mock orders on component mount
    const orders = MockDataSeeder.generateMockOrders(5);
    setMockOrders(orders);
  }, []);

  const runValidationTests = async () => {
    setIsValidating(true);
    try {
      const results = await OrderFlowValidator.validateCompleteOrderFlow();
      setValidationResults(results);
    } catch (error) {
      console.error('Validation error:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const loadTestScenario = (scenarioName: string) => {
    setSelectedScenario(scenarioName);
    const scenarios = MockDataSeeder.getTestScenarios();
    console.log(`Loading scenario: ${scenarioName}`, scenarios[scenarioName as keyof typeof scenarios]);
  };

  const calculateScenarioCost = (scenario: any) => {
    try {
      const result = CostCalculationService.calculateCost({
        products: scenario.products,
        vehicleTypeId: scenario.expectedVehicle,
        deliveryMode: scenario.stops ? 'multi' : 'single',
        stops: scenario.stops || [],
        deliveryDate: new Date().toISOString().split('T')[0],
        deliveryTimeSlot: '08:00-11:00',
        deliveryRegion: 'local',
        specialHandlingRequired: scenario.products.some((p: any) => 
          Object.values(p.special_handling || {}).some(Boolean)
        )
      });
      return result;
    } catch (error) {
      console.error('Cost calculation error:', error);
      return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900">
          🚚 Mexican Logistics Order Flow Demo
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Comprehensive demonstration of the complete 9-step Mexican logistics order management system
          with enhanced features, vehicle selection, multi-stop delivery, and real-time cost calculation.
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Button
          onClick={() => setShowOrderWizard(!showOrderWizard)}
          className="h-20 text-lg"
          variant={showOrderWizard ? "default" : "outline"}
        >
          {showOrderWizard ? '📋 Hide Order Wizard' : '🛒 Try Order Wizard'}
        </Button>
        
        <Button
          onClick={runValidationTests}
          disabled={isValidating}
          className="h-20 text-lg"
          variant="outline"
        >
          {isValidating ? '⏳ Running Tests...' : '🧪 Run Validation Tests'}
        </Button>
        
        <Button
          onClick={() => setMockOrders(MockDataSeeder.generateMockOrders(5))}
          className="h-20 text-lg"
          variant="outline"
        >
          🔄 Generate New Mock Data
        </Button>
      </div>

      {/* Order Wizard */}
      {showOrderWizard && (
        <Card>
          <CardHeader>
            <CardTitle>Complete Order Wizard (9 Steps)</CardTitle>
          </CardHeader>
          <CardContent>
            <OrderWizard />
          </CardContent>
        </Card>
      )}

      {/* Test Scenarios */}
      <Card>
        <CardHeader>
          <CardTitle>Test Scenarios</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(MockDataSeeder.getTestScenarios()).map(([key, scenario]) => {
              const costResult = calculateScenarioCost(scenario);
              return (
                <div
                  key={key}
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    selectedScenario === key ? 'border-blue-500 bg-blue-50' : 'hover:border-gray-300'
                  }`}
                  onClick={() => loadTestScenario(key)}
                >
                  <h4 className="font-semibold mb-2 capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div>Products: {scenario.products.length}</div>
                    <div>Expected Vehicle: {scenario.expectedVehicle}</div>
                    {scenario.stops && <div>Stops: {scenario.stops.length}</div>}
                    {costResult && (
                      <div className="font-medium text-green-600">
                        Est. Cost: ${costResult.total.toFixed(2)} MXN
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Catalog */}
      <Card>
        <CardHeader>
          <CardTitle>Mexican Vehicle Catalog ({MEXICAN_VEHICLE_TYPES.length} Types)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {MEXICAN_VEHICLE_TYPES.map((vehicle) => (
              <div key={vehicle.id} className="border rounded-lg p-3">
                <div className="aspect-video bg-gray-100 rounded mb-2 overflow-hidden">
                  <img
                    src={vehicle.image_path}
                    alt={vehicle.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <h4 className="font-semibold text-sm">{vehicle.name}</h4>
                <p className="text-xs text-gray-600 mb-2">{vehicle.subcategory}</p>
                <div className="space-y-1 text-xs">
                  <div>Max: {vehicle.max_weight_kg}kg / {vehicle.max_volume_m3}m³</div>
                  <div>Rate: ${vehicle.base_rate_per_km}/km</div>
                  <div>Efficiency: {vehicle.fuel_efficiency_km_per_liter}km/L</div>
                </div>
                <Badge variant="outline" className="mt-2 text-xs">
                  {vehicle.category}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Mock Orders */}
      <Card>
        <CardHeader>
          <CardTitle>Sample Orders ({mockOrders.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockOrders.map((order, index) => (
              <div key={order.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="font-semibold">{order.customer_name}</h4>
                    <p className="text-sm text-gray-600">{order.customer_phone}</p>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-green-600">${order.total_cost.toFixed(2)}</div>
                    <Badge variant={
                      order.status === 'delivered' ? 'default' :
                      order.status === 'in_transit' ? 'secondary' :
                      order.status === 'cancelled' ? 'destructive' : 'outline'
                    }>
                      {order.status}
                    </Badge>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Address:</strong> {order.delivery_address.street} {order.delivery_address.number}, {order.delivery_address.colony}
                  </div>
                  <div>
                    <strong>Products:</strong> {order.products.length} items
                  </div>
                  <div>
                    <strong>Delivery:</strong> {order.delivery_date} ({order.delivery_time_slot})
                  </div>
                  <div>
                    <strong>Payment:</strong> {order.payment_method}
                  </div>
                </div>
                
                {order.stops && order.stops.length > 0 && (
                  <div className="mt-3 pt-3 border-t">
                    <strong className="text-sm">Multi-Stop Route:</strong> {order.stops.length} stops
                  </div>
                )}
                
                {order.special_instructions && (
                  <div className="mt-3 pt-3 border-t">
                    <strong className="text-sm">Instructions:</strong>
                    <p className="text-sm text-gray-600 mt-1">{order.special_instructions}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Validation Results */}
      {validationResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🧪 Validation Test Results
              <Badge variant={validationResults.overallPassed ? "default" : "destructive"}>
                {validationResults.overallPassed ? 'PASSED' : 'FAILED'}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{validationResults.summary.totalTests}</div>
                <div className="text-sm text-gray-600">Total Tests</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{validationResults.summary.passed}</div>
                <div className="text-sm text-gray-600">Passed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{validationResults.summary.failed}</div>
                <div className="text-sm text-gray-600">Failed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{validationResults.summary.warnings}</div>
                <div className="text-sm text-gray-600">Warnings</div>
              </div>
            </div>

            <div className="space-y-4">
              {validationResults.results.map((result: any, index: number) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold">{result.step}</h4>
                    <Badge variant={result.passed ? "default" : "destructive"}>
                      {result.passed ? 'PASSED' : 'FAILED'}
                    </Badge>
                  </div>
                  
                  {result.errors.length > 0 && (
                    <div className="mb-2">
                      <strong className="text-red-600">Errors:</strong>
                      <ul className="list-disc list-inside text-sm text-red-600 mt-1">
                        {result.errors.map((error: string, i: number) => (
                          <li key={i}>{error}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {result.warnings.length > 0 && (
                    <div>
                      <strong className="text-yellow-600">Warnings:</strong>
                      <ul className="list-disc list-inside text-sm text-yellow-600 mt-1">
                        {result.warnings.map((warning: string, i: number) => (
                          <li key={i}>{warning}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Implementation Status */}
      <Card>
        <CardHeader>
          <CardTitle>Implementation Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3 text-green-600">✅ Completed Features</h4>
              <ul className="space-y-2 text-sm">
                <li>• Enhanced product management with weight, dimensions, special handling</li>
                <li>• Complete Mexican vehicle catalog (12 types)</li>
                <li>• Automated vehicle selection algorithm</li>
                <li>• Multi-stop delivery management with drag-and-drop</li>
                <li>• Comprehensive Zod validation schemas</li>
                <li>• Real-time cost calculation engine</li>
                <li>• Address autocomplete service</li>
                <li>• Mock data generation and testing</li>
                <li>• 9-step wizard with seamless navigation</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-3 text-blue-600">🔧 Technical Features</h4>
              <ul className="space-y-2 text-sm">
                <li>• TypeScript with strict type safety</li>
                <li>• Responsive design for mobile/desktop</li>
                <li>• Real-time form validation</li>
                <li>• Geolocation integration</li>
                <li>• Route optimization algorithms</li>
                <li>• Cost breakdown with tax calculation</li>
                <li>• Special handling requirements</li>
                <li>• Mexican postal code validation</li>
                <li>• Comprehensive test suite</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
