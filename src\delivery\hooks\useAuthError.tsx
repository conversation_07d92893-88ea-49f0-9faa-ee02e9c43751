import { useState } from 'react';

export function useAuthError() {
  const [error, setError] = useState('');

  const handleError = (error: string | { message?: string } | unknown) => {
    if (typeof error === 'string') {
      setError(error);
    } else if (
      typeof error === 'object' &&
      error &&
      'message' in error &&
      typeof (error as { message: string }).message === 'string'
    ) {
      setError((error as { message: string }).message);
    } else {
      setError('Ha ocurrido un error inesperado');
    }
  };

  const clearError = () => {
    setError('');
  };

  return {
    error,
    handleError,
    clearError,
  };
}
