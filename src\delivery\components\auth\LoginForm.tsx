'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuthStore } from '@/stores/authStore';

export default function LoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { signIn } = useAuthStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // SECURITY: Only allow delivery users to login to delivery system
    const { error } = await signIn(email, password, ['delivery']);

    if (error) {
      setError(error.message || 'Error al iniciar sesión');
    }

    setLoading(false);
  };

  return (
    <div className='w-full'>
      <div className='text-center mb-4 sm:mb-6'>
        <h2 className='text-xl sm:text-2xl font-bold text-white mb-2'>
          Bienvenido de vuelta
        </h2>
        <p className='text-gray-200 text-sm sm:text-base'>
          Accede a tu cuenta para gestionar entregas
        </p>
      </div>

      <form className='space-y-4 sm:space-y-6' onSubmit={handleSubmit}>
        <div className='space-y-3 sm:space-y-4'>
          <div>
            <label
              htmlFor='email'
              className='block text-sm font-medium text-white mb-2'
            >
              Correo electrónico
            </label>
            <input
              id='email'
              name='email'
              type='email'
              autoComplete='email'
              required
              className='w-full px-3 sm:px-4 py-2 sm:py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all text-sm sm:text-base'
              placeholder='Ingresa tu correo electrónico'
              value={email}
              onChange={e => setEmail(e.target.value)}
            />
          </div>

          <div>
            <label
              htmlFor='password'
              className='block text-sm font-medium text-white mb-2'
            >
              Contraseña
            </label>
            <input
              id='password'
              name='password'
              type='password'
              autoComplete='current-password'
              required
              className='w-full px-3 sm:px-4 py-2 sm:py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all text-sm sm:text-base'
              placeholder='Ingresa tu contraseña'
              value={password}
              onChange={e => setPassword(e.target.value)}
            />
          </div>
        </div>

        {error && (
          <div className='rounded-lg bg-red-500/20 border border-red-400/30 p-4'>
            <div className='text-sm text-red-200'>{error}</div>
          </div>
        )}

        <div>
          <button
            type='submit'
            disabled={loading}
            className='w-full py-3 px-4 bg-gradient-to-r from-blue-500 to-green-500 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-green-600 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-105'
          >
            {loading ? 'Iniciando sesión...' : 'Iniciar Sesión'}
          </button>
        </div>
      </form>

      <div className='mt-4 sm:mt-6 text-center pt-4 border-t border-white/20'>
        <p className='text-sm text-gray-300 mb-2'>¿Eres cliente?</p>
        <Link
          href='/auth/login'
          className='inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 rounded-lg transition-all duration-200 hover:shadow-lg'
        >
          Acceder como cliente
        </Link>
      </div>
    </div>
  );
}
