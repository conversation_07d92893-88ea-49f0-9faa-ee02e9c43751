import { NextResponse, type NextRequest } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/lib/supabase';

/**
 * Simplified Next.js middleware for authentication and authorization
 * Following Next.js 15 best practices and Supabase SSR guidelines
 *
 * Key features:
 * - Server-side authentication using JWT validation
 * - Role-based access control
 * - Clean redirects for better UX and SEO
 * - Optimized performance with minimal database queries
 */

type UserRole = 'admin' | 'customer' | 'delivery';

interface AuthUser {
  sub: string;
  role?: UserRole;
  app_metadata?: {
    role?: UserRole;
  };
}

/**
 * Route classification helpers
 */
function isPublicRoute(pathname: string): boolean {
  const publicPaths = [
    '/',
    '/auth/sign-up',
    '/auth/callback',
    '/auth/confirm',
    '/auth/error',
    '/auth/auth-code-error',
    '/auth/sign-up-success',
  ];
  return (
    publicPaths.includes(pathname) ||
    pathname.startsWith('/auth/forgot-password')
  );
}

function isAuthRoute(pathname: string): boolean {
  return (
    pathname.startsWith('/auth/login') || pathname.startsWith('/auth/sign-up')
  );
}

function isProtectedRoute(pathname: string): boolean {
  const protectedPrefixes = ['/admin', '/customer', '/delivery', '/dashboard'];
  return protectedPrefixes.some(prefix => pathname.startsWith(prefix));
}

/**
 * Get user role from JWT claims
 */
function getUserRole(user: AuthUser): UserRole | null {
  return user.role || user.app_metadata?.role || null;
}

/**
 * Get appropriate dashboard URL based on user role
 */
function getDashboardUrl(role: UserRole): string {
  switch (role) {
    case 'admin':
      return '/admin/dashboard';
    case 'customer':
      return '/customer/dashboard';
    case 'delivery':
      return '/delivery';
    default:
      return '/dashboard';
  }
}

/**
 * Create Supabase server client with proper cookie handling
 */
function createSupabaseClient(request: NextRequest) {
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          // Note: Cannot set cookies in middleware, but this is required by the client
          cookiesToSet.forEach(({ name, value }) => {
            request.cookies.set(name, value);
          });
        },
      },
    }
  );
}

/**
 * Main middleware function for authentication and authorization
 */
export async function updateSession(request: NextRequest) {
  try {
    const pathname = request.nextUrl.pathname;

    // Skip middleware for static assets and API routes (handle separately if needed)
    if (pathname.startsWith('/_next') || pathname.startsWith('/api')) {
      return NextResponse.next();
    }

    // Create Supabase client
    const supabase = createSupabaseClient(request);

    // Get user session using JWT validation (recommended by Supabase)
    const { data: claims } = await supabase.auth.getClaims();
    const user = claims?.claims as AuthUser | null;
    let userRole = user ? getUserRole(user) : null;

    // If role is not in JWT claims, fallback to database query
    if (user && !userRole) {
      try {
        const { data: profile } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.sub)
          .single();

        if (profile?.role) {
          userRole = profile.role as UserRole;
        }
      } catch (error) {
        console.error('Error fetching user role from database:', error);
      }
    }

    // Debug logging for development (can be removed in production)
    if (process.env.NODE_ENV === 'development' && user) {
      console.log('Middleware Debug:', {
        pathname,
        userId: user.sub,
        userRole,
        roleFromJWT: getUserRole(user),
        hasRole: !!userRole,
      });
    }

    // 1. Handle unauthenticated users trying to access protected routes
    if (isProtectedRoute(pathname) && !user) {
      const loginUrl = new URL('/auth/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }

    // 2. Handle authenticated users on auth pages (redirect to dashboard)
    if (user && isAuthRoute(pathname)) {
      if (userRole) {
        const dashboardUrl = getDashboardUrl(userRole);
        return NextResponse.redirect(new URL(dashboardUrl, request.url));
      }
      // Fallback to generic dashboard if no role
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }

    // 3. Handle role-based access control for protected routes
    if (user && isProtectedRoute(pathname)) {
      // If user has no role, redirect to generic dashboard
      if (!userRole) {
        return NextResponse.redirect(new URL('/dashboard', request.url));
      }

      // Special handling for /dashboard route - redirect users with specific roles to their dashboards
      if (pathname === '/dashboard' && userRole) {
        const dashboardUrl = getDashboardUrl(userRole);
        return NextResponse.redirect(new URL(dashboardUrl, request.url));
      }

      const hasValidAccess =
        (pathname.startsWith('/admin') && userRole === 'admin') ||
        (pathname.startsWith('/customer') && userRole === 'customer') ||
        (pathname.startsWith('/delivery') && userRole === 'delivery') ||
        pathname.startsWith('/dashboard'); // Generic dashboard is accessible to all authenticated users

      if (!hasValidAccess) {
        // Redirect to appropriate dashboard for user's role
        const dashboardUrl = getDashboardUrl(userRole);
        return NextResponse.redirect(new URL(dashboardUrl, request.url));
      }
    }

    // 4. Handle root path redirects for authenticated users
    if (user && pathname === '/') {
      if (userRole) {
        const dashboardUrl = getDashboardUrl(userRole);
        return NextResponse.redirect(new URL(dashboardUrl, request.url));
      } else {
        // If no role, redirect to generic dashboard
        return NextResponse.redirect(new URL('/dashboard', request.url));
      }
    }

    // 5. Allow access to public routes
    if (isPublicRoute(pathname)) {
      return NextResponse.next();
    }

    // 6. Default: allow the request to continue
    return NextResponse.next();
  } catch (error) {
    console.error('Middleware error:', error);
    // On error, allow the request to continue to avoid breaking the app
    return NextResponse.next();
  }
}
