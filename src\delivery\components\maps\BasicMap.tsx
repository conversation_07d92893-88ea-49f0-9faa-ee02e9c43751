'use client';

import { useEffect, useRef } from 'react';

interface BasicMapProps {
  className?: string;
}

export default function BasicMap({ className = 'h-64' }: BasicMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);

  useEffect(() => {
    if (!mapRef.current || typeof window === 'undefined') return;

    // Wait for Leaflet to be available
    const initMap = () => {
      if (!window.L) {
        setTimeout(initMap, 100);
        return;
      }

      // Clean up existing map instance first
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }

      // Clear the container completely
      if (mapRef.current) {
        mapRef.current.innerHTML = '';
      }

      try {
        // Create map with Monterrey coordinates
        const map = window.L.map(mapRef.current!, {
          center: [25.6866, -100.3161],
          zoom: 12,
        });

        // Add tile layer
        window.L.tileLayer(
          'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
          {
            attribution:
              '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          }
        ).addTo(map);

        // Add a simple marker
        window.L.marker([25.6866, -100.3161])
          .addTo(map)
          .bindPopup('<b>📍 Punto de Entrega</b>')
          .openPopup();

        // Store map instance for cleanup
        mapInstanceRef.current = map;

        console.log('✅ BasicMap created successfully');
      } catch (error) {
        console.error('❌ Error creating BasicMap:', error);
      }
    };

    initMap();

    // Cleanup function - this is the key part
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, []); // Empty dependency array ensures this runs once on mount

  return (
    <div className={`w-full ${className}`}>
      <div
        ref={mapRef}
        className='w-full h-full rounded-lg border border-gray-200 bg-gray-100'
        style={{ minHeight: '256px' }}
      >
        <div className='flex items-center justify-center h-full'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2'></div>
            <p className='text-sm text-gray-600'>Cargando mapa básico...</p>
          </div>
        </div>
      </div>
    </div>
  );
}
