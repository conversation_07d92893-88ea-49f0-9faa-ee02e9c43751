'use client';

import { useState, useEffect } from 'react';
import { useAuthStore } from '@/stores/authStore';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Shield,
  AlertTriangle,
  Activity,
  Users,
  Lock,
  Eye,
  Clock,
  Globe,
  Zap,
} from 'lucide-react';

interface SecurityMetrics {
  totalEvents: number;
  criticalEvents: number;
  highSeverityEvents: number;
  authEvents: number;
  securityEvents: number;
  failedLogins: number;
  rateLimitExceeded: number;
  suspiciousActivity: number;
  uniqueIPs: number;
  uniqueUsers: number;
  eventsByCategory: {
    AUTH: number;
    ACCESS: number;
    DATA: number;
    SYSTEM: number;
    SECURITY: number;
  };
  eventsBySeverity: {
    LOW: number;
    MEDIUM: number;
    HIGH: number;
    CRITICAL: number;
  };
}

interface AuditLogEntry {
  id: string;
  event: string;
  userId?: string;
  ip?: string;
  userAgent?: string;
  path?: string;
  method?: string;
  timestamp: Date;
  details?: Record<string, unknown>;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  category: 'AUTH' | 'ACCESS' | 'DATA' | 'SYSTEM' | 'SECURITY';
}

export default function SecurityDashboard() {
  const { user, isAdmin } = useAuthStore();
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null);
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState<'hour' | 'day' | 'week' | 'month'>(
    'day'
  );

  useEffect(() => {
    if (user && isAdmin) {
      fetchSecurityData();
    }
  }, [user, isAdmin, timeframe]);

  const fetchSecurityData = async () => {
    setLoading(true);
    try {
      // In a real implementation, these would be API calls
      // For now, we'll simulate the data
      const mockMetrics: SecurityMetrics = {
        totalEvents: 1247,
        criticalEvents: 3,
        highSeverityEvents: 28,
        authEvents: 456,
        securityEvents: 89,
        failedLogins: 12,
        rateLimitExceeded: 5,
        suspiciousActivity: 7,
        uniqueIPs: 234,
        uniqueUsers: 89,
        eventsByCategory: {
          AUTH: 456,
          ACCESS: 234,
          DATA: 345,
          SYSTEM: 123,
          SECURITY: 89,
        },
        eventsBySeverity: {
          LOW: 890,
          MEDIUM: 234,
          HIGH: 120,
          CRITICAL: 3,
        },
      };

      const mockAuditLogs: AuditLogEntry[] = [
        {
          id: '1',
          event: 'UNAUTHORIZED_ORDER_CLOSURE_ATTEMPT',
          userId: 'user-123',
          ip: '*************',
          path: '/api/orders/update-status',
          timestamp: new Date(Date.now() - 1000 * 60 * 5),
          severity: 'HIGH',
          category: 'SECURITY',
          details: { orderId: 'order-456', userRole: 'delivery' },
        },
        {
          id: '2',
          event: 'RATE_LIMIT_EXCEEDED',
          ip: '*********',
          path: '/api/orders',
          timestamp: new Date(Date.now() - 1000 * 60 * 15),
          severity: 'HIGH',
          category: 'ACCESS',
          details: { limit: 60, remaining: 0 },
        },
        {
          id: '3',
          event: 'LOGIN_SUCCESS',
          userId: 'user-789',
          ip: '***********',
          timestamp: new Date(Date.now() - 1000 * 60 * 30),
          severity: 'MEDIUM',
          category: 'AUTH',
        },
      ];

      setMetrics(mockMetrics);
      setAuditLogs(mockAuditLogs);
    } catch (error) {
      console.error('Failed to fetch security data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'HIGH':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'LOW':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'AUTH':
        return <Lock className='w-4 h-4' />;
      case 'ACCESS':
        return <Shield className='w-4 h-4' />;
      case 'DATA':
        return <Activity className='w-4 h-4' />;
      case 'SYSTEM':
        return <Zap className='w-4 h-4' />;
      case 'SECURITY':
        return <AlertTriangle className='w-4 h-4' />;
      default:
        return <Eye className='w-4 h-4' />;
    }
  };

  if (!user || !isAdmin) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <Card className='w-96'>
          <CardContent className='p-6 text-center'>
            <Shield className='w-12 h-12 mx-auto mb-4 text-gray-400' />
            <h2 className='text-xl font-semibold mb-2'>Access Denied</h2>
            <p className='text-gray-600'>
              You need admin privileges to access the security dashboard.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50 p-6'>
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <div className='mb-8'>
          <div className='flex items-center gap-3 mb-2'>
            <Shield className='w-8 h-8 text-blue-600' />
            <h1 className='text-3xl font-bold text-gray-900'>
              Security Dashboard
            </h1>
          </div>
          <p className='text-gray-600'>
            Zero Trust security monitoring and audit logs
          </p>
        </div>

        {/* Timeframe Selector */}
        <div className='mb-6'>
          <div className='flex gap-2'>
            {(['hour', 'day', 'week', 'month'] as const).map(period => (
              <Button
                key={period}
                variant={timeframe === period ? 'default' : 'outline'}
                size='sm'
                onClick={() => setTimeframe(period)}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </Button>
            ))}
          </div>
        </div>

        {loading ? (
          <div className='flex items-center justify-center py-12'>
            <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600'></div>
          </div>
        ) : (
          <Tabs defaultValue='overview' className='space-y-6'>
            <TabsList className='grid w-full grid-cols-4'>
              <TabsTrigger value='overview'>Overview</TabsTrigger>
              <TabsTrigger value='events'>Security Events</TabsTrigger>
              <TabsTrigger value='audit'>Audit Logs</TabsTrigger>
              <TabsTrigger value='settings'>Settings</TabsTrigger>
            </TabsList>

            <TabsContent value='overview' className='space-y-6'>
              {/* Key Metrics */}
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
                <Card>
                  <CardContent className='p-6'>
                    <div className='flex items-center justify-between'>
                      <div>
                        <p className='text-sm font-medium text-gray-600'>
                          Total Events
                        </p>
                        <p className='text-2xl font-bold'>
                          {metrics?.totalEvents.toLocaleString()}
                        </p>
                      </div>
                      <Activity className='w-8 h-8 text-blue-600' />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className='p-6'>
                    <div className='flex items-center justify-between'>
                      <div>
                        <p className='text-sm font-medium text-gray-600'>
                          Critical Events
                        </p>
                        <p className='text-2xl font-bold text-red-600'>
                          {metrics?.criticalEvents}
                        </p>
                      </div>
                      <AlertTriangle className='w-8 h-8 text-red-600' />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className='p-6'>
                    <div className='flex items-center justify-between'>
                      <div>
                        <p className='text-sm font-medium text-gray-600'>
                          Unique Users
                        </p>
                        <p className='text-2xl font-bold'>
                          {metrics?.uniqueUsers}
                        </p>
                      </div>
                      <Users className='w-8 h-8 text-green-600' />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className='p-6'>
                    <div className='flex items-center justify-between'>
                      <div>
                        <p className='text-sm font-medium text-gray-600'>
                          Unique IPs
                        </p>
                        <p className='text-2xl font-bold'>
                          {metrics?.uniqueIPs}
                        </p>
                      </div>
                      <Globe className='w-8 h-8 text-purple-600' />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Security Alerts */}
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <AlertTriangle className='w-5 h-5' />
                    Security Alerts
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                    <div className='p-4 bg-red-50 border border-red-200 rounded-lg'>
                      <div className='flex items-center justify-between mb-2'>
                        <span className='text-sm font-medium text-red-800'>
                          Failed Logins
                        </span>
                        <Badge className='bg-red-100 text-red-800'>
                          {metrics?.failedLogins}
                        </Badge>
                      </div>
                      <p className='text-xs text-red-600'>
                        Potential brute force attempts
                      </p>
                    </div>

                    <div className='p-4 bg-orange-50 border border-orange-200 rounded-lg'>
                      <div className='flex items-center justify-between mb-2'>
                        <span className='text-sm font-medium text-orange-800'>
                          Rate Limits
                        </span>
                        <Badge className='bg-orange-100 text-orange-800'>
                          {metrics?.rateLimitExceeded}
                        </Badge>
                      </div>
                      <p className='text-xs text-orange-600'>
                        API abuse attempts blocked
                      </p>
                    </div>

                    <div className='p-4 bg-yellow-50 border border-yellow-200 rounded-lg'>
                      <div className='flex items-center justify-between mb-2'>
                        <span className='text-sm font-medium text-yellow-800'>
                          Suspicious Activity
                        </span>
                        <Badge className='bg-yellow-100 text-yellow-800'>
                          {metrics?.suspiciousActivity}
                        </Badge>
                      </div>
                      <p className='text-xs text-yellow-600'>
                        Anomalous behavior detected
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='audit' className='space-y-6'>
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <Eye className='w-5 h-5' />
                    Recent Audit Logs
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='space-y-4'>
                    {auditLogs.map(log => (
                      <div
                        key={log.id}
                        className='flex items-start gap-4 p-4 border rounded-lg'
                      >
                        <div className='flex-shrink-0'>
                          {getCategoryIcon(log.category)}
                        </div>
                        <div className='flex-1 min-w-0'>
                          <div className='flex items-center gap-2 mb-1'>
                            <span className='font-medium'>{log.event}</span>
                            <Badge className={getSeverityColor(log.severity)}>
                              {log.severity}
                            </Badge>
                          </div>
                          <div className='text-sm text-gray-600 space-y-1'>
                            <div className='flex items-center gap-4'>
                              <span>IP: {log.ip || 'N/A'}</span>
                              <span>User: {log.userId || 'Anonymous'}</span>
                              <span className='flex items-center gap-1'>
                                <Clock className='w-3 h-3' />
                                {log.timestamp.toLocaleString()}
                              </span>
                            </div>
                            {log.path && <div>Path: {log.path}</div>}
                            {log.details && (
                              <div className='text-xs bg-gray-50 p-2 rounded mt-2'>
                                <pre>
                                  {JSON.stringify(log.details, null, 2)}
                                </pre>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='settings' className='space-y-6'>
              <Card>
                <CardHeader>
                  <CardTitle>Zero Trust Configuration</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='space-y-4'>
                    <div className='flex items-center justify-between p-4 border rounded-lg'>
                      <div>
                        <h3 className='font-medium'>
                          Multi-Factor Authentication
                        </h3>
                        <p className='text-sm text-gray-600'>
                          Require MFA for privileged operations
                        </p>
                      </div>
                      <Badge className='bg-green-100 text-green-800'>
                        Enabled
                      </Badge>
                    </div>

                    <div className='flex items-center justify-between p-4 border rounded-lg'>
                      <div>
                        <h3 className='font-medium'>Rate Limiting</h3>
                        <p className='text-sm text-gray-600'>
                          API request rate limiting active
                        </p>
                      </div>
                      <Badge className='bg-green-100 text-green-800'>
                        Active
                      </Badge>
                    </div>

                    <div className='flex items-center justify-between p-4 border rounded-lg'>
                      <div>
                        <h3 className='font-medium'>Security Headers</h3>
                        <p className='text-sm text-gray-600'>
                          Comprehensive security headers applied
                        </p>
                      </div>
                      <Badge className='bg-green-100 text-green-800'>
                        Applied
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </div>
  );
}
