'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';

export default function DashboardPage() {
  const {
    user,
    profile,
    loading,
    isAdmin,
    isCustomer,
    isDelivery,
    hasValidMainAppRole,
    shouldRedirectToDelivery,
    dbError,
  } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    // Don't redirect while loading
    if (loading) {
      return;
    }

    // If no user, redirect to login
    if (!user) {
      router.push('/');
      return;
    }

    // If there's a database error, don't redirect
    if (dbError) {
      return;
    }

    // Only redirect if we have profile data
    if (profile) {
      // If user is delivery, redirect to delivery app
      if (shouldRedirectToDelivery) {
        router.push('/delivery');
        return;
      }

      // If user has valid main app role, redirect accordingly
      if (hasValidMainAppRole) {
        if (isAdmin) {
          router.push('/admin/dashboard');
          return;
        }
        if (isCustomer) {
          router.push('/customer/dashboard');
          return;
        }
      }

      // If user has no valid role, redirect to home
      if (!hasValidMainAppRole && !shouldRedirectToDelivery) {
        router.push('/');
        return;
      }
    }
  }, [
    user,
    profile,
    loading,
    isAdmin,
    isCustomer,
    isDelivery,
    hasValidMainAppRole,
    shouldRedirectToDelivery,
    dbError,
    router,
  ]);

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-black'></div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  // Show database setup screen if there's a database error
  if (dbError && user) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center p-4'>
        <div className='w-full max-w-2xl'>
          <div className='text-center mb-8'>
            <h1 className='text-2xl font-bold text-gray-900 mb-2'>
              Welcome to Mouvers
            </h1>
            <p className='text-gray-600'>
              Complete the database setup to get started
            </p>
          </div>

          <div className='mt-8 text-center'>
            <p className='text-sm text-gray-500'>
              Logged in as: <strong>{user.email}</strong>
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!profile) {
    return null; // Will redirect or show setup
  }

  // Fallback UI while redirecting
  return (
    <div className='min-h-screen flex items-center justify-center'>
      <div className='text-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-4'></div>
        <p className='text-gray-600'>Redirecting to your dashboard...</p>
      </div>
    </div>
  );
}
