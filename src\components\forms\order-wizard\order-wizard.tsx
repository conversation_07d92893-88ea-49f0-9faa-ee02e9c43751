'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { OrderFormData } from '@/types/order-form';
import { OrderFormDataSchema } from '@/lib/validation/order-schemas';
import { OrderWizardStep1 } from './step1-customer-info';
import { OrderWizardStep2 } from './step2-delivery-address';
import { OrderWizardStep3 } from './step3-products';
import { OrderWizardStep4 } from './step4-delivery-options';
import { OrderWizardStep5 } from './step5-multi-stop';
import { OrderWizardStep6 } from './step6-payment';
import { OrderWizardStep7 } from './step7-additional-notes';
import { OrderWizardStep8 } from './step8-mexican-logistics';
import { OrderWizardStep9 } from './step9-review';
import { z } from 'zod';

interface OrderWizardProps {
  onSubmit: (data: OrderFormData) => void;
  onCancel: () => void;
  loading?: boolean;
  initialData?: OrderFormData;
}

export function OrderWizard({
  onSubmit,
  onCancel,
  loading = false,
  initialData,
}: OrderWizardProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<OrderFormData>(
    initialData || {
      // Customer Information
      customer_name: '',
      customer_phone: '',
      customer_email: '',
      customer_type: 'individual',

      // Delivery Address
      delivery_address: {
        id: '1',
        street: '',
        number: '',
        colony: '',
        city: '',
        state: 'Ciudad de México',
        zip: '',
        references: '',
      },

      // Pickup Address
      pickup_address: {
        id: '2',
        street: '',
        number: '',
        colony: '',
        city: '',
        state: 'Ciudad de México',
        zip: '',
        references: '',
      },

      // Shopping Cart
      products: [
        {
          id: '1',
          name: '',
          quantity: 1,
          unit_measure: 'kg',
          unit_price: 0,
          subtotal: 0,
          weight: 1,
          weight_unit: 'kg',
          dimensions: {
            length: 10,
            width: 10,
            height: 10,
            unit: 'cm',
          },
          special_handling: {
            fragile: false,
            perishable: false,
            valuable: false,
            hazardous: false,
            refrigerated: false,
            oversized: false,
          },
          notes: '',
        },
      ],

      // Delivery Options
      delivery_date: '',
      delivery_time_slot: '08:00-11:00',
      delivery_mode: 'home',

      // Payment
      payment_method: 'card',
      invoice_required: false,

      // Additional Notes
      special_instructions: '',
      allow_substitutions: false,

      // Mexican Logistics (new fields)
      vehicle_type_id: '',
      cargo_type_id: '',
      scheduled_pickup_time: '',
      scheduled_delivery_time: '',
      delivery_instructions: '',
      special_handling_notes: '',
      fiscal_data: {
        rfc: '',
        business_name: '',
        tax_regime: '',
      },
      payment_method_details: {
        card_last_four: '',
        digital_wallet_provider: '',
        bank_account_last_four: '',
      },
      stops: [],
      route_optimization: 'balanced',
      tracking_number: '',
      delivery_zone: '',
      delivery_region: 'local',
      terms_accepted: false,
    }
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);

  // Save form data to localStorage for persistence
  useEffect(() => {
    const savedData = localStorage.getItem('orderWizardData');
    if (savedData && !initialData) {
      try {
        const parsedData = JSON.parse(savedData);
        setFormData(parsedData);
      } catch (e) {
        console.error('Failed to parse saved form data', e);
      }
    }
  }, [initialData]);

  // Save form data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('orderWizardData', JSON.stringify(formData));
  }, [formData]);

  const totalSteps = 9;

  const nextStep = async () => {
    if (currentStep < totalSteps) {
      // Validate current step before proceeding
      const isValid = await validateStep(currentStep);
      if (isValid) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    // Validate all steps before submitting
    const isValid = await validateAllSteps();
    if (isValid) {
      onSubmit(formData);
      // Clear saved data after successful submission
      localStorage.removeItem('orderWizardData');
    }
  };

  const updateFormData = (data: Partial<OrderFormData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  // Validation functions
  const validateStep = async (step: number): Promise<boolean> => {
    setIsSaving(true);
    setErrors({});

    try {
      switch (step) {
        case 1:
          // Validate customer info
          OrderFormDataSchema.pick({
            customer_name: true,
            customer_phone: true,
            customer_email: true,
          }).parse(formData);
          break;
        case 2:
          // Validate delivery address
          OrderFormDataSchema.pick({
            delivery_address: true,
          }).parse(formData);
          break;
        case 3:
          // Validate products
          OrderFormDataSchema.pick({
            products: true,
          }).parse(formData);
          break;
        case 4:
          // Validate delivery options
          OrderFormDataSchema.pick({
            delivery_date: true,
            delivery_time_slot: true,
            delivery_mode: true,
          }).parse(formData);
          break;
        case 5:
          // Step 5: Delivery Management - No validation required
          // Users can select delivery mode without additional validation
          break;
        case 6:
          // Validate payment method and invoice requirements
          const paymentValidation = OrderFormDataSchema.pick({
            payment_method: true,
            invoice_required: true,
          });

          // Only validate fiscal data if invoice is required
          if (formData.invoice_required) {
            const paymentWithFiscalValidation = OrderFormDataSchema.pick({
              payment_method: true,
              invoice_required: true,
              fiscal_data: true,
            });
            paymentWithFiscalValidation.parse(formData);
          } else {
            paymentValidation.parse(formData);
          }
          break;
        case 7:
          // Validate additional notes
          OrderFormDataSchema.pick({
            special_instructions: true,
            allow_substitutions: true,
          }).parse(formData);
          break;
        case 8:
          // Validate Mexican logistics
          OrderFormDataSchema.pick({
            vehicle_type_id: true,
            cargo_type_id: true,
            route_optimization: true,
            delivery_region: true,
          }).parse(formData);
          break;
        case 9:
          // Validate terms acceptance
          OrderFormDataSchema.pick({
            terms_accepted: true,
          }).parse(formData);
          
          // Ensure terms are accepted
          if (!formData.terms_accepted) {
            throw new z.ZodError([
              {
                code: 'custom',
                message: 'Debes aceptar los términos y condiciones para continuar',
                path: ['terms_accepted'],
              },
            ]);
          }
          break;
      }
      setIsSaving(false);
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors: Record<string, string> = {};
        error.issues.forEach(issue => {
          if (issue.path) {
            fieldErrors[issue.path.join('.')] = issue.message;
          }
        });
        setErrors(fieldErrors);
      }
      setIsSaving(false);
      return false;
    }
  };

  const validateAllSteps = async (): Promise<boolean> => {
    setIsSaving(true);
    setErrors({});

    try {
      // Validate entire form
      OrderFormDataSchema.parse(formData);
      setIsSaving(false);
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors: Record<string, string> = {};
        error.issues.forEach(issue => {
          if (issue.path) {
            fieldErrors[issue.path.join('.')] = issue.message;
          }
        });
        setErrors(fieldErrors);
      }
      setIsSaving(false);
      return false;
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <OrderWizardStep1
            formData={formData}
            updateFormData={updateFormData}
            errors={errors}
          />
        );
      case 2:
        return (
          <OrderWizardStep2
            formData={formData}
            updateFormData={updateFormData}
            errors={errors}
          />
        );
      case 3:
        return (
          <OrderWizardStep3
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 4:
        return (
          <OrderWizardStep4
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 5:
        return (
          <OrderWizardStep5
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 6:
        return (
          <OrderWizardStep6
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 7:
        return (
          <OrderWizardStep7
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 8:
        return (
          <OrderWizardStep8
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 9:
        return (
          <OrderWizardStep9
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      default:
        return (
          <OrderWizardStep1
            formData={formData}
            updateFormData={updateFormData}
            errors={errors}
          />
        );
    }
  };

  return (
    <div className='max-w-4xl mx-auto p-6 space-y-6'>
      {/* Progress Bar */}
      <div className='mb-8'>
        <div className='flex justify-between items-center mb-2'>
          <h1 className='text-2xl font-bold text-gray-900'>
            Crear Nuevo Pedido - Paso {currentStep} de {totalSteps}
          </h1>
          <span className='text-sm text-gray-500'>
            {Math.round((currentStep / totalSteps) * 100)}% completado
          </span>
        </div>
        <div className='w-full bg-gray-200 rounded-full h-2.5'>
          <div
            className='bg-blue-600 h-2.5 rounded-full transition-all duration-300'
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          ></div>
        </div>
        {Object.keys(errors).length > 0 && (
          <div className='mt-2 text-sm text-red-600'>
            Por favor corrige los errores antes de continuar
          </div>
        )}
      </div>

      {/* Step Content */}
      <div className='space-y-6'>{renderStep()}</div>

      {/* Navigation Buttons */}
      <div className='flex justify-between pt-6'>
        <Button
          type='button'
          variant='outline'
          onClick={currentStep === 1 ? onCancel : prevStep}
          disabled={loading || isSaving}
        >
          {currentStep === 1 ? 'Cancelar' : 'Anterior'}
        </Button>

        {currentStep < totalSteps ? (
          <Button
            type='button'
            onClick={nextStep}
            disabled={loading || isSaving}
          >
            {isSaving ? (
              <div className='flex items-center gap-2'>
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
                Validando...
              </div>
            ) : (
              'Siguiente'
            )}
          </Button>
        ) : (
          <Button
            type='button'
            onClick={handleSubmit}
            disabled={loading || isSaving}
          >
            {loading || isSaving ? (
              <div className='flex items-center gap-2'>
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
                {loading ? 'Creando...' : 'Validando...'}
              </div>
            ) : (
              'Crear Pedido'
            )}
          </Button>
        )}
      </div>
    </div>
  );
}
