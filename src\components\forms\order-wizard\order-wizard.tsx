'use client';

import React from 'react';
import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { OrderFormData } from '@/types/order-form';
import { OrderWizardStep1 } from './step1-customer-info';
import { OrderWizardStep2 } from './step2-delivery-address';
import { OrderWizardStep3 } from './step3-products';
import { OrderWizardStep4 } from './step4-delivery-options';
import { OrderWizardStep5 } from './step5-payment';
import { OrderWizardStep6 } from './step6-additional-notes';
import { OrderWizardStep7 } from './step7-mexican-logistics';
import { OrderWizardStep8 } from './step8-review';

interface OrderWizardProps {
  onSubmit: (data: OrderFormData) => void;
  onCancel: () => void;
  loading?: boolean;
}

export function OrderWizard({
  onSubmit,
  onCancel,
  loading = false,
}: OrderWizardProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<OrderFormData>({
    // Customer Information
    customer_name: '',
    customer_phone: '',
    customer_email: '',

    // Delivery Address
    delivery_address: {
      id: '1',
      street: '',
      number: '',
      colony: '',
      city: '',
      state: '',
      zip: '',
      references: '',
    },

    // Shopping Cart
    products: [
      {
        id: '1',
        name: '',
        quantity: 1,
        unit_measure: 'kg',
        unit_price: 0,
        subtotal: 0,
        notes: '',
      },
    ],

    // Delivery Options
    delivery_date: '',
    delivery_time_slot: '',
    delivery_mode: 'home',

    // Payment
    payment_method: 'card',
    invoice_required: false,

    // Additional Notes
    special_instructions: '',
    allow_substitutions: false,

    // Mexican Logistics (new fields)
    vehicle_type_id: '',
    cargo_type_id: '',
    scheduled_pickup_time: '',
    scheduled_delivery_time: '',
    delivery_instructions: '',
    special_handling_notes: '',
    fiscal_data: {
      rfc: '',
      business_name: '',
      tax_regime: '',
    },
    payment_method_details: {
      card_last_four: '',
      digital_wallet_provider: '',
      bank_account_last_four: '',
    },
    stops: [],
    route_optimization: 'balanced',
    tracking_number: '',
    delivery_zone: '',
    delivery_region: 'local',
  });

  const totalSteps = 8;

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    onSubmit(formData);
  };

  const updateFormData = (data: Partial<OrderFormData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <OrderWizardStep1
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 2:
        return (
          <OrderWizardStep2
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 3:
        return (
          <OrderWizardStep3
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 4:
        return (
          <OrderWizardStep4
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 5:
        return (
          <OrderWizardStep5
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 6:
        return (
          <OrderWizardStep6
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 7:
        return (
          <OrderWizardStep7
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 8:
        return (
          <OrderWizardStep8
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      default:
        return (
          <OrderWizardStep1
            formData={formData}
            updateFormData={updateFormData}
          />
        );
    }
  };

  return (
    <div className='max-w-4xl mx-auto p-6 space-y-6'>
      {/* Progress Bar */}
      <div className='mb-8'>
        <div className='flex justify-between items-center mb-2'>
          <h1 className='text-2xl font-bold text-gray-900'>
            Crear Nuevo Pedido - Paso {currentStep} de {totalSteps}
          </h1>
          <span className='text-sm text-gray-500'>
            {Math.round((currentStep / totalSteps) * 100)}% completado
          </span>
        </div>
        <div className='w-full bg-gray-200 rounded-full h-2.5'>
          <div
            className='bg-blue-600 h-2.5 rounded-full transition-all duration-300'
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Step Content */}
      <div className='space-y-6'>{renderStep()}</div>

      {/* Navigation Buttons */}
      <div className='flex justify-between pt-6'>
        <Button
          type='button'
          variant='outline'
          onClick={currentStep === 1 ? onCancel : prevStep}
          disabled={loading}
        >
          {currentStep === 1 ? 'Cancelar' : 'Anterior'}
        </Button>

        {currentStep < totalSteps ? (
          <Button type='button' onClick={nextStep}>
            Siguiente
          </Button>
        ) : (
          <Button type='button' onClick={handleSubmit} disabled={loading}>
            {loading ? (
              <div className='flex items-center gap-2'>
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
                Creando...
              </div>
            ) : (
              'Crear Pedido'
            )}
          </Button>
        )}
      </div>
    </div>
  );
}
