'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Home, RotateCcw } from 'lucide-react';

export default function Error({
  error,
  reset,
}: {
  error: Error;
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <div className='min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8'>
      <div className='max-w-md w-full space-y-8 text-center'>
        <div>
          <h1 className='text-9xl font-bold text-gray-200'>500</h1>
          <h2 className='mt-4 text-3xl font-bold text-gray-900'>
            Algo salió mal
          </h2>
          <p className='mt-2 text-lg text-gray-600'>
            Lo sentimos, ocurrió un error inesperado. Nuestro equipo ha sido
            notificado.
          </p>
        </div>

        <div className='mt-8 flex flex-col sm:flex-row items-center justify-center gap-4'>
          <Button
            onClick={reset}
            variant='outline'
            className='flex items-center gap-2'
          >
            <RotateCcw className='w-4 h-4' />
            Intentar de nuevo
          </Button>
          <Button
            onClick={() => (window.location.href = '/')}
            className='flex items-center gap-2'
          >
            <Home className='w-4 h-4' />
            Ir al inicio
          </Button>
        </div>
      </div>
    </div>
  );
}
