import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { secureAPI } from '@/lib/security/api-security';
import { auditLog } from '@/lib/security/audit-logger';

// GET /api/cargo-types/category/[category] - Get cargo types by category
export const GET = secureAPI(
  {
    GET: async (request, context) => {
      try {
        // Extract category from URL parameters
        const url = new URL(request.url);
        const category = url.pathname.split('/').pop();

        if (!category) {
          return NextResponse.json(
            { error: 'Categoría no especificada' },
            { status: 400 }
          );
        }

        const supabase = await createClient();

        // Get cargo types by category
        const { data, error } = await supabase
          .from('cargo_types')
          .select('*')
          .eq('category', category)
          .order('name', { ascending: true });

        if (error) {
          console.error('Database error:', error);
          await auditLog({
            event: 'CARGO_TYPES_BY_CATEGORY_FETCH_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { error: error.message, category },
            severity: 'MEDIUM',
            category: 'SYSTEM',
          });
          return NextResponse.json(
            { error: 'Error al obtener los tipos de carga por categoría' },
            { status: 500 }
          );
        }

        await auditLog({
          event: 'CARGO_TYPES_BY_CATEGORY_FETCHED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { category, count: data?.length || 0 },
          severity: 'LOW',
          category: 'DATA',
        });

        return NextResponse.json({
          success: true,
          data,
          count: data?.length || 0,
        });
      } catch (error) {
        await auditLog({
          event: 'CARGO_TYPES_BY_CATEGORY_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: false,
    rateLimitRpm: 60,
  }
);
