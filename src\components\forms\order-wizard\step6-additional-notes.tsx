'use client';

import React from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { OrderFormData } from '@/types/order-form';

interface Step6Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
}

export function OrderWizardStep6({ formData, updateFormData }: Step6Props) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          📝 Notas Adicionales
        </CardTitle>
        <CardDescription>
          Instrucciones especiales y preferencias
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='space-y-2'>
          <Label htmlFor='special_instructions'>
            Instrucciones Especiales para el Repartidor
          </Label>
          <Textarea
            id='special_instructions'
            value={formData.special_instructions || ''}
            onChange={e =>
              updateFormData({
                special_instructions: e.target.value,
              })
            }
            placeholder='ej., llamar antes de llegar, dejar en recepción, no tocar timbre'
            rows={3}
          />
        </div>
        <div className='space-y-2'>
          <Label htmlFor='allow_substitutions'>
            Opción de Sustitución de Productos Agotados
          </Label>
          <div className='flex gap-4'>
            <label className='flex items-center space-x-2'>
              <input
                type='radio'
                name='allow_substitutions'
                value='true'
                checked={formData.allow_substitutions === true}
                onChange={e =>
                  updateFormData({
                    allow_substitutions: e.target.value === 'true',
                  })
                }
                className='text-blue-600'
              />
              <span>✅ Sí, elegir reemplazo</span>
            </label>
            <label className='flex items-center space-x-2'>
              <input
                type='radio'
                name='allow_substitutions'
                value='false'
                checked={formData.allow_substitutions === false}
                onChange={e =>
                  updateFormData({
                    allow_substitutions: e.target.value === 'true',
                  })
                }
                className='text-blue-600'
              />
              <span>❌ No, cancelar producto</span>
            </label>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
