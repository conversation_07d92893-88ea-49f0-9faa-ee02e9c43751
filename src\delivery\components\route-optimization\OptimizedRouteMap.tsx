'use client';

import { useEffect, useState, useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import { Location } from '@/src/delivery/hooks/useRouteOptimization';

// Fix for Leaflet icons in Next.js
if (typeof window !== 'undefined') {
  delete (L.Icon.Default.prototype as unknown as Record<string, unknown>)
    ._getIconUrl;
  L.Icon.Default.mergeOptions({
    iconRetinaUrl:
      'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
    iconUrl:
      'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
    shadowUrl:
      'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
  });
}

interface OptimizedRouteMapProps {
  locations: Location[];
  className?: string;
}

export default function OptimizedRouteMap({
  locations,
  className = 'h-96',
}: OptimizedRouteMapProps) {
  const [mapKey, setMapKey] = useState(0);

  // Generate a unique map ID for this instance
  const mapId = useMemo(
    () => `optimized-route-map-${Math.random().toString(36).substr(2, 9)}`,
    []
  );

  // Calculate center point
  const center: [number, number] = useMemo(() => {
    if (locations.length === 0) {
      return [23.6345, -102.5528]; // Center of Mexico
    }

    const avgLat =
      locations.reduce((sum, loc) => sum + loc.lat, 0) / locations.length;
    const avgLng =
      locations.reduce((sum, loc) => sum + loc.lng, 0) / locations.length;
    return [avgLat, avgLng];
  }, [locations]);

  // Create polyline points
  const polylinePoints: [number, number][] = useMemo(() => {
    return locations.map(loc => [loc.lat, loc.lng] as [number, number]);
  }, [locations]);

  // Force map to re-render when locations change
  useEffect(() => {
    setMapKey(prev => prev + 1);
  }, [locations]);

  if (typeof window === 'undefined') {
    return (
      <div className={`w-full ${className}`}>
        <div className='bg-gray-100 rounded-lg flex items-center justify-center h-full'>
          <p className='text-gray-600'>Cargando mapa...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      <MapContainer
        center={center}
        zoom={13}
        className='w-full h-full rounded-lg'
        key={`${mapId}-${mapKey}`}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url='https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
        />

        {/* Route polyline */}
        {polylinePoints.length > 1 && (
          <Polyline
            positions={polylinePoints}
            color='#3B82F6'
            weight={4}
            opacity={0.8}
          />
        )}

        {/* Location markers */}
        {locations.map((location, index) => (
          <Marker key={location.id} position={[location.lat, location.lng]}>
            <Popup>
              <div className='text-center'>
                <div className='font-semibold'>
                  {index === 0 ? '📍 Punto de Inicio' : `📍 Parada ${index}`}
                </div>
                <div className='text-sm text-gray-600'>{location.name}</div>
                <div className='text-xs text-gray-500 mt-1'>
                  {location.lat.toFixed(4)}, {location.lng.toFixed(4)}
                </div>
              </div>
            </Popup>
          </Marker>
        ))}
      </MapContainer>
    </div>
  );
}
