import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import {
  secureAPI,
  sanitizeInput,
  isValidUUID,
} from '@/lib/security/api-security';
import { auditLog } from '@/lib/security/audit-logger';

// GET /api/vehicle-types/[id] - Get a specific vehicle type by ID
export const GET = secureAPI(
  {
    GET: async (request, context) => {
      try {
        // Extract ID from URL parameters
        const url = new URL(request.url);
        const id = url.pathname.split('/').pop();

        // Validate UUID format
        if (!id || !isValidUUID(id)) {
          return NextResponse.json(
            { error: 'ID de tipo de vehículo inválido' },
            { status: 400 }
          );
        }

        const supabase = await createClient();

        // Get specific vehicle type by ID
        const { data, error } = await supabase
          .from('vehicle_types')
          .select('*')
          .eq('id', id)
          .single();

        if (error) {
          console.error('Database error:', error);
          await auditLog({
            event: 'VEHICLE_TYPE_FETCH_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { error: error.message, vehicleTypeId: id },
            severity: 'MEDIUM',
            category: 'SYSTEM',
          });

          if (error.code === 'PGRST116') {
            return NextResponse.json(
              { error: 'Tipo de vehículo no encontrado' },
              { status: 404 }
            );
          }

          return NextResponse.json(
            { error: 'Error al obtener el tipo de vehículo' },
            { status: 500 }
          );
        }

        if (!data) {
          return NextResponse.json(
            { error: 'Tipo de vehículo no encontrado' },
            { status: 404 }
          );
        }

        await auditLog({
          event: 'VEHICLE_TYPE_FETCHED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { vehicleTypeId: id },
          severity: 'LOW',
          category: 'DATA',
        });

        return NextResponse.json({
          success: true,
          data,
        });
      } catch (error) {
        await auditLog({
          event: 'VEHICLE_TYPE_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: false,
    rateLimitRpm: 60,
  }
);

// PUT /api/vehicle-types/[id] - Update a specific vehicle type (admin only)
export const PUT = secureAPI(
  {
    PUT: async (request, context) => {
      // Check if user is admin
      if (context.user?.role !== 'admin') {
        await auditLog({
          event: 'UNAUTHORIZED_VEHICLE_TYPE_UPDATE_ATTEMPT',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { userRole: context.user?.role },
          severity: 'HIGH',
          category: 'SECURITY',
        });

        return NextResponse.json(
          {
            error:
              'Solo los administradores pueden actualizar tipos de vehículos',
          },
          { status: 403 }
        );
      }

      try {
        // Extract ID from URL parameters
        const url = new URL(request.url);
        const id = url.pathname.split('/').slice(-2)[0];

        // Validate UUID format
        if (!id || !isValidUUID(id)) {
          return NextResponse.json(
            { error: 'ID de tipo de vehículo inválido' },
            { status: 400 }
          );
        }

        // Parse and sanitize request body
        let requestBody;
        try {
          requestBody = sanitizeInput(await request.json());
        } catch {
          return NextResponse.json(
            { error: 'Formato de cuerpo de solicitud inválido' },
            { status: 400 }
          );
        }

        const {
          name,
          category,
          max_weight_kg,
          max_volume_m3,
          base_rate_per_km,
          special_capabilities,
          description,
        } = requestBody as {
          name?: string;
          category?: string;
          max_weight_kg?: number;
          max_volume_m3?: number;
          base_rate_per_km?: number;
          special_capabilities?: string[];
          description?: string;
        };

        const supabase = await createClient();

        // Update vehicle type
        const { data, error } = await supabase
          .from('vehicle_types')
          .update({
            name,
            category,
            max_weight_kg,
            max_volume_m3,
            base_rate_per_km,
            special_capabilities,
            description,
            updated_at: new Date().toISOString(),
          })
          .eq('id', id)
          .select()
          .single();

        if (error) {
          console.error('Database error:', error);
          await auditLog({
            event: 'VEHICLE_TYPE_UPDATE_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: {
              error: error.message,
              vehicleTypeId: id,
              requestData: requestBody,
            },
            severity: 'MEDIUM',
            category: 'DATA',
          });

          if (error.code === 'PGRST116') {
            return NextResponse.json(
              { error: 'Tipo de vehículo no encontrado' },
              { status: 404 }
            );
          }

          return NextResponse.json(
            { error: 'Error al actualizar el tipo de vehículo' },
            { status: 500 }
          );
        }

        if (!data) {
          return NextResponse.json(
            { error: 'Tipo de vehículo no encontrado' },
            { status: 404 }
          );
        }

        await auditLog({
          event: 'VEHICLE_TYPE_UPDATED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { vehicleTypeId: id, name: data.name },
          severity: 'MEDIUM',
          category: 'DATA',
        });

        return NextResponse.json({
          success: true,
          message: 'Tipo de vehículo actualizado exitosamente',
          data,
        });
      } catch (error) {
        await auditLog({
          event: 'VEHICLE_TYPE_UPDATE_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: true,
    allowedRoles: ['admin'],
    requireMFA: false,
    validateCSRF: true,
    rateLimitRpm: 30,
  }
);

// DELETE /api/vehicle-types/[id] - Delete a specific vehicle type (admin only)
export const DELETE = secureAPI(
  {
    DELETE: async (request, context) => {
      // Check if user is admin
      if (context.user?.role !== 'admin') {
        await auditLog({
          event: 'UNAUTHORIZED_VEHICLE_TYPE_DELETE_ATTEMPT',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { userRole: context.user?.role },
          severity: 'HIGH',
          category: 'SECURITY',
        });

        return NextResponse.json(
          {
            error:
              'Solo los administradores pueden eliminar tipos de vehículos',
          },
          { status: 403 }
        );
      }

      try {
        // Extract ID from URL parameters
        const url = new URL(request.url);
        const id = url.pathname.split('/').slice(-2)[0];

        // Validate UUID format
        if (!id || !isValidUUID(id)) {
          return NextResponse.json(
            { error: 'ID de tipo de vehículo inválido' },
            { status: 400 }
          );
        }

        const supabase = await createClient();

        // Check if vehicle type exists
        const { data: existingData, error: fetchError } = await supabase
          .from('vehicle_types')
          .select('name')
          .eq('id', id)
          .single();

        if (fetchError) {
          console.error('Database error:', fetchError);
          await auditLog({
            event: 'VEHICLE_TYPE_DELETE_CHECK_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { error: fetchError.message, vehicleTypeId: id },
            severity: 'MEDIUM',
            category: 'DATA',
          });

          if (fetchError.code === 'PGRST116') {
            return NextResponse.json(
              { error: 'Tipo de vehículo no encontrado' },
              { status: 404 }
            );
          }

          return NextResponse.json(
            { error: 'Error al verificar el tipo de vehículo' },
            { status: 500 }
          );
        }

        if (!existingData) {
          return NextResponse.json(
            { error: 'Tipo de vehículo no encontrado' },
            { status: 404 }
          );
        }

        // Delete vehicle type
        const { error } = await supabase
          .from('vehicle_types')
          .delete()
          .eq('id', id);

        if (error) {
          console.error('Database error:', error);
          await auditLog({
            event: 'VEHICLE_TYPE_DELETE_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: {
              error: error.message,
              vehicleTypeId: id,
              name: existingData.name,
            },
            severity: 'MEDIUM',
            category: 'DATA',
          });
          return NextResponse.json(
            { error: 'Error al eliminar el tipo de vehículo' },
            { status: 500 }
          );
        }

        await auditLog({
          event: 'VEHICLE_TYPE_DELETED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: { vehicleTypeId: id, name: existingData.name },
          severity: 'MEDIUM',
          category: 'DATA',
        });

        return NextResponse.json({
          success: true,
          message: 'Tipo de vehículo eliminado exitosamente',
        });
      } catch (error) {
        await auditLog({
          event: 'VEHICLE_TYPE_DELETE_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: true,
    allowedRoles: ['admin'],
    requireMFA: false,
    validateCSRF: true,
    rateLimitRpm: 30,
  }
);
