/**
 * Zero Trust Security Implementation
 *
 * This module implements Zero Trust security principles:
 * 1. Never Trust, Always Verify
 * 2. Least Privilege Access
 * 3. Continuous Verification
 * 4. Comprehensive Logging
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/lib/supabase';
import { rateLimit } from './rate-limiter';

import { auditLog } from './audit-logger';

export interface SecurityContext {
  user: {
    id: string;
    email: string;
    role: 'admin' | 'customer' | 'delivery';
    lastVerified: Date;
    mfaEnabled: boolean;
    sessionId: string;
  } | null;
  request: {
    ip: string;
    userAgent: string;
    path: string;
    method: string;
    timestamp: Date;
  };
  security: {
    riskScore: number;
    requiresMFA: boolean;
    isPrivilegedOperation: boolean;
    rateLimitRemaining: number;
  };
}

export interface ZeroTrustConfig {
  requireMFA?: boolean;
  maxRiskScore?: number;
  rateLimitRpm?: number;
  privilegedPaths?: string[];
  allowedOrigins?: string[];
  requireCSRF?: boolean;
}

/**
 * Zero Trust Security Middleware
 * Implements continuous verification and least privilege access
 */
export async function zeroTrustMiddleware(
  request: NextRequest,
  config: ZeroTrustConfig = {}
): Promise<{ context: SecurityContext; response?: NextResponse }> {
  const startTime = Date.now();

  try {
    // 1. Extract request metadata
    const requestMetadata = {
      ip: getClientIP(request),
      userAgent: request.headers.get('user-agent') || 'unknown',
      path: request.nextUrl.pathname,
      method: request.method,
      timestamp: new Date(),
    };

    // 2. Rate limiting (Never Trust principle)
    const rateLimitResult = await rateLimit(
      requestMetadata.ip,
      config.rateLimitRpm || 60
    );

    if (!rateLimitResult.allowed) {
      await auditLog({
        event: 'RATE_LIMIT_EXCEEDED',
        ip: requestMetadata.ip,
        path: requestMetadata.path,
        details: { limit: config.rateLimitRpm, remaining: 0 },
      });

      return {
        context: createSecurityContext(null, requestMetadata, {
          riskScore: 100,
        }),
        response: NextResponse.json(
          { error: 'Rate limit exceeded' },
          { status: 429, headers: { 'Retry-After': '60' } }
        ),
      };
    }

    // 3. Request validation (simplified - basic checks only)
    // Note: Advanced request validation was removed with request-validator.ts

    // 4. Authentication verification (Always Verify principle)
    const authResult = await verifyAuthentication(request);

    // 5. Calculate risk score
    const riskScore = calculateRiskScore(requestMetadata, authResult.user);

    // 6. Check if operation requires MFA
    const isPrivilegedOperation = isPrivilegedPath(
      requestMetadata.path,
      config.privilegedPaths
    );

    const requiresMFA =
      config.requireMFA ||
      isPrivilegedOperation ||
      riskScore > (config.maxRiskScore || 70);

    // 7. MFA verification for high-risk operations
    if (requiresMFA && authResult.user && !authResult.user.mfaEnabled) {
      await auditLog({
        event: 'MFA_REQUIRED',
        userId: authResult.user.id,
        ip: requestMetadata.ip,
        path: requestMetadata.path,
        details: { riskScore, isPrivilegedOperation },
      });

      return {
        context: createSecurityContext(authResult.user, requestMetadata, {
          riskScore,
          requiresMFA: true,
        }),
        response: NextResponse.json(
          { error: 'Multi-factor authentication required' },
          { status: 403 }
        ),
      };
    }

    // 8. CSRF protection for state-changing operations
    if (
      config.requireCSRF &&
      ['POST', 'PUT', 'DELETE', 'PATCH'].includes(request.method)
    ) {
      const csrfValid = await validateCSRF(request);
      if (!csrfValid) {
        await auditLog({
          event: 'CSRF_VALIDATION_FAILED',
          userId: authResult.user?.id,
          ip: requestMetadata.ip,
          path: requestMetadata.path,
        });

        return {
          context: createSecurityContext(authResult.user, requestMetadata, {
            riskScore: 90,
          }),
          response: NextResponse.json(
            { error: 'CSRF validation failed' },
            { status: 403 }
          ),
        };
      }
    }

    // 9. Create security context
    const securityContext = createSecurityContext(
      authResult.user,
      requestMetadata,
      {
        riskScore,
        requiresMFA,
        isPrivilegedOperation,
        rateLimitRemaining: rateLimitResult.remaining,
      }
    );

    // 10. Audit successful request
    await auditLog({
      event: 'REQUEST_AUTHORIZED',
      userId: authResult.user?.id,
      ip: requestMetadata.ip,
      path: requestMetadata.path,
      details: {
        riskScore,
        processingTime: Date.now() - startTime,
        rateLimitRemaining: rateLimitResult.remaining,
      },
    });

    return { context: securityContext };
  } catch (error) {
    console.error('Zero Trust middleware error:', error);

    await auditLog({
      event: 'SECURITY_MIDDLEWARE_ERROR',
      ip: getClientIP(request),
      path: request.nextUrl.pathname,
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
      },
    });

    return {
      context: createSecurityContext(
        null,
        {
          ip: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown',
          path: request.nextUrl.pathname,
          method: request.method,
          timestamp: new Date(),
        },
        { riskScore: 100 }
      ),
      response: NextResponse.json(
        { error: 'Security validation failed' },
        { status: 500 }
      ),
    };
  }
}

/**
 * Verify user authentication with continuous validation
 * Uses Supabase's recommended JWT validation approach
 */
async function verifyAuthentication(request: NextRequest) {
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll() {
          // Read-only in middleware
        },
      },
    }
  );

  try {
    // Use getClaims() for JWT validation (Supabase recommended approach)
    const { data: claims, error: claimsError } =
      await supabase.auth.getClaims();

    if (claimsError || !claims?.claims) {
      return { user: null };
    }

    const jwtClaims = claims.claims;

    // Get additional user data if needed
    const { data: userData } = await supabase.auth.getUser();
    const user = userData.user;

    if (user && jwtClaims) {
      // Try to get role from JWT claims first (more efficient)
      let userRole = jwtClaims.role || jwtClaims.app_metadata?.role;

      // Fallback to database query if role not in JWT
      if (!userRole) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();

        userRole = profile?.role;
      }

      return {
        user: {
          id: user.id,
          email: user.email!,
          role: userRole as 'admin' | 'customer' | 'delivery',
          lastVerified: new Date(),
          mfaEnabled: false, // TODO: Implement MFA tracking
          sessionId: jwtClaims.sub, // Use JWT subject as session ID
          jwtClaims, // Include JWT claims for additional validation
        },
      };
    }

    return { user: null };
  } catch (error) {
    console.error('Authentication verification failed:', error);
    return { user: null };
  }
}

/**
 * Calculate risk score based on request context and user behavior
 */
function calculateRiskScore(
  request: { ip: string; userAgent: string; path: string; method: string },
  user: SecurityContext['user']
): number {
  let score = 0;

  // Base score for unauthenticated requests
  if (!user) score += 30;

  // High-risk paths
  const highRiskPaths = ['/admin', '/api/orders/update-status', '/api/users'];
  if (highRiskPaths.some(path => request.path.startsWith(path))) {
    score += 25;
  }

  // State-changing operations
  if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(request.method)) {
    score += 15;
  }

  // Missing or suspicious user agent
  if (!request.userAgent || request.userAgent === 'unknown') {
    score += 20;
  }

  // TODO: Add more sophisticated risk factors:
  // - Geolocation anomalies
  // - Time-based patterns
  // - Device fingerprinting
  // - Behavioral analysis

  return Math.min(score, 100);
}

/**
 * Check if path requires privileged access
 */
function isPrivilegedPath(path: string, customPaths?: string[]): boolean {
  const defaultPrivilegedPaths = [
    '/admin',
    '/api/orders/assign-driver',
    '/api/orders/update-status',
    '/api/users',
    '/api/admin',
  ];

  const privilegedPaths = [...defaultPrivilegedPaths, ...(customPaths || [])];
  return privilegedPaths.some(privilegedPath =>
    path.startsWith(privilegedPath)
  );
}

/**
 * Validate CSRF token
 */
async function validateCSRF(request: NextRequest): Promise<boolean> {
  // TODO: Implement proper CSRF validation
  // For now, check for custom header that indicates same-origin request
  const csrfHeader =
    request.headers.get('x-csrf-token') ||
    request.headers.get('x-requested-with');

  return !!csrfHeader;
}

/**
 * Get client IP address
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  return 'unknown';
}

/**
 * Create security context object
 */
function createSecurityContext(
  user: SecurityContext['user'],
  request: SecurityContext['request'],
  security: Partial<SecurityContext['security']>
): SecurityContext {
  return {
    user,
    request,
    security: {
      riskScore: security.riskScore || 0,
      requiresMFA: security.requiresMFA || false,
      isPrivilegedOperation: security.isPrivilegedOperation || false,
      rateLimitRemaining: security.rateLimitRemaining || 0,
    },
  };
}
