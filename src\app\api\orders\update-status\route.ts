import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { ORDER_STATUSES, isValidStatus } from '@/lib/order-status';
import {
  secureAPI,
  sanitizeInput,
  isValidUUID,
} from '@/lib/security/api-security';
import { auditLog } from '@/lib/security/audit-logger';

// Export secure API handlers
export const GET = secureAPI(
  {
    GET: async (request, context) => {
      return NextResponse.json({
        message: 'Order status API endpoint is working',
        timestamp: new Date().toISOString(),
        security: {
          riskScore: context.security.riskScore,
          rateLimitRemaining: context.security.rateLimitRemaining,
        },
        env: {
          hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
          hasSupabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        },
      });
    },
  },
  {
    requireAuth: false,
    rateLimitRpm: 30,
  }
);

export const PUT = secureAPI(
  {
    PUT: async (request, context) => {
      // Initialize user context (guaranteed to exist due to requireAuth: true)
      const user = context.user!;

      // Parse and sanitize request body
      let requestBody;
      try {
        requestBody = sanitizeInput(await request.json());
      } catch {
        return NextResponse.json(
          { error: 'Formato de cuerpo de solicitud inválido' },
          { status: 400 }
        );
      }

      try {
        // Initialize Supabase client (authentication handled by secureAPI)
        const supabase = await createClient();

        // User role is available from security context
        const userRole = user.role;

        const { orderId, newStatus } = requestBody as {
          orderId?: string;
          newStatus?: string;
        };

        if (!orderId || !newStatus) {
          return NextResponse.json(
            { error: 'Se requiere el ID de la orden y el nuevo estado' },
            { status: 400 }
          );
        }

        // Validate UUID format for orderId
        if (!isValidUUID(orderId)) {
          return NextResponse.json(
            { error: 'Formato de ID de orden inválido' },
            { status: 400 }
          );
        }

        // Validate status values using centralized configuration
        if (!isValidStatus(newStatus)) {
          return NextResponse.json(
            {
              error: 'Valor de estado inválido',
              details: `Estados válidos: ${ORDER_STATUSES.join(', ')}`,
              providedStatus: newStatus,
            },
            { status: 400 }
          );
        }

        // Only admins can close orders
        if (newStatus === 'closed' && userRole !== 'admin') {
          await auditLog({
            event: 'UNAUTHORIZED_ORDER_CLOSURE_ATTEMPT',
            userId: user.id,
            ip: context.request.ip,
            details: { orderId, userRole },
            severity: 'HIGH',
            category: 'SECURITY',
          });

          return NextResponse.json(
            { error: 'Solo los administradores pueden cerrar órdenes' },
            { status: 403 }
          );
        }

        // If delivery personnel is trying to mark as delivered, change to pending-admin-confirmation
        let finalStatus = newStatus;
        if (userRole === 'delivery' && newStatus === 'delivered') {
          finalStatus = 'pending-admin-confirmation';
        }

        // If user is delivery personnel, check if they are assigned to this order
        if (userRole === 'delivery') {
          const { data: order, error: orderError } = await supabase
            .from('orders')
            .select('delivery_id, status')
            .eq('id', orderId)
            .single();

          if (orderError || !order) {
            return NextResponse.json(
              { error: 'Orden no encontrada' },
              { status: 404 }
            );
          }

          if (order.delivery_id !== user.id) {
            return NextResponse.json(
              { error: 'Solo puedes actualizar órdenes asignadas a ti' },
              { status: 403 }
            );
          }

          // Security: Prevent delivery personnel from modifying admin-confirmed orders
          if (order.status === 'confirmed' || order.status === 'in-transit') {
            // Only allow delivery personnel to mark as delivered (which becomes pending-admin-confirmation)
            if (newStatus !== 'pending-admin-confirmation') {
              return NextResponse.json(
                {
                  error:
                    'Por seguridad, no puedes modificar órdenes confirmadas por administración. Solo puedes marcarlas como entregadas.',
                  currentStatus: order.status,
                  allowedStatus: 'pending-admin-confirmation',
                },
                { status: 403 }
              );
            }
          }
        }

        // Additional validation for closing orders
        if (finalStatus === 'closed') {
          const { data: orderToClose, error: orderError } = await supabase
            .from('orders')
            .select('status')
            .eq('id', orderId)
            .single();

          if (orderError || !orderToClose) {
            return NextResponse.json(
              { error: 'Orden no encontrada' },
              { status: 404 }
            );
          }

          if (orderToClose.status !== 'delivered') {
            return NextResponse.json(
              {
                error: 'Solo se pueden cerrar órdenes que han sido entregadas',
                currentStatus: orderToClose.status,
                requiredStatus: 'delivered',
              },
              { status: 400 }
            );
          }
        }

        // Update the order status
        const { data: updateData, error: updateError } = await supabase
          .from('orders')
          .update({
            status: finalStatus,
            updated_at: new Date().toISOString(),
          })
          .eq('id', orderId)
          .select();

        if (updateError) {
          console.error('Database update error:', updateError);

          // Handle constraint violation specifically
          if (updateError.code === '23514') {
            // Check constraint violation
            return NextResponse.json(
              {
                error: 'Valor de estado no válido para la base de datos',
                details: `El estado '${finalStatus}' no está permitido. Estados válidos: ${ORDER_STATUSES.join(', ')}`,
                code: updateError.code,
                hint: 'Verifique que el estado sea uno de los valores permitidos',
                constraintError: true,
                providedStatus: finalStatus,
                validStatuses: ORDER_STATUSES,
              },
              { status: 400 }
            );
          }

          return NextResponse.json(
            {
              error: 'Error al actualizar el estado de la orden',
              details: updateError.message,
              code: updateError.code,
              hint: updateError.hint,
            },
            { status: 500 }
          );
        }

        // Check if any rows were actually updated
        if (!updateData || updateData.length === 0) {
          return NextResponse.json(
            { error: 'No se encontró la orden para actualizar' },
            { status: 404 }
          );
        }

        // Log successful order status update
        await auditLog({
          event: 'ORDER_STATUS_UPDATED',
          userId: user.id,
          ip: context.request.ip,
          details: {
            orderId,
            oldStatus: 'unknown', // Could be enhanced to track old status
            newStatus: finalStatus,
            userRole,
          },
          severity: 'MEDIUM',
          category: 'DATA',
        });

        return NextResponse.json({
          success: true,
          message: `Estado de la orden actualizado a ${finalStatus}`,
          orderId,
          newStatus: finalStatus,
        });
      } catch (error) {
        await auditLog({
          event: 'ORDER_STATUS_UPDATE_ERROR',
          userId: user.id,
          ip: context.request.ip,
          details: {
            orderId: (requestBody as { orderId?: string })?.orderId,
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: true,
    allowedRoles: ['admin', 'delivery'],
    requireMFA: false,
    validateCSRF: true,
    rateLimitRpm: 30,
    customValidation: async context => {
      // Additional validation for high-risk operations
      if (context.security.riskScore > 80) {
        return {
          valid: false,
          error: 'Request blocked due to high risk score',
        };
      }
      return { valid: true };
    },
  }
);
