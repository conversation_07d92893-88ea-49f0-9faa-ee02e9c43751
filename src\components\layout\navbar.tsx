'use client';

import Link from 'next/link';
import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/ui/button';
import { LogoutButton } from '@/components/auth/logout-button';
import { Truck } from 'lucide-react';

export function Navbar() {
  const { user, loading } = useAuthStore();

  return (
    <nav className='bg-white shadow-sm border-b'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='flex justify-between items-center h-16'>
          <div className='flex items-center'>
            <Link
              href={user ? '/dashboard' : '/'}
              className='text-xl font-bold text-black'
            >
              Mouvers
            </Link>
          </div>

          <div className='flex items-center space-x-4'>
            {loading ? (
              <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-black'></div>
            ) : user ? (
              <>
                <Link href='/delivery'>
                  <Button
                    variant='ghost'
                    className='flex items-center space-x-2'
                  >
                    <Truck className='h-4 w-4' />
                    <span>Delivery</span>
                  </Button>
                </Link>
                <span className='text-sm text-gray-600'>{user.email}</span>
                <LogoutButton className='text-sm'>Sign Out</LogoutButton>
              </>
            ) : (
              <>
                <Link href='/auth/sign-up'>
                  <Button variant='ghost'>Sign Up</Button>
                </Link>
                <Link href='/auth/login'>
                  <Button>Sign In</Button>
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
